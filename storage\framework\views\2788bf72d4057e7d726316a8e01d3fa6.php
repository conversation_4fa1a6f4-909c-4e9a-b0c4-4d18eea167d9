
    <!-- Brand container starts -->
    <div class="brand-container d-flex align-items-center justify-content-between">

    <!-- App brand starts -->
    <div class="app-brand ms-3">
        <a href="<?php echo e(route('dashboard')); ?>">
            <img src="<?php echo e(asset('assets/images/logo.svg')); ?>" class="logo" alt="Property Management System">
        </a>
    </div>
    <!-- App brand ends -->

    <!-- Pin sidebar starts -->
    <button type="button" class="pin-sidebar me-3">
        <i class="ri-menu-line"></i>
    </button>
    <!-- Pin sidebar ends -->

    </div>
    <!-- Brand container ends -->

    <!-- Sidebar profile starts -->
    <div class="sidebar-profile">
    <?php if(auth()->guard()->check()): ?>
        <div class="profile-photo-container position-relative" style="width: 80px; height: 80px; margin: 0 auto 10px;">
            <img src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                 class="rounded-5 border border-primary border-3"
                 alt="<?php echo e(auth()->user()->name); ?>"
                 style="width: 100%; height: 100%; object-fit: cover;">

            <!-- Profile Photo Hover Overlay -->
            <div class="profile-photo-overlay position-absolute top-0 start-0 w-100 h-100 rounded-5 d-flex align-items-center justify-content-center"
                 style="background: rgba(0,123,255,0.8); opacity: 0; transition: opacity 0.3s; cursor: pointer;"
                 onmouseover="this.style.opacity='1'"
                 onmouseout="this.style.opacity='0'"
                 onclick="showProfilePhotoModal()">
                <i class="ri-camera-line text-white fs-4"></i>
            </div>
        </div>

        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary"><?php echo e(auth()->user()->name); ?></h6>
        <small class="profile-name text-nowrap text-truncate">
            <?php echo e(ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User'))); ?>

        </small>
        <?php if(auth()->user()->hasProfilePhoto()): ?>
            <div class="mt-1">
                <small class="text-success">
                    <i class="ri-camera-line me-1"></i>Custom Photo
                </small>
            </div>
        <?php else: ?>
            <div class="mt-1">
                <small class="text-muted">
                    <i class="ri-user-line me-1"></i>Default Avatar
                </small>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <img src="<?php echo e(asset('assets/images/doctor5.png')); ?>" class="rounded-5 border border-primary border-3"
            alt="Property Management System">
        <h6 class="mb-1 profile-name text-nowrap text-truncate text-primary">Guest</h6>
        <small class="profile-name text-nowrap text-truncate">Guest User</small>
    <?php endif; ?>
    </div>
    <!-- Sidebar profile ends -->

    <!-- Sidebar menu starts -->
    <div class="sidebarMenuScroll">
    <ul class="sidebar-menu">
        <li class="<?php echo e(request()->routeIs('dashboard') ? 'active current-page' : ''); ?>">
        <a href="<?php echo e(route('dashboard')); ?>">
            <i class="ri-home-6-line"></i>
            <span class="menu-text">Dashboard</span>
        </a>
        </li>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_properties')): ?>
        <li class="treeview <?php echo e(request()->routeIs('properties.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-building-line"></i>
            <span class="menu-text">Properties</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('properties.index')); ?>">All Properties</a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Property::class)): ?>
            <li>
            <a href="<?php echo e(route('properties.create')); ?>">Add Property</a>
            </li>
            <?php endif; ?>
            <li>
            <a href="<?php echo e(route('reports.property')); ?>">Property Reports</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_own_properties')): ?>
        <?php if(!auth()->user()->can('view_properties')): ?>
        <li class="treeview <?php echo e(request()->routeIs('properties.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-building-line"></i>
            <span class="menu-text">My Properties</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('properties.index')); ?>">All Properties</a>
            </li>
            <li>
            <a href="<?php echo e(route('properties.create')); ?>">Add Property</a>
            </li>
            <li>
            <a href="<?php echo e(route('reports.property')); ?>">Property Reports</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_units')): ?>
        <li class="treeview <?php echo e(request()->routeIs('units.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-home-line"></i>
            <span class="menu-text">Units</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('units.index')); ?>">All Units</a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Unit::class)): ?>
            <li>
            <a href="<?php echo e(route('units.create')); ?>">Add Unit</a>
            </li>
            <?php endif; ?>
            <li>
            <a href="<?php echo e(route('units.index', ['status' => 'available'])); ?>">Available Units</a>
            </li>
            <li>
            <a href="<?php echo e(route('units.index', ['status' => 'occupied'])); ?>">Occupied Units</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_own_properties')): ?>
        <?php if(!auth()->user()->can('view_units')): ?>
        <li class="treeview <?php echo e(request()->routeIs('units.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-home-line"></i>
            <span class="menu-text">My Units</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('units.index')); ?>">All Units</a>
            </li>
            <li>
            <a href="<?php echo e(route('units.create')); ?>">Add Unit</a>
            </li>
            <li>
            <a href="<?php echo e(route('units.index', ['status' => 'available'])); ?>">Available Units</a>
            </li>
            <li>
            <a href="<?php echo e(route('units.index', ['status' => 'occupied'])); ?>">Occupied Units</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>
        <?php endif; ?>

        <!-- Billing & Payments -->
        <li class="treeview <?php echo e(request()->routeIs('bills.*', 'payments.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-money-dollar-circle-line"></i>
            <span class="menu-text">Billing & Payments</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('bills.index')); ?>">All Bills</a>
            </li>
            <li>
            <a href="<?php echo e(route('bills.create')); ?>">Create Bill</a>
            </li>
            <li>
            <a href="<?php echo e(route('payments.index')); ?>">All Payments</a>
            </li>
            <li>
            <a href="<?php echo e(route('payments.create')); ?>">Record Payment</a>
            </li>
        </ul>
        </li>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_complaints')): ?>
        <li class="treeview <?php echo e(request()->routeIs('complaints.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-customer-service-line"></i>
            <span class="menu-text">Complaints</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('complaints.index')); ?>">All Complaints</a>
            </li>
            <li>
            <a href="<?php echo e(route('complaints.create')); ?>">New Complaint</a>
            </li>
            <li>
            <a href="<?php echo e(route('complaints.index', ['status' => 'open'])); ?>">Open Complaints</a>
            </li>
            <li>
            <a href="<?php echo e(route('complaints.index', ['status' => 'resolved'])); ?>">Resolved Complaints</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>

        <!-- Task & Notification System -->
        <li class="treeview <?php echo e(request()->routeIs('tasks.*', 'notifications.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-task-line"></i>
            <span class="menu-text">Tasks & Notifications</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('tasks.index')); ?>">All Tasks</a>
            </li>
            <li>
            <a href="<?php echo e(route('tasks.create')); ?>">Create Task</a>
            </li>
            <li>
            <a href="<?php echo e(route('tasks.dashboard')); ?>">Task Dashboard</a>
            </li>
            <li>
            <a href="<?php echo e(route('notifications.index')); ?>">Notifications</a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Notification::class)): ?>
            <li>
            <a href="<?php echo e(route('notifications.create')); ?>">Send Notification</a>
            </li>
            <?php endif; ?>
        </ul>
        </li>

        <!-- Document Management -->
        <li class="treeview <?php echo e(request()->routeIs('documents.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-file-text-line"></i>
            <span class="menu-text">Documents</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('documents.index')); ?>">All Documents</a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Document::class)): ?>
            <li>
            <a href="<?php echo e(route('documents.create')); ?>">Upload Document</a>
            </li>
            <?php endif; ?>
            <li>
            <a href="<?php echo e(route('documents.expiring')); ?>">Expiring Documents</a>
            </li>
            <li>
            <a href="<?php echo e(route('documents.index', ['status' => 'expired'])); ?>">Expired Documents</a>
            </li>
        </ul>
        </li>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_users')): ?>
        <li class="treeview <?php echo e(request()->routeIs('admin.users.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-group-2-line"></i>
            <span class="menu-text">Users</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('admin.users.create')); ?>">New User</a>
            </li>
            <li>
            <a href="<?php echo e(route('admin.users.index')); ?>">List Users</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_roles')): ?>
        <li class="treeview <?php echo e(request()->routeIs('admin.roles.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-shield-user-line"></i>
            <span class="menu-text">Roles & Permissions</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('admin.roles.create')); ?>">New Role</a>
            </li>
            <li>
            <a href="<?php echo e(route('admin.roles.index')); ?>">List Roles</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>

        <!-- Reports Section -->
        <li class="treeview <?php echo e(request()->routeIs('reports.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-bar-chart-line"></i>
            <span class="menu-text">Reports & Analytics</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('reports.property')); ?>">Property Reports</a>
            </li>
            <li>
            <a href="<?php echo e(route('reports.financial')); ?>">Financial Reports</a>
            </li>
            <li>
            <a href="<?php echo e(route('reports.property')); ?>">Occupancy Analytics</a>
            </li>
            <li>
            <a href="<?php echo e(route('reports.financial')); ?>">Revenue Analysis</a>
            </li>
        </ul>
        </li>

        <!-- Visitor Management -->
        <li class="treeview <?php echo e(request()->routeIs('visitors.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-user-add-line"></i>
            <span class="menu-text">Visitor Management</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="<?php echo e(route('visitors.create')); ?>">Check-in Visitor</a>
            </li>
            <li>
            <a href="<?php echo e(route('visitors.index')); ?>">Visitor History</a>
            </li>
            <li>
            <a href="<?php echo e(route('visitors.dashboard')); ?>">Visitor Dashboard</a>
            </li>
            <li>
            <a href="<?php echo e(route('visitors.index', ['status' => 'checked_in'])); ?>">Currently Checked In</a>
            </li>
        </ul>
        </li>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-system')): ?>
        <li class="treeview <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?>">
        <a href="#!">
            <i class="ri-settings-line"></i>
            <span class="menu-text">System Settings</span>
        </a>
        <ul class="treeview-menu">
            <li>
            <a href="/settings">General Settings</a>
            </li>
            <li>
            <a href="/settings#email">Email Configuration</a>
            </li>
            <li>
            <a href="/settings#backup">Backup & Restore</a>
            </li>
            <li>
            <a href="/settings#company">Company Information</a>
            </li>
        </ul>
        </li>
        <?php endif; ?>
    </ul>
    </div>
    <!-- Sidebar menu ends -->

    <!-- Sidebar contact starts -->
    <div class="sidebar-contact">
    <p class="fw-light mb-1 text-nowrap text-truncate">Emergency Contact</p>
    <h5 class="m-0 lh-1 text-nowrap text-truncate">+60 1163844258</h5>
    <i class="ri-phone-line"></i>
    </div>
    <!-- Sidebar contact ends -->
<?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/partials/sidebar.blade.php ENDPATH**/ ?>