@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-user-add-line text-primary me-2"></i>Check In Visitor
                        </h4>
                        <p class="text-muted mb-0">
                            Register a new visitor to the property
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('visitors.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Visitors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Check-in Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('visitors.store') }}" method="POST" id="checkInForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Visitor Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-user-line me-2"></i>Visitor Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                       id="name" name="name" value="{{ old('name') }}" 
                                                       placeholder="Enter visitor's full name" required>
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                                       id="phone" name="phone" value="{{ old('phone') }}" 
                                                       placeholder="Enter phone number" required>
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email Address</label>
                                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                       id="email" name="email" value="{{ old('email') }}" 
                                                       placeholder="Enter email address (optional)">
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                                <input type="tel" class="form-control @error('emergency_contact') is-invalid @enderror" 
                                                       id="emergency_contact" name="emergency_contact" value="{{ old('emergency_contact') }}" 
                                                       placeholder="Emergency contact number">
                                                @error('emergency_contact')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="identification_type" class="form-label">ID Type <span class="text-danger">*</span></label>
                                                <select class="form-select @error('identification_type') is-invalid @enderror" 
                                                        id="identification_type" name="identification_type" required>
                                                    <option value="">Select ID Type</option>
                                                    <option value="national_id" {{ old('identification_type') == 'national_id' ? 'selected' : '' }}>National ID</option>
                                                    <option value="passport" {{ old('identification_type') == 'passport' ? 'selected' : '' }}>Passport</option>
                                                    <option value="driver_license" {{ old('identification_type') == 'driver_license' ? 'selected' : '' }}>Driver's License</option>
                                                    <option value="other" {{ old('identification_type') == 'other' ? 'selected' : '' }}>Other</option>
                                                </select>
                                                @error('identification_type')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="identification_number" class="form-label">ID Number <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('identification_number') is-invalid @enderror" 
                                                       id="identification_number" name="identification_number" value="{{ old('identification_number') }}" 
                                                       placeholder="Enter ID number" required>
                                                @error('identification_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="vehicle_number" class="form-label">Vehicle Number</label>
                                        <input type="text" class="form-control @error('vehicle_number') is-invalid @enderror" 
                                               id="vehicle_number" name="vehicle_number" value="{{ old('vehicle_number') }}" 
                                               placeholder="Enter vehicle registration number (if applicable)">
                                        @error('vehicle_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Visit Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-map-pin-line me-2"></i>Visit Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                                <select class="form-select @error('property_id') is-invalid @enderror" 
                                                        id="property_id" name="property_id" required>
                                                    <option value="">Select Property</option>
                                                    @foreach($properties as $property)
                                                        <option value="{{ $property->id }}" {{ old('property_id') == $property->id ? 'selected' : '' }}>
                                                            {{ $property->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('property_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="visiting_unit_id" class="form-label">Unit</label>
                                                <select class="form-select @error('visiting_unit_id') is-invalid @enderror" 
                                                        id="visiting_unit_id" name="visiting_unit_id">
                                                    <option value="">Select Unit (Optional)</option>
                                                </select>
                                                @error('visiting_unit_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="visiting_tenant_id" class="form-label">Visiting Tenant</label>
                                                <select class="form-select @error('visiting_tenant_id') is-invalid @enderror" 
                                                        id="visiting_tenant_id" name="visiting_tenant_id">
                                                    <option value="">Select Tenant (Optional)</option>
                                                    @foreach($tenants as $tenant)
                                                        <option value="{{ $tenant->id }}" {{ old('visiting_tenant_id') == $tenant->id ? 'selected' : '' }}>
                                                            {{ $tenant->name }} ({{ $tenant->email }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('visiting_tenant_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="purpose_of_visit" class="form-label">Purpose of Visit <span class="text-danger">*</span></label>
                                                <select class="form-select @error('purpose_of_visit') is-invalid @enderror" 
                                                        id="purpose_of_visit" name="purpose_of_visit" required>
                                                    <option value="">Select Purpose</option>
                                                    <option value="Personal Visit" {{ old('purpose_of_visit') == 'Personal Visit' ? 'selected' : '' }}>Personal Visit</option>
                                                    <option value="Business Meeting" {{ old('purpose_of_visit') == 'Business Meeting' ? 'selected' : '' }}>Business Meeting</option>
                                                    <option value="Maintenance" {{ old('purpose_of_visit') == 'Maintenance' ? 'selected' : '' }}>Maintenance</option>
                                                    <option value="Delivery" {{ old('purpose_of_visit') == 'Delivery' ? 'selected' : '' }}>Delivery</option>
                                                    <option value="Inspection" {{ old('purpose_of_visit') == 'Inspection' ? 'selected' : '' }}>Inspection</option>
                                                    <option value="Emergency" {{ old('purpose_of_visit') == 'Emergency' ? 'selected' : '' }}>Emergency</option>
                                                    <option value="Other" {{ old('purpose_of_visit') == 'Other' ? 'selected' : '' }}>Other</option>
                                                </select>
                                                @error('purpose_of_visit')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Additional Notes</label>
                                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                                  id="notes" name="notes" rows="3" 
                                                  placeholder="Any additional information about the visit...">{{ old('notes') }}</textarea>
                                        @error('notes')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-time-line me-2"></i>Check-in Details
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Check-in Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="ri-calendar-line"></i>
                                            </span>
                                            <input type="text" class="form-control" 
                                                   value="{{ now()->format('M d, Y H:i') }}" readonly>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Checked in by</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="ri-user-line"></i>
                                            </span>
                                            <input type="text" class="form-control" 
                                                   value="{{ auth()->user()->name }}" readonly>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="ri-information-line me-2"></i>
                                        <strong>Security Notice:</strong> All visitors are required to provide valid identification and will be logged for security purposes.
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Visitors -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-history-line me-2"></i>Recent Visitors
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <!-- This would be populated with recent visitors -->
                                        <div class="text-center text-muted py-3">
                                            <i class="ri-user-line fs-1 mb-2"></i>
                                            <p class="mb-0">Recent visitors will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('visitors.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In Visitor
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('visiting_unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Form validation
    const form = document.getElementById('checkInForm');
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        const phone = document.getElementById('phone').value;
        const idType = document.getElementById('identification_type').value;
        const idNumber = document.getElementById('identification_number').value;
        const propertyId = document.getElementById('property_id').value;
        const purpose = document.getElementById('purpose_of_visit').value;

        if (!name || !phone || !idType || !idNumber || !propertyId || !purpose) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Show confirmation
        Swal.fire({
            title: 'Check In Visitor',
            html: `Are you sure you want to check in:<br><br><strong>${name}</strong><br>Phone: ${phone}`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, Check In',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (!result.isConfirmed) {
                e.preventDefault();
            }
        });
    });

    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        this.value = value;
    });
});
</script>
@endsection
