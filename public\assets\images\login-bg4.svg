<svg xmlns="http://www.w3.org/2000/svg" width="454.332" height="436.861" viewBox="0 0 454.332 436.861"><defs><style>.a{fill:#ebebeb;}.b{fill:#fafafa;}.c{fill:#f5f5f5;}.d{fill:#e0e0e0;}.e,.m{fill:#fff;}.f{fill:#455a64;}.g,.l{fill:#263238;}.h{fill:#f24a46;}.i{opacity:0.3;}.j{fill:#ffbe9d;}.k{fill:#eb996e;}.l{font-size:9px;}.l,.m{font-family:<PERSON>serrat-Bold, Montserrat;font-weight:700;}.m{font-size:8px;}.n{fill:#ffcdb0;}.o{opacity:0.6;}</style></defs><g transform="translate(-219.238 -105.695)"><g transform="translate(222.677 105.695)"><path class="a" d="M664.525,182.733c.5-3.657-4.655-6.65-8.55-6.973-1.852-.152-3.8.133-5.518-.475-3.8-1.33-5.32-6.232-9.024-7.789-2.764-1.169-5.956-.134-8.635,1.215s-5.339,3.059-8.341,3.249c-2.6.162-5.443-.8-7.723.475-1.7.95-2.593,2.907-4.16,4.066a9.915,9.915,0,0,1-5.434,1.472,15.434,15.434,0,0,0-5.6.95c-1.728.8-3.163,2.632-2.8,4.5Z" transform="translate(-247.644 -109.728)"></path><path class="b" d="M610.172,116.442c.351-2.508-3.182-4.56-5.832-4.749a14.746,14.746,0,0,1-3.8-.323c-2.593-.95-3.629-4.255-6.166-5.329-1.9-.8-4.066-.086-5.908.835s-3.648,2.09-5.7,2.223c-1.777.114-3.714-.551-5.282.322-1.158.646-1.767,1.986-2.849,2.784a6.849,6.849,0,0,1-3.714,1.007,10.424,10.424,0,0,0-3.8.627,3.02,3.02,0,0,0-1.9,3.068Z" transform="translate(-245.442 -105.695)"></path><path class="c" d="M543.15,139.693c.436-3.173-4.047-5.795-7.419-6.06-1.615-.134-3.286.114-4.806-.419-3.306-1.149-4.617-5.415-7.846-6.773-2.4-1.016-5.177-.114-7.514,1.064s-4.645,2.65-7.248,2.85c-2.271.142-4.75-.7-6.726.408-1.472.826-2.25,2.527-3.61,3.533a8.541,8.541,0,0,1-4.749,1.283,13.374,13.374,0,0,0-4.882.8c-1.5.694-2.755,2.29-2.441,3.914Z" transform="translate(-240.22 -107.031)"></path><rect class="a" width="72.118" height="95.115" transform="translate(169.019 152.703)"></rect><rect class="d" width="33.817" height="95.115" transform="translate(207.32 152.703)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(184.142 165.584)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(184.142 227.234)"></rect><path class="c" d="M442.143,306.448c0,.18-8.549.322-19.15.322s-19.15-.142-19.15-.322,8.549-.323,19.15-.323S442.143,306.22,442.143,306.448Z" transform="translate(-234.824 -118.883)"></path><rect class="c" width="6.488" height="15.75" transform="translate(184.142 196.646)"></rect><path class="c" d="M442.143,339.647c0,.18-8.549.323-19.15.323s-19.15-.143-19.15-.323,8.549-.323,19.15-.323S442.143,339.467,442.143,339.647Z" transform="translate(-234.824 -121.067)"></path><rect class="c" width="6.488" height="15.75" transform="translate(214.501 165.584)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(228.569 165.584)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(214.501 195.478)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(228.569 195.478)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(214.501 225.362)"></rect><rect class="c" width="6.488" height="15.75" transform="translate(228.569 225.362)"></rect><rect class="d" width="82.956" height="3.429" transform="translate(163.595 149.388)"></rect><path class="a" d="M476.508,269.6c0,.18-16.149.323-36,.323s-35.992-.143-35.992-.323,16.149-.314,35.992-.314S476.508,269.428,476.508,269.6Z" transform="translate(-234.868 -116.459)"></path><rect class="c" width="49.396" height="156.556" transform="translate(37.189 69.794)"></rect><rect class="a" width="24.698" height="156.119" transform="translate(61.887 69.794)"></rect><rect class="c" width="17.44" height="15.37" transform="translate(42.832 54.424)"></rect><rect class="a" width="4.161" height="15.37" transform="translate(56.112 54.424)"></rect><path class="d" d="M258.325,180.351v-.9h54.953v.9" transform="translate(-225.249 -110.548)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 76.501)"></rect><path class="d" d="M287.425,200.709c0,.085-5.529.162-12.348.162s-12.349-.077-12.349-.162,5.529-.171,12.349-.171S287.425,200.623,287.425,200.709Z" transform="translate(-225.539 -111.935)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 76.501)"></rect><path class="d" d="M314.1,200.709c0,.085-5.529.162-12.349.162s-12.349-.077-12.349-.162,5.529-.171,12.349-.171S314.1,200.623,314.1,200.709Z" transform="translate(-227.293 -111.935)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 94.008)"></rect><path class="d" d="M314.1,219.45c0,.1-5.529.171-12.349.171s-12.349-.076-12.349-.171,5.529-.162,12.349-.162S314.1,219.354,314.1,219.45Z" transform="translate(-227.293 -113.169)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 112.037)"></rect><path class="d" d="M314.1,238.758c0,.086-5.529.162-12.349.162s-12.349-.076-12.349-.162,5.529-.171,12.349-.171S314.1,238.664,314.1,238.758Z" transform="translate(-227.293 -114.439)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 130.076)"></rect><path class="d" d="M314.1,258.058c0,.094-5.529.171-12.349.171s-12.349-.077-12.349-.171,5.529-.171,12.349-.171S314.1,257.963,314.1,258.058Z" transform="translate(-227.293 -115.709)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 147.592)"></rect><path class="d" d="M314.1,276.808c0,.1-5.529.171-12.349.171s-12.349-.076-12.349-.171,5.529-.171,12.349-.171S314.1,276.713,314.1,276.808Z" transform="translate(-227.293 -116.943)"></path><rect class="d" width="4.18" height="8.815" transform="translate(71.852 165.175)"></rect><rect class="d" width="4.18" height="8.815" transform="translate(71.852 182.996)"></rect><path class="d" d="M314.1,295.64c0,.085-5.529.161-12.349.161s-12.349-.076-12.349-.161,5.529-.171,12.349-.171S314.1,295.545,314.1,295.64Z" transform="translate(-227.293 -118.182)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 94.273)"></rect><path class="d" d="M287.425,219.735c0,.094-5.529.171-12.348.171s-12.349-.077-12.349-.171,5.529-.162,12.349-.162S287.425,219.649,287.425,219.735Z" transform="translate(-225.539 -113.188)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 112.046)"></rect><path class="d" d="M287.425,238.759c0,.1-5.529.171-12.348.171s-12.349-.076-12.349-.171,5.529-.161,12.349-.161S287.425,238.664,287.425,238.759Z" transform="translate(-225.539 -114.44)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 129.819)"></rect><path class="d" d="M287.425,257.783c0,.1-5.529.171-12.348.171s-12.349-.076-12.349-.171,5.529-.171,12.349-.171S287.425,257.66,287.425,257.783Z" transform="translate(-225.539 -115.691)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 147.592)"></rect><path class="d" d="M287.425,276.808c0,.1-5.529.171-12.348.171s-12.349-.076-12.349-.171,5.529-.171,12.349-.171S287.425,276.713,287.425,276.808Z" transform="translate(-225.539 -116.943)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 165.356)"></rect><path class="d" d="M287.425,295.833c0,.094-5.529.171-12.348.171s-12.349-.077-12.349-.171,5.529-.171,12.349-.171S287.425,295.738,287.425,295.833Z" transform="translate(-225.539 -118.194)"></path><rect class="b" width="4.18" height="8.815" transform="translate(46.935 183.129)"></rect><path class="a" d="M302.407,371.031H229.719L229.4,256.148h72.7Z" transform="translate(-223.345 -115.594)"></path><rect class="d" width="36.344" height="114.883" transform="translate(42.395 140.553)"></rect><rect class="a" width="25.667" height="22.618" transform="translate(14.353 117.936)"></rect><rect class="d" width="6.117" height="22.618" transform="translate(33.903 117.936)"></rect><rect class="d" width="80.876" height="1.32" transform="translate(0 139.233)"></rect><rect class="e" width="6.155" height="12.976" transform="translate(20.404 150.423)"></rect><path class="d" d="M265.74,286.026c0,.142-8.131.256-18.172.256s-18.172-.114-18.172-.256,8.141-.248,18.172-.248S265.74,285.892,265.74,286.026Z" transform="translate(-223.345 -117.544)"></path><rect class="c" width="6.155" height="12.976" transform="translate(57.062 150.423)"></rect><path class="a" d="M304.989,286.026c0,.142-8.141.256-18.171.256s-18.172-.114-18.172-.256,8.131-.248,18.172-.248S304.989,285.892,304.989,286.026Z" transform="translate(-225.928 -117.544)"></path><rect class="c" width="6.155" height="12.976" transform="translate(57.062 176.175)"></rect><path class="a" d="M304.989,313.571c0,.134-8.141.248-18.171.248s-18.172-.114-18.172-.248,8.131-.247,18.172-.247S304.989,313.466,304.989,313.571Z" transform="translate(-225.928 -119.357)"></path><rect class="c" width="6.155" height="12.976" transform="translate(57.062 202.716)"></rect><path class="a" d="M304.989,342.041c0,.134-8.141.248-18.171.248s-18.172-.114-18.172-.248,8.131-.256,18.172-.256S304.989,341.87,304.989,342.041Z" transform="translate(-225.928 -121.229)"></path><rect class="c" width="6.155" height="12.976" transform="translate(57.062 229.247)"></rect><path class="a" d="M304.989,370.412c0,.133-8.141.247-18.171.247s-18.172-.114-18.172-.247,8.131-.248,18.172-.248S304.989,370.278,304.989,370.412Z" transform="translate(-225.928 -123.097)"></path><rect class="e" width="6.155" height="12.976" transform="translate(20.404 176.574)"></rect><path class="d" d="M265.74,314.019c0,.142-8.131.256-18.172.256s-18.172-.114-18.172-.256,8.141-.248,18.172-.248S265.74,313.885,265.74,314.019Z" transform="translate(-223.345 -119.386)"></path><rect class="e" width="6.155" height="12.976" transform="translate(20.404 202.726)"></rect><path class="d" d="M265.74,342.041c0,.134-8.131.248-18.172.248s-18.172-.114-18.172-.248,8.141-.256,18.172-.256S265.74,341.88,265.74,342.041Z" transform="translate(-223.345 -121.229)"></path><rect class="e" width="6.155" height="12.976" transform="translate(20.404 228.877)"></rect><path class="d" d="M265.74,370.014c0,.134-8.131.248-18.172.248s-18.172-.114-18.172-.248,8.141-.256,18.172-.256S265.74,369.872,265.74,370.014Z" transform="translate(-223.345 -123.07)"></path><path class="c" d="M634.186,304.535a26.182,26.182,0,0,1-16.149-12.092,23.266,23.266,0,0,1-1.691-19.216c1.758-4.883,5.139-9.186,6.593-14.163,1.643-5.643.665-11.636-.351-17.4s-2.051-11.75-.513-17.422,6.411-11,12.662-11.4c5.795-.37,11.143,3.639,13.565,8.55s2.432,10.591,1.9,15.987-1.662,10.8-1.32,16.215c.7,10.971,7.276,21.3,6.507,32.3-.408,5.7-2.774,9.366-6.821,13.66-3.486,3.7-9.29,6.137-14.429,5" transform="translate(-248.719 -112.743)"></path><path class="d" d="M637.684,361.524c-.066,0-.114-24.3-.114-54.259s.048-54.269.114-54.269.1,24.289.1,54.269S637.741,361.524,637.684,361.524Z" transform="translate(-250.203 -115.387)"></path><path class="d" d="M636.909,290.248a7.087,7.087,0,0,1-1.007-.884c-.6-.561-1.406-1.358-2.261-2.27s-1.643-1.72-2.214-2.318a8.32,8.32,0,0,1-.883-.95,7.341,7.341,0,0,1,1,.884c.6.56,1.416,1.358,2.262,2.27s1.643,1.728,2.213,2.318A9.048,9.048,0,0,1,636.909,290.248Z" transform="translate(-249.741 -117.416)"></path><rect class="d" width="36.287" height="4.436" transform="translate(369.642 241.634)"></rect><path class="d" d="M638.047,265.96a7.466,7.466,0,0,0,2.213-.95,6.018,6.018,0,0,0,1.777-1.8,5.887,5.887,0,0,0,.95-2.849v-.1l.123.1a4.746,4.746,0,0,0-2.669,1.472,9.513,9.513,0,0,0-1.492,1.995c-.351.608-.608,1.131-.779,1.483a3.187,3.187,0,0,1-.276.55,4.244,4.244,0,0,1,.2-.579,13.3,13.3,0,0,1,.722-1.52,9.768,9.768,0,0,1,1.472-2.061,4.9,4.9,0,0,1,2.792-1.539h.134v.228a6.157,6.157,0,0,1-2.85,4.749,5.583,5.583,0,0,1-1.653.7,3.719,3.719,0,0,1-.475.086Z" transform="translate(-250.224 -115.859)"></path><path class="d" d="M638.058,266.109a12.938,12.938,0,0,1,1.52-1.9,13.908,13.908,0,0,1,1.682-1.757,14.238,14.238,0,0,1-1.52,1.9A14.66,14.66,0,0,1,638.058,266.109Z" transform="translate(-250.235 -116.009)"></path></g><g transform="translate(290.957 111.246)"><path class="f" d="M416.42,402.7l-98.886-.057A21.536,21.536,0,0,1,296.008,381.1V133.171a21.535,21.535,0,0,1,21.535-21.534h.019l98.887.057a21.544,21.544,0,0,1,21.525,21.544l-.048,247.929A21.535,21.535,0,0,1,416.42,402.7Z" transform="translate(-296.008 -111.637)"></path><path class="e" d="M417.728,121.08H398.559a4.749,4.749,0,0,0-4.655,4.842v3.5a4.748,4.748,0,0,1-4.633,4.863H351.88a4.749,4.749,0,0,1-4.655-4.842v-3.536a4.749,4.749,0,0,0-4.633-4.863H319.041a15.1,15.1,0,0,0-15.094,15.066L303.9,378.081a15.084,15.084,0,0,0,15.085,15.085h0l98.678.057a15.075,15.075,0,0,0,15.094-15.056v-.029l.048-241.964A15.094,15.094,0,0,0,417.728,121.08Z" transform="translate(-296.527 -112.256)"></path><path class="g" d="M417.712,121.067h.522a10.878,10.878,0,0,1,1.529.114,15.06,15.06,0,0,1,11.95,9.252,15.571,15.571,0,0,1,1.149,6.137v7.106c0,20.1,0,49.187.048,85.122s0,78.719,0,126.235V377.64a16.4,16.4,0,0,1-.579,4.6,15.342,15.342,0,0,1-1.9,4.255,16.367,16.367,0,0,1-3.144,3.486,15.313,15.313,0,0,1-8.711,3.382c-3.163.1-6.307,0-9.5,0h-88.97a15.961,15.961,0,0,1-9.822-2.717,15.467,15.467,0,0,1-5.984-8.265,16.567,16.567,0,0,1-.627-5.1V136.332c0-.607.057-1.225.086-1.833a8.3,8.3,0,0,1,.313-1.8A15.26,15.26,0,0,1,319.6,120.858h22.57a4.826,4.826,0,0,1,4.816,3.258,8.161,8.161,0,0,1,.314,2.974v1.472a14.136,14.136,0,0,0,0,1.434,4.749,4.749,0,0,0,1.13,2.527,4.427,4.427,0,0,0,2.308,1.434,4.925,4.925,0,0,0,1.358.134h37.55a4.348,4.348,0,0,0,1.843-.656,4.749,4.749,0,0,0,2.118-3.04,8.336,8.336,0,0,0,.114-1.824v-1.8a9.975,9.975,0,0,1,.076-1.767,5.045,5.045,0,0,1,.571-1.615,4.748,4.748,0,0,1,2.4-2.128,7.611,7.611,0,0,1,3-.313h0a7.249,7.249,0,0,0-2.945.323,4.614,4.614,0,0,0-2.336,2.07,4.871,4.871,0,0,0-.542,1.568,10.4,10.4,0,0,0-.076,1.739v1.8a8.343,8.343,0,0,1-.114,1.9,4.982,4.982,0,0,1-2.185,3.154,4.646,4.646,0,0,1-1.9.684c-.7.057-1.377,0-2.07,0H352.177a5.186,5.186,0,0,1-1.425-.142,4.677,4.677,0,0,1-2.432-1.273,5.118,5.118,0,0,1-1.2-2.66,11.063,11.063,0,0,1-.048-1.472v-1.52a7.937,7.937,0,0,0-.294-2.849,4.754,4.754,0,0,0-1.748-2.29,4.593,4.593,0,0,0-2.85-.779H319.643a14.916,14.916,0,0,0-15.16,11.618c-.152.57-.2,1.169-.3,1.757a10.242,10.242,0,0,0-.076,1.8V377.289a16.327,16.327,0,0,0,.608,4.988,15.123,15.123,0,0,0,5.814,8.027,15.544,15.544,0,0,0,9.584,2.641h89c3.163,0,6.345.047,9.451,0a14.9,14.9,0,0,0,8.483-3.287,16.01,16.01,0,0,0,3.06-3.4,14.773,14.773,0,0,0,1.9-4.132,15.934,15.934,0,0,0,.561-4.493v-22.58c0-47.5,0-90.3.076-126.244s.057-65.012.077-85.112v-7.106a15.721,15.721,0,0,0-1.1-6.08,14.843,14.843,0,0,0-2.849-4.483,15.028,15.028,0,0,0-9.024-4.749,11.849,11.849,0,0,0-1.52-.143Z" transform="translate(-296.512 -112.243)"></path><path class="g" d="M428.061,150.6c0,.067-2.5.143-5.566.162s-5.576,0-5.576-.066,2.5-.142,5.567-.162S428.061,150.53,428.061,150.6Z" transform="translate(-303.964 -114.196)"></path><path class="g" d="M428.083,153.647c0,.066-2.489.142-5.567.162s-5.566,0-5.566-.066,2.489-.143,5.566-.162S428.083,153.58,428.083,153.647Z" transform="translate(-303.966 -114.396)"></path><path class="g" d="M428.112,156.7c0,.067-2.5.143-5.566.162s-5.576,0-5.576-.066,2.5-.142,5.567-.162S428.112,156.631,428.112,156.7Z" transform="translate(-303.967 -114.597)"></path><path class="g" d="M320.239,157h.884l2.375-.057-.066.066v-2.669a.949.949,0,0,1,.8-.731h1.14a.95.95,0,0,1,.779.95v2.517l-.1-.1h2.992l-.114.123v-4.692a.953.953,0,0,0-.418-.713l-1.311-1.292-1.273-1.244-.608-.608c-.208-.2-.38-.418-.627-.465a.8.8,0,0,0-.684.133c-.18.152-.371.371-.561.551l-1.073,1.083c-.674.7-1.387,1.349-1.9,1.957a2.131,2.131,0,0,0-.142,1.121v3.847a3.386,3.386,0,0,1,0,.39v-4.816a1.228,1.228,0,0,1,.162-.627c.561-.685,1.215-1.283,1.9-2l1.073-1.092c.191-.191.352-.38.58-.58a1.04,1.04,0,0,1,.864-.171,1.65,1.65,0,0,1,.722.513l.39.409,1.272,1.244,1.311,1.292a2.063,2.063,0,0,1,.333.361,1.052,1.052,0,0,1,.162.5V157h-3.23v-2.632a.73.73,0,0,0-.608-.721h-1.092a.741.741,0,0,0-.646.589v2.736H320.4A.673.673,0,0,1,320.239,157Z" transform="translate(-297.602 -114.019)"></path><rect class="h" width="81.731" height="24.964" rx="11.734" transform="translate(32.041 193.052)"></rect><path class="a" d="M428.715,214.572c.219-1.615-2.051-2.935-3.8-3.077a9.931,9.931,0,0,1-2.432-.209c-1.672-.579-2.337-2.746-3.971-3.429a4.643,4.643,0,0,0-3.8.542,10.118,10.118,0,0,1-3.676,1.425c-1.14.076-2.393-.351-3.4.209-.741.418-1.14,1.282-1.824,1.785a4.345,4.345,0,0,1-2.393.656,6.7,6.7,0,0,0-2.47.4,1.9,1.9,0,0,0-1.235,1.985Z" transform="translate(-302.831 -117.956)"></path><rect class="c" width="73.533" height="61.403" transform="translate(38.738 109.734)"></rect><path class="g" d="M337.47,290.5c0-1.729,0-24.574-.077-61.4v-.085h73.714v61.583h-.086l-73.542-.086,73.542-.085-.094.085V229.1l.1.1H337.5l.086-.085C337.507,265.923,337.479,288.768,337.47,290.5Z" transform="translate(-298.731 -119.36)"></path><rect class="f" width="7.637" height="17.858" transform="translate(55.162 153.279)"></rect><path class="b" d="M355.91,286.442a.389.389,0,0,0,.389.39h.01a.39.39,0,0,0,.39-.39h0a.389.389,0,0,0-.38-.4h-.009A.4.4,0,0,0,355.91,286.442Z" transform="translate(-299.95 -123.113)"></path><path class="d" d="M337.475,257.851s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,257.813,337.475,257.851Z" transform="translate(-298.737 -121.253)"></path><path class="d" d="M337.475,260.18s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,260.142,337.475,260.18Z" transform="translate(-298.737 -121.407)"></path><path class="d" d="M337.475,262.508s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,262.471,337.475,262.508Z" transform="translate(-298.737 -121.56)"></path><path class="d" d="M337.475,264.838s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.057-36.677-.057S337.475,264.781,337.475,264.838Z" transform="translate(-298.737 -121.714)"></path><path class="d" d="M337.475,267.165s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,267.128,337.475,267.165Z" transform="translate(-298.737 -121.866)"></path><path class="d" d="M337.475,269.494s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,269.456,337.475,269.494Z" transform="translate(-298.737 -122.02)"></path><path class="d" d="M337.475,271.822s16.414.067,36.676.067,36.677,0,36.677-.067-16.424-.066-36.677-.066S337.475,271.785,337.475,271.822Z" transform="translate(-298.737 -122.173)"></path><path class="d" d="M337.475,274.151s16.414.066,36.676.066,36.677,0,36.677-.066-16.424-.066-36.677-.066S337.475,274.113,337.475,274.151Z" transform="translate(-298.737 -122.326)"></path><rect class="e" width="5.681" height="11.124" transform="translate(77.523 118.056)"></rect><path class="g" d="M378.979,249.123a.147.147,0,0,0,0-.057V238l-.067.066h5.7s-.133-.123-.066-.066h0v11.1h.057V237.942h0l-.066-.066h-5.747v11.19Z" transform="translate(-301.455 -119.943)"></path><path class="g" d="M382.007,249.118s.066-2.5.066-5.586,0-5.595-.066-5.595-.067,2.507-.067,5.595S381.969,249.118,382.007,249.118Z" transform="translate(-301.662 -119.948)"></path><path class="g" d="M379.063,244.368s1.282.066,2.849.066a21.917,21.917,0,0,0,2.85-.066,21.922,21.922,0,0,0-2.85-.066C380.345,244.3,379.063,244.33,379.063,244.368Z" transform="translate(-301.473 -120.366)"></path><rect class="b" width="5.681" height="11.124" transform="translate(45.122 153.744)"></rect><path class="g" d="M344.3,287.381V276.258l-.067.066h5.7l-.066-.066h0v11.1h.162V276.276h0c.066.057-.076-.085-.066-.066h-5.7v11.171Z" transform="translate(-299.181 -122.466)"></path><path class="g" d="M347.323,287.38s.066-2.508.066-5.595,0-5.595-.066-5.595-.067,2.507-.067,5.595S347.285,287.38,347.323,287.38Z" transform="translate(-299.38 -122.465)"></path><path class="g" d="M344.379,282.57s1.283.066,2.849.066a21.932,21.932,0,0,0,2.85-.066,21.912,21.912,0,0,0-2.85-.066C345.662,282.5,344.379,282.533,344.379,282.57Z" transform="translate(-299.191 -122.88)"></path><path class="d" d="M387.583,253.617a1.031,1.031,0,0,0,0-.152v-1.919h5.7l-.067-.066h0v2.108l.067-.066h.094v-1.966h0s-.123-.133-.066-.076h-5.747v1.976A.787.787,0,0,0,387.583,253.617Z" transform="translate(-302.032 -120.838)"></path><path class="d" d="M349.348,235.5a.908.908,0,0,0,0-.143v-1.776h4.113l-.066-.066h0v1.9l.057-.066h-4.113a1.549,1.549,0,0,0,.294,0h3.942v-2h0s-.133-.134-.076-.066h-4.17v1.842A.572.572,0,0,0,349.348,235.5Z" transform="translate(-299.515 -119.64)"></path><rect class="d" width="5.519" height="2.014" transform="translate(81.902 132.779)"></rect><rect class="d" width="5.519" height="2.014" transform="translate(75.12 114.512)"></rect><rect class="d" width="5.519" height="2.014" transform="translate(48.456 130.946)"></rect><rect class="d" width="4.341" height="2.014" transform="translate(38.985 123.499)"></rect><path class="h" d="M349.693,207.259l50.555.361,16.282,20.043H333.611Z" transform="translate(-298.482 -117.929)"></path><path class="g" d="M349.683,207.259s-.37.494-1.092,1.425l-3.182,4.056-11.75,15.009-.057-.114h82.918l-.076.162c-5.8-7.153-11.276-13.907-16.272-20.053h.066l-36.553-.3-10.325-.1h10.3l36.581.219h0l16.291,20.043.134.152H333.468l.086-.1L345.371,212.7l3.2-4.027Z" transform="translate(-298.473 -117.929)"></path><path class="c" d="M376.488,215.7l-3.885,6.469H355.1V215.7l7.637-13.869,9.87,13.755Z" transform="translate(-299.896 -117.572)"></path><circle class="e" cx="2.859" cy="2.859" r="2.859" transform="translate(60.681 92.826)"></circle><path class="g" d="M360.993,213.855a2.174,2.174,0,0,1,.057-.7,2.848,2.848,0,0,1,1.035-1.615,2.907,2.907,0,0,1,2.774-.446,2.945,2.945,0,0,1,0,5.538,2.907,2.907,0,0,1-2.774-.447,2.848,2.848,0,0,1-1.035-1.615,2.228,2.228,0,0,1-.057-.712,2.661,2.661,0,0,0,.133.693,2.794,2.794,0,0,0,3.418,1.979,2.568,2.568,0,0,0,.249-.079,2.774,2.774,0,1,0-2.632-4.816,2.855,2.855,0,0,0-1.035,1.529A2.8,2.8,0,0,0,360.993,213.855Z" transform="translate(-300.284 -118.17)"></path><rect class="e" width="18.39" height="19.074" transform="translate(53.243 113.733)"></rect><path class="g" d="M353,252.451V233.376l-.057.057h18.39l-.066-.066v19.074l.057-.066h.048V233.168H352.849v19.055A.3.3,0,0,0,353,252.451Z" transform="translate(-299.748 -119.634)"></path><path class="g" d="M362.473,252.207s.066-4.18.066-9.347,0-9.348-.066-9.348-.066,4.19-.066,9.348S362.436,252.207,362.473,252.207Z" transform="translate(-300.377 -119.656)"></path><path class="g" d="M353.225,244.246s4.028.066,9,.066,9,0,9-.066-4.027-.066-9-.066S353.225,244.208,353.225,244.246Z" transform="translate(-299.773 -120.358)"></path><rect class="b" width="5.681" height="11.124" transform="translate(42.69 117.828)"></rect><path class="g" d="M341.667,248.879a.287.287,0,0,1,0-.057V237.755l-.066.066h5.7l-.066-.066h0v11.1h.1V237.7h0c.057.066-.085-.086-.066-.066h-5.7v11.19Z" transform="translate(-299.006 -119.927)"></path><path class="g" d="M344.72,248.883s.066-2.508.066-5.595,0-5.595-.066-5.595-.067,2.507-.067,5.595S344.72,248.883,344.72,248.883Z" transform="translate(-299.209 -119.931)"></path><path class="g" d="M341.776,244.124s1.282.066,2.849.066a21.916,21.916,0,0,0,2.85-.066,22.016,22.016,0,0,0-2.85-.066C343.068,244.058,341.776,244.086,341.776,244.124Z" transform="translate(-299.02 -120.35)"></path><rect class="c" width="2.802" height="21.753" transform="translate(31.633 149.384)"></rect><path class="g" d="M329.869,293.289V271.431h2.9c.2.2,0,.066.1.1h0v12.947c0,3.676,0,6.707-.048,8.8h0l-.048.048V271.582h0c.048,0-.1-.1.1.1h-2.85l.1-.1V292.89A.422.422,0,0,1,329.869,293.289Z" transform="translate(-298.236 -122.151)"></path><rect class="c" width="2.802" height="21.753" transform="translate(66.59 149.384)"></rect><path class="g" d="M367.288,293.289V271.431h2.849c.2.2.057.066.1.1h0v21.743h-.048v-21.7h0l.1.1h-2.8l.1-.1c0,6.08,0,11.476-.048,15.437v5.871A.422.422,0,0,1,367.288,293.289Z" transform="translate(-300.698 -122.151)"></path><path class="h" d="M386.482,270.888H325.944l13.166-10.877h32.763Z" transform="translate(-297.978 -121.4)"></path><g class="i" transform="translate(27.966 138.612)"><path d="M386.482,270.888H325.944l13.166-10.877h32.763Z" transform="translate(-325.944 -260.012)"></path></g><path class="h" d="M374.3,260.012l-11.466,10.877h26.161Z" transform="translate(-300.405 -121.4)"></path><path class="h" d="M354.947,215.607h-2.118l8.787-15.161.969,1.292-7.637,13.869" transform="translate(-299.747 -117.481)"></path><path class="g" d="M354.948,215.615h-2.09l8.8-15.2h0l.95,1.292h0Zm-2.1,0h2.09l7.6-13.86-.95-1.272Z" transform="translate(-299.748 -117.479)"></path><g class="i" transform="translate(53.082 82.966)"><path d="M354.947,215.607h-2.118l8.787-15.161.969,1.292-7.637,13.869" transform="translate(-352.829 -200.446)"></path></g><path class="a" d="M373.836,223.136v-6.573h3.942Z" transform="translate(-301.129 -118.541)"></path><path class="h" d="M376.958,215.6h3.163l-10.962-15.227-6.925.066,10.839,15.047Z" transform="translate(-300.366 -117.476)"></path><path class="g" d="M376.949,215.6h-1.016l-2.849-.066h0L362.2,200.517l-.1-.152h7.163l10.924,15.265.057.077h-.094v.076l-11.01-15.2h-6.84l.077-.162,10.81,15.066h0l2.85.114Z" transform="translate(-300.356 -117.475)"></path><path class="g" d="M397.44,229.192c.048,0,.1,13.736.1,30.673s0,30.673-.1,30.673-.1-13.726-.1-30.673S397.383,229.192,397.44,229.192Z" transform="translate(-302.675 -119.372)"></path><rect class="b" width="5.681" height="11.124" transform="translate(75.566 154.076)"></rect><path class="g" d="M376.9,287.679V276.547l-.066.066h5.7l-.066-.066h0v11.1l.047-.057h.057v-11.1h0l-.076-.077h-5.7v11.133A.265.265,0,0,1,376.9,287.679Z" transform="translate(-301.324 -122.479)"></path><path class="g" d="M379.922,287.676s.066-2.508.066-5.6,0-5.585-.066-5.585-.076,2.5-.076,5.585S379.883,287.676,379.922,287.676Z" transform="translate(-301.525 -122.485)"></path><path class="g" d="M376.978,282.926s1.283.066,2.85.066a21.9,21.9,0,0,0,2.849-.066A54.209,54.209,0,0,0,376.978,282.926Z" transform="translate(-301.336 -122.903)"></path><path class="g" d="M415.53,294.79s-20.575.1-45.948.1-45.947-.048-45.947-.1,20.565-.1,45.947-.1S415.53,294.733,415.53,294.79Z" transform="translate(-297.826 -123.682)"></path><path class="g" d="M394.657,171.755c0,.142-10.583.247-23.634.247s-23.633-.1-23.633-.247,10.582-.248,23.633-.248S394.657,171.621,394.657,171.755Z" transform="translate(-299.389 -115.576)"></path><path class="g" d="M384.949,175.751c0,.133-5.4.247-12.063.247s-12.064-.114-12.064-.247,5.405-.247,12.064-.247S384.949,175.608,384.949,175.751Z" transform="translate(-300.273 -115.839)"></path><path class="g" d="M414.922,363.5c0,.134-18.79.248-41.967.248s-41.968-.114-41.968-.248,18.79-.247,41.968-.247S414.922,363.4,414.922,363.5Z" transform="translate(-298.31 -128.193)"></path><path class="g" d="M414.922,374.194c0,.143-18.79.248-41.967.248s-41.968-.1-41.968-.248,18.79-.247,41.968-.247S414.922,374.061,414.922,374.194Z" transform="translate(-298.31 -128.897)"></path><path class="g" d="M414.922,385.054c0,.133-18.79.247-41.967.247s-41.968-.114-41.968-.247,18.79-.247,41.968-.247S414.922,384.912,414.922,385.054Z" transform="translate(-298.31 -129.611)"></path><path class="a" d="M348.782,198.2c.219-1.614-2.052-2.935-3.8-3.068a9.414,9.414,0,0,1-2.432-.219c-1.671-.579-2.336-2.736-3.97-3.429a4.643,4.643,0,0,0-3.8.542,9.95,9.95,0,0,1-3.677,1.425c-1.149.076-2.393-.351-3.4.209-.75.418-1.14,1.282-1.833,1.8a4.4,4.4,0,0,1-2.394.646,6.651,6.651,0,0,0-2.46.408,1.9,1.9,0,0,0-1.235,1.976Z" transform="translate(-297.572 -116.879)"></path></g><g transform="translate(234.38 359.26)"><path class="h" d="M292.525,465.554a42.863,42.863,0,0,1,40.847-11.218,74.475,74.475,0,0,1-41.36,12.919" transform="translate(-239.168 -382.114)"></path><path class="h" d="M307.373,411.469c-1.7-12.045,2.983-25.6,12-34.349.3,11.931-4.75,25.363-12,34.349" transform="translate(-240.156 -377.12)"></path><path class="h" d="M299.672,436.253a64.382,64.382,0,0,1,29.542-28.232,40.586,40.586,0,0,1-29.647,28.364" transform="translate(-239.665 -379.153)"></path><path class="h" d="M298.083,442.03c7.276.208,14.638,0,21.677-1.9s13.774-5.443,18.457-11.01a72.331,72.331,0,0,0-40.02,12.216" transform="translate(-239.567 -380.542)"></path><path class="h" d="M300.547,425.278c-3.7-13.84-9.784-23.416-20.67-31.167a28.792,28.792,0,0,0,20.67,31.167" transform="translate(-238.356 -378.238)"></path><path class="h" d="M272.642,451.677a20.5,20.5,0,0,1,12.444-19c-.608,7.143-6.222,15.417-12.444,19" transform="translate(-237.893 -380.776)"></path><path class="h" d="M267.512,438.685a30.53,30.53,0,0,0-20-26.038c2.508,11.561,9.2,21.193,20,26.038" transform="translate(-236.24 -379.458)"></path><path class="h" d="M272.847,462.092c-2.565-3.667-6.745-5.946-11.077-7.086s-8.863-1.235-13.3-1.33a31.135,31.135,0,0,0,24.08,10.031" transform="translate(-236.303 -382.157)"></path><path class="h" d="M277.157,494.161a34.2,34.2,0,0,0-41.711,19.758c15.152-2.85,30.1-9.6,41.711-19.758" transform="translate(-235.446 -384.721)"></path><path class="h" d="M297.788,483.286c-2.518,7.447-2.033,16.434,3.562,22.846a27.3,27.3,0,0,0-3.562-22.846" transform="translate(-239.452 -384.106)"></path><path class="h" d="M308.414,478.863a28,28,0,0,0,15.465,22.712,1.727,1.727,0,0,0,1.368.228c.674-.276.741-1.206.655-1.9-1.092-9.5-8.159-18.751-17.487-21" transform="translate(-240.247 -383.815)"></path><path class="h" d="M315.287,477.764a22.951,22.951,0,0,0,32.433,1.26q.568-.525,1.1-1.089c-10.657-1.169-22.959-1.9-33.532-.171" transform="translate(-240.7 -383.675)"></path><path class="g" d="M314.7,386.617a1.662,1.662,0,0,1-.1.313c-.085.219-.19.522-.323.95-.3.807-.741,1.966-1.292,3.448s-1.234,3.334-2,5.472c-.38,1.073-.8,2.213-1.235,3.429l-1.329,3.856c-1.9,5.4-3.981,11.874-6.251,19.084s-4.551,15.2-6.849,23.625-4.275,16.491-5.984,23.852-3.088,14.04-4.124,19.663c-.265,1.406-.513,2.736-.75,4.009s-.427,2.461-.618,3.582c-.379,2.241-.712,4.17-.95,5.757s-.436,2.783-.56,3.638c-.067.4-.124.713-.162.95a1.742,1.742,0,0,1-.067.323,3.1,3.1,0,0,1,0-.323l.114-.95c.115-.864.276-2.09.485-3.657s.5-3.514.874-5.766c.18-1.121.37-2.318.579-3.59s.465-2.613.722-4.019c.949-5.632,2.4-12.3,4.037-19.682s3.7-15.455,5.966-23.89,4.645-16.4,6.887-23.625,4.408-13.679,6.336-19.064l1.358-3.848c.447-1.215.864-2.355,1.263-3.419.788-2.128,1.472-3.961,2.071-5.453l1.349-3.419c.162-.38.276-.674.371-.884A1.447,1.447,0,0,1,314.7,386.617Z" transform="translate(-238.516 -377.745)"></path><path class="g" d="M254.329,419.257l.18.171.513.541c.437.466,1.083,1.178,1.9,2.119a74.6,74.6,0,0,1,6.279,8.369,56.043,56.043,0,0,1,6.564,14.04,136.878,136.878,0,0,1,3.933,18.58c2.165,13.3,4.825,25.192,6.754,33.8l2.279,10.192c.256,1.187.465,2.119.608,2.774.066.3.114.542.151.732a1.224,1.224,0,0,1,0,.247,1.636,1.636,0,0,1-.076-.238l-.19-.721c-.162-.656-.4-1.577-.694-2.755-.579-2.422-1.406-5.89-2.432-10.164-1.995-8.6-4.75-20.5-6.867-33.8a141.664,141.664,0,0,0-3.8-18.542,56.823,56.823,0,0,0-6.45-13.992,80.669,80.669,0,0,0-6.194-8.426c-.778-.95-1.406-1.682-1.833-2.176l-.475-.561C254.366,419.333,254.32,419.257,254.329,419.257Z" transform="translate(-236.688 -379.893)"></path><path class="g" d="M316.481,477.346a1.591,1.591,0,0,1-.38.066l-1.1.123-.8.077-.949.142c-.684.1-1.454.2-2.29.39l-1.329.266-1.416.351c-.949.228-2,.589-3.087.95a45.616,45.616,0,0,0-6.754,2.983,38.071,38.071,0,0,0-6.051,4.2,28.886,28.886,0,0,0-2.318,2.233,23.636,23.636,0,0,0-1.843,2.118,21.429,21.429,0,0,0-1.358,1.9,13.8,13.8,0,0,0-.95,1.482l-.57.95a1.543,1.543,0,0,1-.219.313,1.624,1.624,0,0,1,.152-.351l.494-1a12.635,12.635,0,0,1,.884-1.53,18.974,18.974,0,0,1,1.32-1.9,23.44,23.44,0,0,1,1.824-2.176,29.642,29.642,0,0,1,2.318-2.279,36.791,36.791,0,0,1,6.1-4.275,43.629,43.629,0,0,1,6.83-2.973c1.1-.323,2.128-.674,3.126-.884l1.434-.342,1.34-.237c.855-.171,1.634-.248,2.308-.323l.949-.114h1.91C316.348,477.3,316.481,477.3,316.481,477.346Z" transform="translate(-238.711 -383.713)"></path><path class="g" d="M320.479,419.531a5.4,5.4,0,0,1-.77.741l-2.194,1.9c-1.9,1.6-4.455,3.8-7.333,6.165s-5.509,4.532-7.418,6.07l-2.279,1.8a4.277,4.277,0,0,1-.865.617,5.453,5.453,0,0,1,.77-.742l2.194-1.9c1.9-1.6,4.455-3.8,7.333-6.155s5.5-4.532,7.409-6.07l2.279-1.8A5.393,5.393,0,0,1,320.479,419.531Z" transform="translate(-239.669 -379.911)"></path><path class="g" d="M322.985,434.59c0,.1-5.566,1.9-12.5,3.9s-12.539,3.695-12.615,3.543,5.558-1.852,12.5-3.9S322.947,434.514,322.985,434.59Z" transform="translate(-239.553 -380.901)"></path><path class="g" d="M301.558,427.212a5.016,5.016,0,0,1-.579-.8c-.39-.607-.884-1.358-1.472-2.25-1.255-1.9-3.031-4.475-5.13-7.229s-4.123-5.139-5.615-6.85l-1.776-1.985a5.006,5.006,0,0,1-.618-.77,3.968,3.968,0,0,1,.732.656c.456.447,1.1,1.112,1.9,1.9,1.548,1.671,3.619,4.046,5.7,6.8s3.856,5.387,5.053,7.324c.589.95,1.055,1.757,1.368,2.318A4.6,4.6,0,0,1,301.558,427.212Z" transform="translate(-238.797 -379.108)"></path><path class="g" d="M281.226,436.99a17.9,17.9,0,0,1-1.311,2.156c-.836,1.321-1.976,3.145-3.145,5.206s-2.166,3.98-2.85,5.367a17.72,17.72,0,0,1-1.187,2.232,13.54,13.54,0,0,1,.95-2.356c.646-1.425,1.6-3.362,2.774-5.443s2.365-3.885,3.268-5.168A13.735,13.735,0,0,1,281.226,436.99Z" transform="translate(-237.899 -381.059)"></path><path class="g" d="M273.3,462.57c0,.057-1.092-.466-2.849-1.255s-4.19-1.814-6.944-2.783-5.311-1.682-7.173-2.156a30.607,30.607,0,0,1-3-.8,3.6,3.6,0,0,1,.836.085c.532.086,1.292.228,2.233.428a61.537,61.537,0,0,1,7.266,2.1,62.671,62.671,0,0,1,6.935,2.9c.855.428,1.549.8,2.014,1.064A3.1,3.1,0,0,1,273.3,462.57Z" transform="translate(-236.623 -382.282)"></path><path class="g" d="M278,494.309a2.248,2.248,0,0,1-.342.123l-1.007.256-3.724.855a123.838,123.838,0,0,0-12.16,3.211,54.093,54.093,0,0,0-11.341,5.31c-1.358.865-2.413,1.625-3.126,2.176a11.765,11.765,0,0,1-1.13.836,1.984,1.984,0,0,1,.257-.266c.18-.162.427-.408.779-.693a31.746,31.746,0,0,1,3.059-2.29,49.842,49.842,0,0,1,11.4-5.481,101,101,0,0,1,12.234-3.1l3.743-.741A10.236,10.236,0,0,1,278,494.309Z" transform="translate(-236.086 -384.831)"></path><path class="g" d="M300.391,500.767c-.114,0-.779-3.913-1.491-8.767s-1.2-8.806-1.084-8.825.779,3.9,1.492,8.768S300.505,500.748,300.391,500.767Z" transform="translate(-239.549 -384.098)"></path><path class="g" d="M320.821,493.335a21.615,21.615,0,0,1-1.776-2.108c-1.064-1.33-2.546-3.154-4.209-5.139s-3.22-3.8-4.341-5.034a18.861,18.861,0,0,1-1.767-2.119,3.019,3.019,0,0,1,.561.485c.342.333.827.826,1.425,1.434,1.178,1.235,2.755,2.974,4.436,4.968s3.116,3.857,4.123,5.225c.5.684.95,1.254,1.168,1.643A2.721,2.721,0,0,1,320.821,493.335Z" transform="translate(-240.268 -383.819)"></path><path class="f" d="M265.839,513.913v9.737h2.432l8.663,27.329h19.2l8.378-27.054,3.249-.275v-9.737Z" transform="translate(-237.446 -386.121)"></path><path class="g" d="M306.307,524.615c0,.114-8.473.2-18.932.2s-18.913-.086-18.913-.2,8.473-.208,18.932-.208S306.307,524.5,306.307,524.615Z" transform="translate(-237.619 -386.811)"></path><path class="g" d="M321.6,457.571a8.2,8.2,0,0,1-1.235.333L317,458.72c-2.849.7-6.744,1.767-10.971,3.182s-7.989,2.935-10.677,4.095l-3.173,1.387a7.256,7.256,0,0,1-1.178.475,8.425,8.425,0,0,1,1.111-.617c.722-.38,1.8-.9,3.126-1.511a110.062,110.062,0,0,1,10.658-4.208,111.8,111.8,0,0,1,11.038-3.088c1.425-.313,2.593-.551,3.4-.684A7.4,7.4,0,0,1,321.6,457.571Z" transform="translate(-239.102 -382.414)"></path><path class="b" d="M300.277,540.855c0,.114-5.946.209-13.3.209s-13.3-.1-13.3-.209,5.946-.2,13.3-.2S300.277,540.741,300.277,540.855Z" transform="translate(-237.962 -387.881)"></path><path class="b" d="M300.97,538.435c0,.114-6.307.209-14.077.209s-14.077-.1-14.077-.209,6.307-.2,14.077-.2S300.97,538.3,300.97,538.435Z" transform="translate(-237.905 -387.721)"></path></g><g transform="translate(379.915 176.508)"><path class="j" d="M424.353,303.383l-9.993-5.139L400,298.52s-.475,1.9,2.47,2.85a37.726,37.726,0,0,0,8.2,1.073s-2.108-.162,2.432,3.258a16.743,16.743,0,0,0,10.193,3Z" transform="translate(-391.807 -189.178)"></path><path class="j" d="M400.638,299.353s-6.821,1.349-8.312,2.251-1.149,2.033-.76,2.384,5.234-.294,6.469-.485c.769-.114,3.676-.085,3.676-.085s-3.591,1.083-3.971,2.5,1.625,2.176,1.625,2.176-1.064,2.108-.276,3.116a2.48,2.48,0,0,0,1.682,1.054s-1.13,1.473,0,2.47,5.082,0,5.082,0,2.375.75,3.391.257,13.584-1.8,13.584-1.8l.693-8.473-19.416-4.749Z" transform="translate(-391.232 -189.251)"></path><path class="k" d="M404.888,306.746c-.171-.057-.076-.75-.1-1.577s-.142-1.52,0-1.586.6.608.618,1.568S405.049,306.812,404.888,306.746Z" transform="translate(-392.119 -189.529)"></path><path class="k" d="M399.938,308.468c0-.1.4-.171.95-.323a2.853,2.853,0,0,0,.95-.428,1.342,1.342,0,0,0,.561-.949,1.093,1.093,0,0,0-.437-.95,2.4,2.4,0,0,0-.884-.437c-.589-.152-1-.219-.95-.313s.4-.181,1.074-.134a2.362,2.362,0,0,1,1.158.428,1.6,1.6,0,0,1,.713,1.434,1.9,1.9,0,0,1-.865,1.34,2.613,2.613,0,0,1-1.158.39C400.337,308.639,399.947,308.553,399.938,308.468Z" transform="translate(-391.805 -189.617)"></path><path class="k" d="M399.938,308.693a3.422,3.422,0,0,1,1.586-.333,4.282,4.282,0,0,1,1.786.228,4.37,4.37,0,0,1,.95.5,2.651,2.651,0,0,1,.75.95,2.035,2.035,0,0,1,.209,1.263,1.684,1.684,0,0,1-.722.95,4.434,4.434,0,0,1-1.7.636c-1.016.171-1.634,0-1.625-.076s.617-.1,1.52-.362a4.879,4.879,0,0,0,1.463-.674,1.151,1.151,0,0,0,.456-.636,1.543,1.543,0,0,0-.171-.845,2.2,2.2,0,0,0-.561-.75,4.479,4.479,0,0,0-.807-.447,4.618,4.618,0,0,0-1.577-.333C400.6,308.769,399.947,308.8,399.938,308.693Z" transform="translate(-391.805 -189.842)"></path><path class="k" d="M403.541,316.058c0-.076.456-.123,1.13-.5l.561-.294a2.583,2.583,0,0,0,.57-.314c.219-.208.086-.513-.475-.788a10.012,10.012,0,0,0-2.792-.845c-.742-.1-1.226-.066-1.244-.162s.456-.275,1.272-.285a7.06,7.06,0,0,1,3.078.742,3.143,3.143,0,0,1,.4.256,1.08,1.08,0,0,1,.379.437.892.892,0,0,1-.237,1.092,2.63,2.63,0,0,1-.732.351l-.608.228C404.083,316.267,403.541,316.163,403.541,316.058Z" transform="translate(-391.894 -190.14)"></path><path class="k" d="M405.048,308.209c0,.18-.57.3-1.235.427s-1.226.209-1.292,0,.437-.532,1.178-.674S405.057,308.038,405.048,308.209Z" transform="translate(-391.974 -189.814)"></path><path class="k" d="M409.936,313.583c-.191,0,.522-2.337-.542-4.9a6.957,6.957,0,0,0-.807-1.492l-.077-.114v-.123a4.222,4.222,0,0,0-.1-2.232c-.181-.542-.362-.836-.3-.884s.342.19.636.741a3.8,3.8,0,0,1,.352,2.5l-.048-.237a6.33,6.33,0,0,1,.95,1.6,7.044,7.044,0,0,1,.465,3.7C410.306,313.08,410,313.6,409.936,313.583Z" transform="translate(-392.341 -189.545)"></path><path class="k" d="M417.752,310.4c0,.076-.523.162-1.349,0a6.9,6.9,0,0,1-2.964-1.358,7.005,7.005,0,0,1-2.013-2.565c-.333-.769-.371-1.292-.3-1.311a10.7,10.7,0,0,0,2.707,3.372C415.748,310.014,417.78,310.185,417.752,310.4Z" transform="translate(-392.539 -189.633)"></path><path class="k" d="M420.149,308.65a2.143,2.143,0,0,1-.864-.123,13.391,13.391,0,0,1-2.233-.769,16.217,16.217,0,0,1-6.212-4.854l.276.123h-.836a19.118,19.118,0,0,1-8.264-1.711,4.311,4.311,0,0,1-1.9-1.767,1.324,1.324,0,0,1-.2-.95,5.446,5.446,0,0,0,.418.808,4.392,4.392,0,0,0,1.9,1.482,21.123,21.123,0,0,0,8.055,1.482h.979l.1.133a17.284,17.284,0,0,0,5.889,4.835C418.991,308.251,420.178,308.546,420.149,308.65Z" transform="translate(-391.802 -189.201)"></path><path class="h" d="M519.943,521.409l.751,19.131s-19.2,7.191-19.378,10.743l37.151-.123.047-29.865Z" transform="translate(-398.475 -203.854)"></path><path class="d" d="M534.226,540.123a1.52,1.52,0,0,1,1.055,1.728,1.453,1.453,0,0,1-1.7,1.064,1.6,1.6,0,0,1-1.112-1.833,1.53,1.53,0,0,1,1.9-.95" transform="translate(-400.523 -205.089)"></path><path class="g" d="M538.445,553.052l-.114-3.011-35.584,1.244s-1.643.722-1.454,1.9Z" transform="translate(-398.473 -205.746)"></path><path class="g" d="M521.418,541.682c0,.18.95.265,1.8.846s1.378,1.339,1.539,1.263-.1-1.112-1.2-1.8S521.381,541.52,521.418,541.682Z" transform="translate(-399.798 -205.185)"></path><path class="g" d="M517.249,543.387c0,.18.751.484,1.368,1.254s.8,1.577.95,1.568.294-1.036-.493-1.976S517.249,543.253,517.249,543.387Z" transform="translate(-399.524 -205.304)"></path><path class="g" d="M514.8,548.252c.162,0,.409-.864-.057-1.9s-1.32-1.377-1.4-1.235.456.684.826,1.5S514.617,548.2,514.8,548.252Z" transform="translate(-399.266 -205.42)"></path><path class="h" d="M480.794,521.013l.75,19.141s-19.2,7.19-19.407,10.734l37.161-.123.048-29.865Z" transform="translate(-395.897 -203.828)"></path><path class="d" d="M495.089,539.708a1.509,1.509,0,0,1,1.054,1.728,1.452,1.452,0,0,1-1.7,1.064,1.6,1.6,0,0,1-1.112-1.842,1.51,1.51,0,0,1,1.9-.9" transform="translate(-397.948 -205.064)"></path><path class="g" d="M499.3,552.625l-.114-3.011-35.593,1.244s-1.653.732-1.463,1.9Z" transform="translate(-395.896 -205.718)"></path><path class="g" d="M482.27,541.264c0,.18.95.256,1.8.836s1.369,1.348,1.54,1.272-.1-1.121-1.207-1.8S482.223,541.093,482.27,541.264Z" transform="translate(-397.222 -205.157)"></path><path class="g" d="M478.1,543.01c0,.18.742.484,1.369,1.244s.8,1.576.95,1.567.285-1.035-.494-1.966S478.1,542.838,478.1,543.01Z" transform="translate(-396.948 -205.278)"></path><path class="g" d="M475.65,547.794c.161,0,.408-.874-.057-1.9s-1.321-1.387-1.4-1.235.447.684.826,1.5S475.469,547.775,475.65,547.794Z" transform="translate(-396.691 -205.389)"></path><path class="g" d="M508.21,392.866c-5.7,1.329-6.649,71.691-6.649,71.691l-.152,63.92-22.01.19s-3.211-28.022-4.446-55.636c-.8-17.839-1.9-33.153-1.9-33.153S470.86,416.8,470.7,397.511s1.9-46.422,1.9-46.422l32.715,7.656Z" transform="translate(-396.46 -192.655)"></path><path class="g" d="M541.158,366.965s1.12,20.138,1.529,25.173-2.394,136.1-2.394,136.1H518.786s-7.856-75.424-8.245-87.393-4.426-45.681-4.426-45.681V366.006l33.94,1.13" transform="translate(-398.791 -193.636)"></path><path class="f" d="M504.789,376.3a14.677,14.677,0,0,0-.2,2.707c0,1.682,0,4,.19,6.554s.513,4.863.8,6.516a13.922,13.922,0,0,0,.608,2.65,19.084,19.084,0,0,0-.285-2.7c-.2-1.663-.465-3.962-.656-6.5s-.285-4.853-.322-6.525A19.139,19.139,0,0,0,504.789,376.3Z" transform="translate(-398.691 -194.313)"></path><path class="f" d="M531.726,374.73a5.32,5.32,0,0,0,.845,1.767,13.3,13.3,0,0,0,7.5,5.558,5.056,5.056,0,0,0,1.9.294,16.4,16.4,0,0,1-10.287-7.6Z" transform="translate(-400.474 -194.21)"></path><path class="f" d="M474.075,386.626a6.852,6.852,0,0,0,2.261-.475,15.206,15.206,0,0,0,8.4-7.058,6.664,6.664,0,0,0,.864-2.147c-.076,0-.408.788-1.14,1.985a17.657,17.657,0,0,1-3.562,4.113,17.372,17.372,0,0,1-4.674,2.792C474.92,386.36,474.066,386.551,474.075,386.626Z" transform="translate(-396.683 -194.356)"></path><path class="f" d="M510.763,375.592a.7.7,0,0,0-.323-.228,1.612,1.612,0,0,0-1.092,0,1.8,1.8,0,0,0-1.14,1.177,1.823,1.823,0,0,0,2.754,1.9,1.842,1.842,0,0,0,.7-1.482,1.659,1.659,0,0,0-.362-1.035c-.171-.191-.313-.238-.322-.219a2.2,2.2,0,0,1,.351,1.244,1.531,1.531,0,0,1-.617,1.149,1.435,1.435,0,0,1-2.1-1.454,1.57,1.57,0,0,1,.855-.95A2.168,2.168,0,0,1,510.763,375.592Z" transform="translate(-398.927 -194.246)"></path><path class="f" d="M517.514,463.062a13.462,13.462,0,0,0,1.9,2.042,56.52,56.52,0,0,0,10.6,8.1,14.5,14.5,0,0,0,2.461,1.264,21.025,21.025,0,0,0-2.29-1.549c-1.425-.95-3.372-2.251-5.433-3.8s-3.8-3.106-5.12-4.246A20.936,20.936,0,0,0,517.514,463.062Z" transform="translate(-399.541 -200.023)"></path><path class="f" d="M482.047,455.791a14.106,14.106,0,0,0,1.7,2.119c1.1,1.263,2.688,2.954,4.522,4.75s3.591,3.306,4.882,4.369a14.409,14.409,0,0,0,2.176,1.625,16.426,16.426,0,0,0-1.957-1.9c-1.244-1.13-2.945-2.688-4.749-4.454s-3.448-3.42-4.607-4.617A17.488,17.488,0,0,0,482.047,455.791Z" transform="translate(-397.208 -199.544)"></path><path class="j" d="M543.873,375.279l-1.084,3.391a34.323,34.323,0,0,0-4.474,2.365c-.57.579-2.916,6.146-2.916,6.146s.351,4.825.712,5.044,1.415.637,1.691-.893.437-4.132.437-4.132l2.555-2.327.618,3.429-1.653,5.13s-3.724.949-3.753,1.795a1.33,1.33,0,0,0,1.093,1.531,1.3,1.3,0,0,0,.171.018,18.736,18.736,0,0,0,2.849-.114s.228,2.242,2.185,1.785l1.956-.465s.694,1.985,1.9,2.014a2.5,2.5,0,0,0,1.9-1.035l3.866-5.7a15.8,15.8,0,0,0,2.166-4.826l2.289-8.863Z" transform="translate(-400.718 -194.247)"></path><path class="j" d="M540.5,385.745a5.522,5.522,0,0,1,2.09-1.739c.617-.294,1.492-.408,1.9.152a1.113,1.113,0,0,1,0,1.14,2.284,2.284,0,0,1-.855.816c.56,0,1.215.076,1.482.561a1.083,1.083,0,0,1-.342,1.292,3.107,3.107,0,0,1-1.321.57c.428-.142.836.38.76.817a1.8,1.8,0,0,1-.789,1.054,3.722,3.722,0,0,1-1.7.864,1.538,1.538,0,0,1-1.643-.75.948.948,0,0,1,.817-1.386,1.113,1.113,0,0,1-1.226-.76,2.024,2.024,0,0,1-.076-1.6,3.884,3.884,0,0,1,.9-1.026" transform="translate(-400.986 -194.805)"></path><path class="k" d="M540.486,385.733a11.474,11.474,0,0,0-.741.949,1.464,1.464,0,0,0-.048,1.292,1.443,1.443,0,0,0,.38.713.846.846,0,0,0,.76.228v.38a.789.789,0,0,0-.655.949,1.2,1.2,0,0,0,1.235.779,2.987,2.987,0,0,0,1.691-.76c.55-.38,1.083-.95.674-1.415a.265.265,0,0,0-.3-.114l-.133-.476c.769-.171,1.5-.493,1.577-1.14a.607.607,0,0,0-.39-.655,2.206,2.206,0,0,0-.95-.162h-.788l.674-.4a1.77,1.77,0,0,0,.884-.95.741.741,0,0,0-.4-.95,1.757,1.757,0,0,0-1.083,0,5.428,5.428,0,0,0-.95.437,10.9,10.9,0,0,0-1.51,1.263,1.992,1.992,0,0,1,.276-.456,4.827,4.827,0,0,1,1.092-1.026,2.65,2.65,0,0,1,2.27-.589,1.082,1.082,0,0,1,.6.532,1.3,1.3,0,0,1,.076.826,2.163,2.163,0,0,1-1.055,1.273l-.123-.418a2.98,2.98,0,0,1,1.15.19,1.1,1.1,0,0,1,.664,1.14,1.576,1.576,0,0,1-.741,1.112,4.24,4.24,0,0,1-1.187.447l-.142-.476a.79.79,0,0,1,.836.266,1.1,1.1,0,0,1,.256.656,1.3,1.3,0,0,1-.19.655,3.2,3.2,0,0,1-.827.817,3.433,3.433,0,0,1-1.985.836,1.636,1.636,0,0,1-1.663-1.169,1.166,1.166,0,0,1,1.1-1.415l1.739.076-1.71.3a1.386,1.386,0,0,1-1.519-1.234,1.653,1.653,0,0,1,.161-1.492,3.079,3.079,0,0,1,.646-.684C540.381,385.771,540.476,385.714,540.486,385.733Z" transform="translate(-400.969 -194.793)"></path><path class="k" d="M546.979,391.111a29.535,29.535,0,0,1-2.251,4.617,3.569,3.569,0,0,1-1.263,1.653,3.152,3.152,0,0,1-1.691.19,5.629,5.629,0,0,0-1.064,0c-.247,0-.38.1-.4.057s.1-.123.351-.218a3.73,3.73,0,0,1,1.121-.152,3.018,3.018,0,0,0,1.482-.237c.475-.248.731-.874,1.073-1.5A29.429,29.429,0,0,1,546.979,391.111Z" transform="translate(-401.041 -195.288)"></path><path class="k" d="M549.359,394.243a6.532,6.532,0,0,1-1.691,3.3,4.747,4.747,0,0,1-2.127,1.634c-.7.191-1.141-.057-1.112-.1a4.538,4.538,0,0,0,1.016-.19,5.605,5.605,0,0,0,1.9-1.625C548.627,395.734,549.235,394.2,549.359,394.243Z" transform="translate(-401.312 -195.494)"></path><path class="k" d="M544,386.241a14.773,14.773,0,0,1-2.3.607,16.4,16.4,0,0,1-2.261.742,3.8,3.8,0,0,1,2.119-1.216A3.848,3.848,0,0,1,544,386.241Z" transform="translate(-400.984 -194.959)"></path><path class="k" d="M543.97,388.678c.048.133-.674.541-1.653.807s-1.8.276-1.824.143.75-.362,1.7-.627S543.923,388.555,543.97,388.678Z" transform="translate(-401.053 -195.126)"></path><path class="h" d="M539.378,249.976s9.5,4.312,13.518,17.355,14.248,62.638,11.541,78.616c0,0-6.126,24.555-7.106,25.4l-13.84-4.863-7.542-89.738Z" transform="translate(-400.754 -186.002)"></path><path class="h" d="M485.235,248.508l23.986-4.237s24.147,3.154,25.866,5.33,2.7,85.16,3.439,89.558c.95,5.519,0,23.548,0,23.548l-67.14,2.165-.646-11.921,1.121-97.984Z" transform="translate(-396.464 -185.626)"></path><path class="g" d="M532.918,266a.737.737,0,0,1-.066.247,6.594,6.594,0,0,0-.151.741c-.067.3-.152.731-.238,1.2s-.133,1.044-.208,1.662a41.886,41.886,0,0,0,.3,10.62,126.132,126.132,0,0,0,3.514,15.38,137.974,137.974,0,0,1,4.076,18.818c.949,6.773,1.453,13.3,1.719,19.2s.38,11.266.447,15.75.1,8.084.133,10.629c0,1.226.048,2.194.066,2.85v.75a1.457,1.457,0,0,0,0,.256.736.736,0,0,0,0-.256v-14.23c0-4.494-.085-9.832-.342-15.768s-.741-12.454-1.7-19.245a133.734,133.734,0,0,0-4.113-18.865,128.044,128.044,0,0,1-3.6-15.313,43.584,43.584,0,0,1-.447-10.554c.057-.627.114-1.177.162-1.662s.142-.864.2-1.2l.114-.75A1.031,1.031,0,0,0,532.918,266Z" transform="translate(-400.498 -187.056)"></path><path class="g" d="M486.246,332.908a2.388,2.388,0,0,0,.228.342l.732.95c.627.807,1.586,1.9,2.85,3.277s2.783,2.9,4.579,4.522a74.584,74.584,0,0,0,6.136,4.949,72.571,72.571,0,0,0,6.65,4.255,62.271,62.271,0,0,0,5.747,2.907c1.682.741,3.04,1.311,4.009,1.653l1.112.39c.256.085.39.123.4.1a1.83,1.83,0,0,0-.371-.191l-1.073-.465c-.95-.39-2.3-.95-3.942-1.777s-3.581-1.785-5.7-2.982a74.958,74.958,0,0,1-6.582-4.256c-2.261-1.614-4.313-3.305-6.127-4.891s-3.362-3.116-4.626-4.427-2.261-2.413-2.925-3.182l-.788-.874A1.8,1.8,0,0,0,486.246,332.908Z" transform="translate(-397.484 -191.459)"></path><path class="g" d="M485.534,298.916a2.14,2.14,0,0,0,.1.446c.1.323.209.731.351,1.235.3,1.064.817,2.6,1.5,4.475s1.6,4.066,2.7,6.459,2.45,4.968,4,7.6a81.024,81.024,0,0,0,4.749,7.172c1.586,2.137,3.04,3.98,4.351,5.49s2.393,2.688,3.191,3.476l.95.95a1.589,1.589,0,0,0,.333.3,1.865,1.865,0,0,0-.276-.352l-.845-.95c-.75-.826-1.8-2.042-3.077-3.571s-2.718-3.391-4.256-5.519-3.116-4.559-4.664-7.162-2.85-5.177-4.009-7.543-2.052-4.55-2.775-6.4-1.272-3.363-1.634-4.418l-.427-1.206A1.731,1.731,0,0,0,485.534,298.916Z" transform="translate(-397.437 -189.222)"></path><g class="i" transform="translate(131.345 96.68)"><path d="M532.429,284.985c8.7,24.252,11.019,50.346,9.832,76.041-3.229-3.116-4.3-7.894-4.265-12.349s1.016-8.92,1.206-13.4C539.9,318.232,529.227,301.713,532.429,284.985Z" transform="translate(-531.828 -284.985)"></path></g><g class="i" transform="translate(89.413 113.736)"><path d="M486.966,303.276c-.276-.779,1.9,12.462,4.93,17.441A36.161,36.161,0,0,0,504.17,332.9S492.771,319.823,486.966,303.276Z" transform="translate(-486.943 -303.243)"></path></g><path class="h" d="M461.738,262.183c-1.653,4.066-9.185,41.008-9.185,41.008l-27.472-7.163s-6.764,18.685-7.675,18.352,35.641,15.037,42.813,14.515c7.011-.5,9.423-10.9,9.423-10.9l10.953-67.719S465.272,253.462,461.738,262.183Z" transform="translate(-392.953 -186.021)"></path><path class="g" d="M470.739,278.152a2.706,2.706,0,0,1,.066.513,12.817,12.817,0,0,0,.066,1.491c.048,1.292.1,3.164.133,5.472.077,4.617.134,10.991.162,18.049s0,13.432,0,18.049c0,2.251,0,4.1-.057,5.472v1.482a2.221,2.221,0,0,1,0,.522,1.931,1.931,0,0,1-.057-.522c0-.39,0-.884-.048-1.482,0-1.369-.057-3.211-.1-5.472,0-4.683-.086-11.029-.143-18.049s0-13.355,0-18.049v-6.953A1.994,1.994,0,0,1,470.739,278.152Z" transform="translate(-396.463 -187.856)"></path><path class="j" d="M509.647,245.655h0a5.75,5.75,0,0,0,.522-2.85l-2.564-37.1c0-10.363-5.956-19-16.31-18.79l-.95.086a18.524,18.524,0,0,0-17.023,18.514c0,8.986.143,18.9.589,23.69.95,9.9,10.971,10.62,10.971,10.62l.475,3.857s-.514,6.735,7.352,8.72c6.042,1.52,12.453-2.5,15.094-4.436A5.837,5.837,0,0,0,509.647,245.655Z" transform="translate(-396.633 -181.852)"></path><path class="k" d="M485.616,243.156a24.142,24.142,0,0,0,12.672-4.874s-2.47,7.39-12.434,7.305Z" transform="translate(-397.442 -185.232)"></path><path class="g" d="M476.546,215.967a1.434,1.434,0,0,0,1.482,1.32,1.359,1.359,0,0,0,1.359-1.357c0-.013,0-.026,0-.039a1.434,1.434,0,0,0-1.482-1.32,1.368,1.368,0,0,0-1.359,1.377Z" transform="translate(-396.846 -183.672)"></path><path class="g" d="M476.024,211.652c.191.171,1.2-.713,2.717-.827s2.707.551,2.85.342-.134-.4-.656-.722a3.855,3.855,0,0,0-2.3-.5,3.73,3.73,0,0,0-2.147.893C476.062,211.234,475.9,211.576,476.024,211.652Z" transform="translate(-396.809 -183.366)"></path><path class="g" d="M491.534,215.123a1.434,1.434,0,0,0,1.482,1.32,1.367,1.367,0,0,0,1.358-1.377v-.02a1.434,1.434,0,0,0-1.482-1.32,1.358,1.358,0,0,0-1.359,1.357C491.533,215.1,491.533,215.109,491.534,215.123Z" transform="translate(-397.832 -183.616)"></path><path class="g" d="M492.071,210.645c.19.171,1.2-.713,2.726-.836s2.7.551,2.849.351-.133-.408-.646-.721a3.9,3.9,0,0,0-2.308-.5,3.707,3.707,0,0,0-2.147.893C492.156,210.218,491.976,210.57,492.071,210.645Z" transform="translate(-397.866 -183.3)"></path><path class="g" d="M486.872,222.447a9.322,9.322,0,0,0-2.507-.247c-.4,0-.77-.057-.855-.314a2,2,0,0,1,.161-1.178c.3-.949.589-1.985.95-3.049,1.244-4.351,2.119-7.913,1.9-7.96s-1.33,3.429-2.564,7.761l-.865,3.068a2.31,2.31,0,0,0-.076,1.549,1.019,1.019,0,0,0,.693.513,2.829,2.829,0,0,0,.665,0A10.46,10.46,0,0,0,486.872,222.447Z" transform="translate(-397.265 -183.351)"></path><path class="g" d="M491.724,224.108c-.247,0-.114,1.662-1.434,2.945s-3.1,1.254-3.088,1.482.418.294,1.159.257a4.159,4.159,0,0,0,2.583-1.15,3.571,3.571,0,0,0,1.1-2.46C492.056,224.45,491.838,224.108,491.724,224.108Z" transform="translate(-397.547 -184.299)"></path><path class="g" d="M491.146,204.311c.191.4,1.691.085,3.5.152s3.287.436,3.5.047c.1-.19-.19-.561-.807-.949a6.126,6.126,0,0,0-5.319-.152C491.355,203.769,491.051,204.12,491.146,204.311Z" transform="translate(-397.805 -182.902)"></path><path class="g" d="M476.237,206.888c.294.323,1.311-.123,2.574-.247s2.347,0,2.565-.342c.094-.181-.1-.514-.6-.808a3.8,3.8,0,0,0-4.141.476C476.218,206.356,476.1,206.708,476.237,206.888Z" transform="translate(-396.822 -183.048)"></path><path class="j" d="M509.155,217.267a2.708,2.708,0,0,1,2.562-2.846l.136,0c1.9,0,4,.9,4.2,4.75.333,6.792-6.554,5.7-6.573,5.538S509.278,220.107,509.155,217.267Z" transform="translate(-398.991 -183.662)"></path><path class="k" d="M510.944,222.228a1.619,1.619,0,0,0,.323.162,1.148,1.148,0,0,0,.883,0,2.9,2.9,0,0,0,1.226-2.689,3.818,3.818,0,0,0-.418-1.682,1.371,1.371,0,0,0-.95-.864.608.608,0,0,0-.684.362c-.077.19,0,.332-.067.342s-.151-.1-.114-.39a.8.8,0,0,1,.3-.494.951.951,0,0,1,.646-.209,1.653,1.653,0,0,1,1.292.95,4.006,4.006,0,0,1,.5,1.9c0,1.368-.608,2.678-1.577,3.011a1.253,1.253,0,0,1-1.092-.123C510.963,222.4,510.916,222.247,510.944,222.228Z" transform="translate(-399.109 -183.816)"></path><path class="g" d="M472.9,204.823c-.047,1.377-1.985-3.2-1.32-4.408a3.581,3.581,0,1,1-1.254-6.992,3.209,3.209,0,0,1,.447-3.913,4.737,4.737,0,0,1,1.976-.865,4.948,4.948,0,0,1,.475-2.441,6.023,6.023,0,0,1,9.271-2.108,5.975,5.975,0,0,1,9.86,0,5.7,5.7,0,0,1,8.387,2.507,4.437,4.437,0,0,1,5.311.608,15.733,15.733,0,0,0,1.548,1.8,14.357,14.357,0,0,0,2.518,1.13,4.825,4.825,0,0,1,1.822,6.576,4.742,4.742,0,0,1-.435.644,11.25,11.25,0,0,1,2.632,2.479,3.8,3.8,0,0,1,.589,3.448c-.522,1.358-2.014,2.156-2.527,3.524-.228.627-.228,1.32-.456,1.957a3.867,3.867,0,0,1-2.3,2.213c-.542,2.764-2.413,3.581-2.442,4.389-.237,8.473-3.9,3.695-6.336-14.049-.57-4.17.884-6.365-2.518-8.806a16.317,16.317,0,0,0-1.149,1.71,5.633,5.633,0,0,1-7.906.975c-.144-.113-.282-.232-.415-.358a4.837,4.837,0,0,1-7.7,1.073c-1.178,1.14-3.952,3.088-6.174,1.206A18.176,18.176,0,0,0,472.9,204.823Z" transform="translate(-396.212 -181.496)"></path></g><g transform="translate(219.238 523.843)"><path class="g" d="M649.237,553.542c0,.133-96.265.247-214.986.247s-215.014-.085-215.014-.247,96.246-.247,215.014-.247S649.237,553.428,649.237,553.542Z" transform="translate(-219.238 -553.295)"></path></g><text class="l" transform="translate(352.825 153.173)"><tspan x="0" y="0">PMS</tspan></text><text class="m" transform="translate(344.013 319.497)"><tspan x="0" y="0">MANAGE</tspan></text><g transform="translate(518.334 133.331)"><path class="h" d="M566.913,135.355a2.327,2.327,0,0,1,.484,0h1.426a15.3,15.3,0,0,1,2.3.219c.456.077.95.123,1.463.228l1.643.408a26.614,26.614,0,0,1,7.9,3.534,27.48,27.48,0,0,1-4.731,48.275,26.817,26.817,0,0,1-9.157,2.014,26.216,26.216,0,0,1-9.575-1.187l-.285-.1h.218L542.6,194.3l-.646.228.2-.664,4.161-13.3.066.323a27.414,27.414,0,0,1-6.422-23.748,26.924,26.924,0,0,1,4.132-9.822,27.3,27.3,0,0,1,6.337-6.65,24.587,24.587,0,0,1,6.507-3.6,21.1,21.1,0,0,1,2.849-.95,17.969,17.969,0,0,1,2.47-.513c.75-.1,1.415-.219,1.985-.266l1.463-.048h.941a.973.973,0,0,1,.3,0,2.093,2.093,0,0,1-.3.048l-.884.066-1.443.1c-.57.066-1.226.19-1.966.314a17.875,17.875,0,0,0-2.432.56,20.807,20.807,0,0,0-2.85.95,31.543,31.543,0,0,0-3.125,1.472,28.311,28.311,0,0,0-3.249,2.157,27.418,27.418,0,0,0-6.174,6.564,26.5,26.5,0,0,0-3.99,9.7,26.845,26.845,0,0,0,6.365,23.149l.133.142-.057.18q-1.984,6.412-4.132,13.3l-.447-.428,15.987-5.4h.228l.276.085a25.7,25.7,0,0,0,9.329,1.159,26.386,26.386,0,0,0,8.929-1.948,26.962,26.962,0,0,0-2.983-50.868l-1.615-.437c-.513-.114-1-.171-1.443-.256a14.99,14.99,0,0,0-2.28-.285L567.4,135.5A3.122,3.122,0,0,1,566.913,135.355Z" transform="translate(-539.401 -135.277)"></path><path class="h" d="M551.626,165.749l4.208-4.218,8.122,7.219L579.9,153.41l3.9,4.218-20.452,19.245Z" transform="translate(-540.205 -136.47)"></path></g><g transform="translate(515.109 197.487)"><path class="n" d="M599.405,540.307l1.381-6.947-18.013-1.689-.93,10.133,6.322,3.858Z" transform="translate(-538.969 -225.516)"></path><path class="n" d="M664.585,519.2l-1.932,29.873-13.721-1.752-1.416-29.281Z" transform="translate(-543.29 -224.619)"></path><path class="h" d="M648.731,543.082s9.313,7.262,14.335,1.725l1.07,2.881L668.5,568.04a2.666,2.666,0,0,1-1.74,3.128,2.7,2.7,0,0,1-2.79-.681c-3.778-3.926-17.617-18.434-17.354-20.281l.342-4.913a2.509,2.509,0,0,1,1.771-2.21Z" transform="translate(-543.231 -226.267)"></path><g class="o" transform="translate(103.394 322.224)"><path class="e" d="M666.28,571.66l-19.569-22.789-.051,1.074a2.972,2.972,0,0,0,.845,2.435c1.553,2.3,5.933,7.518,16.39,18.48a2.725,2.725,0,0,0,2.385.826Z" transform="translate(-646.625 -548.871)"></path></g><g class="o" transform="translate(104.236 318.836)"><path class="e" d="M650.008,545.61a1.557,1.557,0,0,1,.329,2.08,1.583,1.583,0,0,1-2.115.33,1.506,1.506,0,1,1,1.9-2.307" transform="translate(-647.527 -545.245)"></path></g><path class="g" d="M661.917,560.869a8.86,8.86,0,0,1,2.392-1.849,9.683,9.683,0,0,1,3.031-.4c.04-.157-.3-.281-.872-.392a4.255,4.255,0,0,0-2.358.273,4.119,4.119,0,0,0-1.836,1.468C661.919,560.47,661.84,560.844,661.917,560.869Z" transform="translate(-544.236 -227.259)"></path><path class="g" d="M663.75,564.683c.123.12,1-.657,2.364-.965a24.828,24.828,0,0,1,2.593-.182c.084-.14-1.146-.733-2.7-.362S663.584,564.609,663.75,564.683Z" transform="translate(-544.357 -227.581)"></path><path class="g" d="M658.474,556.008a27.867,27.867,0,0,1,3.545-1.73,34.4,34.4,0,0,1,3.91-.671c.014-.174-1.862-.592-4.092.137A6.014,6.014,0,0,0,658.474,556.008Z" transform="translate(-544.01 -226.941)"></path><path class="g" d="M658.356,549.579c.063.164,1.535-.346,3.405-.317s3.341.55,3.4.4-1.374-.946-3.391-.956A5.436,5.436,0,0,0,658.356,549.579Z" transform="translate(-544.003 -226.637)"></path><path class="g" d="M662.858,548.983a2.1,2.1,0,0,0-.093-.959,6.541,6.541,0,0,0-1.114-2.4,3.007,3.007,0,0,0-1.475-1.247,1.081,1.081,0,0,0-1.171.383,1.673,1.673,0,0,0-.257,1.19,3.765,3.765,0,0,0,3.728,2.966,4.324,4.324,0,0,0,3.978-2.741,1.435,1.435,0,0,0-.09-1.228,1.065,1.065,0,0,0-1.166-.4,3.656,3.656,0,0,0-1.592,1.074,7.319,7.319,0,0,0-1.5,2.171,2.212,2.212,0,0,0-.279.922A11.777,11.777,0,0,1,663.9,545.9a3.458,3.458,0,0,1,1.4-.888.579.579,0,0,1,.635.214,1.015,1.015,0,0,1,.014.814,3.894,3.894,0,0,1-3.475,2.343,3.288,3.288,0,0,1-3.209-2.482,1.175,1.175,0,0,1,.15-.868.581.581,0,0,1,.636-.219,2.733,2.733,0,0,1,1.272,1.026A9.642,9.642,0,0,1,662.858,548.983Z" transform="translate(-544.028 -226.349)"></path><path class="g" d="M661.291,333.654c.011.641,7.068,23.111,7.068,23.111s11.177,21.4,7.905,34.815c-1.807,7.4-9.732,53.605-9.732,53.605s-2.717,35.174-4.35,50.348-2.207,23.309-2.207,23.309l-16.58-.39-5.027-67.664-2.119-53.164-20.9,67.328s-4.352,20.766-6.767,29.539-7.892,27.454-7.892,27.454L582.764,519.6s1.384-32.59,2.957-40.51,6.749-36.35,6.749-36.35l4.549-27.623,4.614-44.917,4.375-17.313,5.471-19.591Z" transform="translate(-539.029 -212.462)"></path><path class="n" d="M568.938,305.645s-.678-2.256-2.153-3.019-7.978-.394-9.353-.372-5.8-1.555-7.787.131a1.342,1.342,0,0,0,.89,1.675,1.323,1.323,0,0,0,.16.038c2.388.436,6.807,1.288,6.613,1.62s-7.735-.381-9.284.156l-11.182-.081s-1.934,1.85-.1,2.537l.024,1.48a5.059,5.059,0,0,0,1.944.832c1.469.418-.073,1.317,1.854,1.7a16.936,16.936,0,0,0,3.952.289l.2.862a6.4,6.4,0,0,0,1.931.574c1.563.329,18.957-.318,18.957-.318l8.933,2.672,1.134-8.777Z" transform="translate(-535.949 -210.377)"></path><path class="k" d="M566.895,305.529a24.2,24.2,0,0,0-4.3,1.69,8.325,8.325,0,0,0-2.388,2.143,12.863,12.863,0,0,1-.76,1.138,2.591,2.591,0,0,1,.5-1.306,7.077,7.077,0,0,1,2.409-2.369,9.86,9.86,0,0,1,3.157-1.186A3.632,3.632,0,0,1,566.895,305.529Z" transform="translate(-537.495 -210.634)"></path><path class="k" d="M552.723,312.286a17.221,17.221,0,0,1-3.9.325,8.86,8.86,0,0,0-2.661.226,2.6,2.6,0,0,0-.854.663c-.07,0-.009-.562.693-.946a6.735,6.735,0,0,1,2.789-.392A16.98,16.98,0,0,1,552.723,312.286Z" transform="translate(-536.563 -211.068)"></path><path class="k" d="M551.757,309.66a7.46,7.46,0,0,1-1.793.4,20.573,20.573,0,0,1-4.462.274,11.54,11.54,0,0,0-4.323.271,2.689,2.689,0,0,0-1.115.6c-.2.211-.175.4-.218.4s-.125-.206.078-.512a2.514,2.514,0,0,1,1.146-.789,6.548,6.548,0,0,1,1.993-.44,21.723,21.723,0,0,1,2.466.02,28.3,28.3,0,0,0,4.394-.143A7.669,7.669,0,0,1,551.757,309.66Z" transform="translate(-536.202 -210.906)"></path><path class="k" d="M549.986,307.628a8.88,8.88,0,0,1-1.944.188c-1.2.021-2.862.108-4.7.139-.917.016-1.793-.048-2.6-.025a11.42,11.42,0,0,0-2.065.164,3.259,3.259,0,0,0-1.291.419c-.257.177-.323.36-.358.344s0-.225.251-.481a2.953,2.953,0,0,1,1.332-.6,10.165,10.165,0,0,1,2.124-.261c.8-.013,1.68.024,2.6-.043,1.825-.031,3.461-.058,4.682-.009A8.089,8.089,0,0,1,549.986,307.628Z" transform="translate(-536.019 -210.761)"></path><path class="n" d="M637.448,267.29,629.1,277.875l2,4.276,10.058.576,10.268-1.868,5.7-2.155,3.527-9.527-1.267-7.448-4.012.232Z" transform="translate(-542.078 -207.754)"></path><path class="e" d="M628.3,278.054s-.627,3.4,5.529,3.5,21.934-3.958,21.934-3.958l13.29,22.548-13.2,29.966-42.666-.462-.124-7.433s-3.755-2.481-4.284-10.314a32.693,32.693,0,0,1,1.733-13.331Z" transform="translate(-540.734 -208.797)"></path><path class="h" d="M630.185,265.2s-11.733,2.212-13.192,2.973-7.231,7.477-8.988,12.1-3.2,15.866-3.2,15.866-4.483,6.7-4.465,7.795l.049,2.941s-3.372,5.388-3.362,5.942l.009.554-22.893-12.487-8.316,15.58s20.139,13.917,29.753,19.555a5.529,5.529,0,0,0,7.32-1.6h0l-6.71,46.847,4.7.658,9.573-53.231-.125-7.432s-7.684-7.66-1.493-18.349c2.154-3.705,8.425-13.746,11.8-18.762s11.99-15.857,11.99-15.857l-.052-5.729Z" transform="translate(-537.915 -207.808)"></path><path class="h" d="M658.936,259.848l5.83.872,2.763,3.251s16.448,3.862,18.428,6.347-9.44,34.777-9.44,34.777L670.785,331.3s9.9,25.694,10.865,38.443l-43.682,13.808s-2.1-44.1-.872-52.078,6.3-28.38,9.836-36.963,12.528-27.125,12.518-27.706S658.936,259.848,658.936,259.848Z" transform="translate(-542.579 -207.63)"></path><g class="i" transform="translate(95.28 117.514)"><path d="M671.569,338.606a3.185,3.185,0,0,0-1.484-1.221c-4.364-2.194-9.464-2.378-14.087-3.98-4.4-1.519-8.728-4.354-13.292-3.508-1.786.334-2.677.339-4.463.7l-.3,1.477c2.811,1.415,7.581-.049,10.231,1.646a27.538,27.538,0,0,1,2.46,2.157,15.06,15.06,0,0,0,3.495,1.958q3.723,1.669,7.513,3.145c2.4.947,4.99,1.855,7.49,1.225a3.843,3.843,0,0,0,2.125-1.247A2.192,2.192,0,0,0,671.569,338.606Z" transform="translate(-637.94 -329.743)"></path></g><path class="h" d="M680.982,268.189c-.654.271-7.649,14.053-9.214,20.925s6.84,27.58,6.84,27.58L653.8,315.456l-2.2,12.266s22.962,7.837,30.537,9.562,15.157,2.767,17.305-.29a16.08,16.08,0,0,0,2.577-6.257l-3.786-25.615L694.777,288.9s-.458-7.21-2.207-11.379S683.482,267.134,680.982,268.189Z" transform="translate(-543.559 -208.171)"></path><path class="g" d="M614.9,330.921a2.617,2.617,0,0,1-.088-.57c-.007-.424-.085-.968-.149-1.642-.12-1.478-.284-3.526-.482-6.05a6.459,6.459,0,0,0-.464-1.888,11.447,11.447,0,0,0-1.155-1.8,8.177,8.177,0,0,1-1.557-4.587,22.5,22.5,0,0,1,2.487-10.722,89.919,89.919,0,0,1,5.64-9.476c1.8-2.861,3.37-5.483,4.779-7.618a54.523,54.523,0,0,1,3.561-4.915c.459-.553.849-.975,1.087-1.247a3.278,3.278,0,0,1,.408-.405,3.833,3.833,0,0,1-.338.465l-1.026,1.289c-.846,1.139-2.065,2.821-3.43,4.982s-2.9,4.791-4.693,7.668a95.092,95.092,0,0,0-5.579,9.458A22.217,22.217,0,0,0,611.4,314.37a7.861,7.861,0,0,0,1.441,4.39,11.826,11.826,0,0,1,1.174,1.876,6.926,6.926,0,0,1,.466,2c.173,2.593.268,4.626.334,6.053.012.674.021,1.22.071,1.643A2.951,2.951,0,0,1,614.9,330.921Z" transform="translate(-540.888 -208.956)"></path><path class="g" d="M633.8,281.815a2.29,2.29,0,0,1-.182.514,12.645,12.645,0,0,1-.686,1.413,27.1,27.1,0,0,1-3.445,4.609,50.526,50.526,0,0,0-5.317,6.6A38.235,38.235,0,0,0,620,304.392a51.508,51.508,0,0,0-1.271,5.215,18.284,18.284,0,0,0-.48,4.932,17.924,17.924,0,0,0,1.249,4.307,12.522,12.522,0,0,1,.457,3.974c-.012,2.423.073,4.378.174,5.735q.067.942.1,1.555a3.794,3.794,0,0,1,.009.546,2.089,2.089,0,0,1-.1-.535c-.049-.354-.118-.863-.191-1.555-.135-1.348-.289-3.31-.32-5.742a12.233,12.233,0,0,0-.49-3.869,17.5,17.5,0,0,1-1.3-4.393,18.2,18.2,0,0,1,.452-5.053,52.465,52.465,0,0,1,1.176-5.23,37.392,37.392,0,0,1,4.289-9.54,47.781,47.781,0,0,1,5.429-6.59,28.918,28.918,0,0,0,3.525-4.491C633.441,282.5,633.716,281.8,633.8,281.815Z" transform="translate(-541.336 -209.075)"></path><path class="g" d="M641.558,280.714a1.983,1.983,0,0,1-.147.522q-.255.733-.582,1.438a33.526,33.526,0,0,1-2.869,4.92c-1.342,2-3.071,4.275-4.677,7a44.477,44.477,0,0,0-4.25,9.322,36,36,0,0,0-1.561,10.109c-.086,3.16.1,6.056.14,8.419s.073,4.326.018,5.686a13.179,13.179,0,0,1-.1,1.551,2.059,2.059,0,0,1-.069.529,3.115,3.115,0,0,1-.008-.536l-.026-1.54c-.022-1.342-.055-3.28-.164-5.684s-.278-5.248-.219-8.435a35.988,35.988,0,0,1,1.559-10.229,43.269,43.269,0,0,1,4.282-9.41c1.642-2.736,3.378-5.007,4.773-7a37.085,37.085,0,0,0,2.974-4.809c.3-.6.51-1.073.652-1.4A3.18,3.18,0,0,1,641.558,280.714Z" transform="translate(-541.942 -209.003)"></path><path class="g" d="M648.11,279.724a2.189,2.189,0,0,1-.06.529,14.513,14.513,0,0,1-.3,1.511,34.683,34.683,0,0,1-1.78,5.361c-.906,2.213-2.18,4.762-3.577,7.581a64.286,64.286,0,0,0-3.945,9.4,107.727,107.727,0,0,0-4.025,18.111c-.3,2.368-.387,4.282-.477,5.616a3.649,3.649,0,0,1-.4,1.5,1.2,1.2,0,0,1-.348.412,2.305,2.305,0,0,0,.269-.454,3.94,3.94,0,0,0,.3-1.468c.03-1.325.075-3.264.33-5.631a98.143,98.143,0,0,1,3.91-18.24,60.483,60.483,0,0,1,4-9.457c1.416-2.819,2.715-5.333,3.648-7.521a39.286,39.286,0,0,0,1.91-5.286C647.94,280.437,648.067,279.716,648.11,279.724Z" transform="translate(-542.348 -208.938)"></path><path class="g" d="M602.99,332.823c-.069,0,.071-.954.123-2.486a25.788,25.788,0,0,0-.446-5.939,15.276,15.276,0,0,0-2-5.558,8.1,8.1,0,0,0-1.708-1.7,1.555,1.555,0,0,1,.619.284,5.172,5.172,0,0,1,1.354,1.267,14.03,14.03,0,0,1,2.172,5.658,21.783,21.783,0,0,1,.327,6.053A8.7,8.7,0,0,1,602.99,332.823Z" transform="translate(-540.095 -211.4)"></path><path class="g" d="M609.2,316.554a2.162,2.162,0,0,1-.354-.488,10.109,10.109,0,0,1-.745-1.563,12.874,12.874,0,0,1-.538-6.291,34.676,34.676,0,0,1,2.966-8.912c1.443-3.217,2.89-6.816,4.741-10.465a35.1,35.1,0,0,1,3.169-5.073,52.605,52.605,0,0,1,3.7-4.138c2.555-2.483,4.049-5.26,5.561-7.484s2.825-3.925,3.775-5.075c.475-.579.848-1.017,1.12-1.308a2.393,2.393,0,0,1,.417-.431,3.514,3.514,0,0,1-.347.481l-1.05,1.368c-.906,1.184-2.193,2.91-3.644,5.142a41.562,41.562,0,0,1-5.533,7.588,53.25,53.25,0,0,0-3.643,4.128,34.493,34.493,0,0,0-3.118,5.011c-1.844,3.614-3.3,7.213-4.759,10.422a35.315,35.315,0,0,0-3.038,8.793,13.232,13.232,0,0,0,.39,6.19A15.677,15.677,0,0,0,609.2,316.554Z" transform="translate(-540.65 -207.99)"></path><path class="g" d="M604.385,344.856a3.927,3.927,0,0,1,.087-.98c.093-.624.243-1.526.441-2.6.437-2.318,1.01-5.331,1.647-8.682a57.791,57.791,0,0,0,1.057-8.733c.066-2.243-.061-3.634.017-3.644a4.654,4.654,0,0,1,.155.975c.049.311.072.7.1,1.149s.016.961.025,1.524a48.151,48.151,0,0,1-.9,8.8c-.61,3.394-1.312,6.426-1.777,8.632q-.338,1.551-.58,2.606A4.427,4.427,0,0,1,604.385,344.856Z" transform="translate(-540.452 -211.602)"></path><path class="g" d="M667.435,264.668a3.969,3.969,0,0,1,.01.641,17.7,17.7,0,0,1-.134,1.837,61.571,61.571,0,0,1-1.013,6.708,64.8,64.8,0,0,1-2.807,9.662c-.672,1.742-1.454,3.608-2.349,5.458a12.471,12.471,0,0,1-.747,1.414,2.466,2.466,0,0,1-1.465.89l-3.118.84-1.008.267.071-.4,3.722,2.638.184.126-.135.184-11.045,14.9-3.293,4.382-.907,1.158a2.283,2.283,0,0,1-.331.386,2.885,2.885,0,0,1,.27-.437l.845-1.2,3.188-4.451L658.3,294.689l.049.311-3.722-2.63-.394-.279.465-.12,1.008-.277,3.118-.84a1.981,1.981,0,0,0,1.227-.688,10.01,10.01,0,0,0,.7-1.336c.9-1.841,1.67-3.654,2.359-5.414A66.865,66.865,0,0,0,666,273.849c.619-2.78.953-5.071,1.152-6.649.091-.763.167-1.37.221-1.83A2.575,2.575,0,0,1,667.435,264.668Z" transform="translate(-542.997 -207.947)"></path><path class="g" d="M683.5,268.536a7.783,7.783,0,0,0-1.889.343,8.742,8.742,0,0,0-4.023,3.235,17.706,17.706,0,0,0-1.825,3.137,30.14,30.14,0,0,0-1.527,3.895,31.173,31.173,0,0,0-1.452,9.44c-.021,3.4.975,6.544,1.6,9.442s1.321,5.482,1.859,7.654,1.078,3.911,1.462,5.1c.208.594.355,1.058.481,1.359a2.936,2.936,0,0,1,.164.482,1.987,1.987,0,0,1-.25-.446c-.152-.291-.324-.747-.559-1.341-.444-1.179-.992-2.909-1.609-5.071s-1.283-4.756-1.988-7.635c-.336-1.448-.7-2.965-1.063-4.543a22.538,22.538,0,0,1-.586-5,30.815,30.815,0,0,1,1.511-9.546,27.385,27.385,0,0,1,1.579-3.929,17.31,17.31,0,0,1,1.911-3.165,8.605,8.605,0,0,1,4.222-3.213,4.967,4.967,0,0,1,1.442-.223A1.846,1.846,0,0,1,683.5,268.536Z" transform="translate(-544.921 -208.199)"></path><path class="g" d="M673.1,338.1a2,2,0,0,1-.417-.106l-1.2-.352-4.464-1.414L651.508,331.3l-.174-.058,0-.181c.708-3.742,1.488-7.815,2.3-12.155l0-.174,0-.181.268,0c9.624.029,18.331.559,24.6,1.2,3.13.3,5.662.606,7.405.837l2.012.278a6.263,6.263,0,0,1,.7.126,3.622,3.622,0,0,1-.711-.039l-1.941-.176c-1.734-.178-4.291-.43-7.42-.7-6.275-.535-14.955-1.03-24.57-1.06l.222-.177,0,.164-2.323,12.155-.142-.24,15.456,5.125,4.43,1.51,1.167.421A1.724,1.724,0,0,1,673.1,338.1Z" transform="translate(-543.541 -211.492)"></path><path class="g" d="M689.861,318.567c0,.13-2.22.305-4.93.74s-4.883.947-4.919.818a17.828,17.828,0,0,1,4.851-1.258A17.393,17.393,0,0,1,689.861,318.567Z" transform="translate(-545.428 -211.489)"></path><path class="g" d="M645.558,366.53a5.2,5.2,0,0,1-.165-.568,2.438,2.438,0,0,0-.886-1.231,2.283,2.283,0,0,0-2.151-.249,2.25,2.25,0,0,0,.07,4.213,2.313,2.313,0,0,0,2.141-.321,2.439,2.439,0,0,0,.845-1.26,5.65,5.65,0,0,1,.146-.583,1.294,1.294,0,0,1,.01.623,2.4,2.4,0,0,1-.841,1.469,2.6,2.6,0,0,1-2.484.465,2.648,2.648,0,0,1-.084-4.992,2.6,2.6,0,0,1,2.5.382,2.371,2.371,0,0,1,.889,1.44C645.633,366.312,645.584,366.538,645.558,366.53Z" transform="translate(-542.824 -214.478)"></path><path class="g" d="M644.645,342.411s-.055-.215-.174-.576a2.492,2.492,0,0,0-.887-1.223,2.284,2.284,0,0,0-2.159-.249,2.241,2.241,0,0,0,.071,4.2,2.286,2.286,0,0,0,2.15-.321,2.5,2.5,0,0,0,.845-1.252c.106-.365.111-.582.154-.583a1.2,1.2,0,0,1,.01.623,2.486,2.486,0,0,1-.841,1.469,2.6,2.6,0,0,1-2.476.465,2.648,2.648,0,0,1-.084-5,2.6,2.6,0,0,1,2.491.382,2.479,2.479,0,0,1,.889,1.44A1.2,1.2,0,0,1,644.645,342.411Z" transform="translate(-542.764 -212.891)"></path><g class="i" transform="translate(58.978 105.563)"><path d="M599.167,316.95a7.016,7.016,0,0,1,4.432,3.682,17.458,17.458,0,0,1,1.683,5.9,5.389,5.389,0,0,1-.373,3.607c-.3.507-1.076.884-1.438.431a1.04,1.04,0,0,1-.173-.473c-.473-2.416-.627-4.889-1.2-7.285s-1.1-4.309-3.014-5.852" transform="translate(-599.081 -316.95)"></path></g><g class="i" transform="translate(100.205 72.286)"><path d="M643.219,316.445c6.281-2.753,10.9-8.638,14.523-14.454.784-1.259,1.55-2.527,2.317-3.795a5.194,5.194,0,0,0,.905-2.265,1.73,1.73,0,0,0-1.269-1.856,2.432,2.432,0,0,0,2.69-1.1,7.148,7.148,0,0,0,.929-2.967c.4-2.516.943-6.2,1.343-8.677-.662,1.811-2.118,4.839-2.78,6.65a8.906,8.906,0,0,1-1.928,3.676c-1.629,1.456-3.3.99-5.227,1.611,1.26.325,2.764,1.763,3.722,2.63-4.7,6.9-15.233,20.584-15.233,20.584" transform="translate(-643.212 -281.329)"></path></g><path class="d" d="M634.681,207.133c-1.725.375-3.606.147-5.32.66a8.651,8.651,0,0,0-5.206,5.4,25.388,25.388,0,0,0-1.2,7.645l-.369,6.93a21.658,21.658,0,0,1-.549,4.432c-.432,1.609-1.632,5.895-1.649,7.972a22.439,22.439,0,0,0,5.454,15.617c1.631,1.782,4.976,2.513,7.346,2.958l4.842-4.408c.17-.158-3.75-4.264-3.658-4.508a1.644,1.644,0,0,0-.123-1.123,55.382,55.382,0,0,1,1.646-41.284" transform="translate(-541.504 -204.161)"></path><path class="n" d="M636.555,266.027c-.281-9.039-.207-12.433-.207-12.381s-7.808-1.176-9.544-11.291c-.872-5.022-.742-13.289-.414-20.054.3-6.063,4.221-13.606,10.288-13.05l18.986,4.5a3.575,3.575,0,0,1,3.244,3.572h0L657.585,265.5" transform="translate(-541.883 -204.299)"></path><path class="g" d="M630.508,229.629a1.3,1.3,0,0,0,1.269,1.294,1.236,1.236,0,0,0,1.319-1.149l0-.041a1.3,1.3,0,0,0-1.268-1.294,1.236,1.236,0,0,0-1.319,1.149Z" transform="translate(-542.171 -205.563)"></path><path class="g" d="M629.021,227.684c.158.17,1.132-.573,2.534-.588s2.435.669,2.6.493-.093-.379-.54-.7a3.575,3.575,0,0,0-2.079-.606,3.411,3.411,0,0,0-2.013.683C629.075,227.3,628.941,227.608,629.021,227.684Z" transform="translate(-542.072 -205.421)"></path><path class="g" d="M645.173,229.6a1.3,1.3,0,0,0,1.268,1.294,1.247,1.247,0,0,0,1.328-1.158l0-.033a1.29,1.29,0,0,0-1.268-1.294,1.238,1.238,0,0,0-1.327,1.141Z" transform="translate(-543.136 -205.561)"></path><path class="g" d="M643.743,227.753c.158.17,1.132-.574,2.534-.6s2.435.669,2.6.493-.093-.379-.54-.692a3.508,3.508,0,0,0-2.079-.605,3.411,3.411,0,0,0-2.013.683C643.805,227.371,643.663,227.677,643.743,227.753Z" transform="translate(-543.04 -205.426)"></path><path class="g" d="M638.214,238.211a9.684,9.684,0,0,0-2.274-.377c-.355.007-.694-.092-.759-.333a1.8,1.8,0,0,1,.216-1.068l1.019-2.691c1.415-3.892,2.426-7.094,2.269-7.144a46.584,46.584,0,0,0-2.834,6.972l-.975,2.751a2.053,2.053,0,0,0-.167,1.4.928.928,0,0,0,.6.518,2.951,2.951,0,0,0,.606.077A8.519,8.519,0,0,0,638.214,238.211Z" transform="translate(-542.444 -205.442)"></path><path class="k" d="M637.066,256.505a27.305,27.305,0,0,0,14.1-3.827s-3.9,7.188-14.061,6.388Z" transform="translate(-542.602 -207.158)"></path><path class="h" d="M639.146,242.562a2.51,2.51,0,0,1,2.243-.964,2.276,2.276,0,0,1,1.606.839,1.454,1.454,0,0,1,.132,1.659,1.679,1.679,0,0,1-1.826.49,5.366,5.366,0,0,1-1.8-1.034,1.384,1.384,0,0,1-.4-.409.457.457,0,0,1-.009-.518" transform="translate(-542.73 -206.428)"></path><path class="g" d="M642.581,240c-.225,0-.208,1.51-1.5,2.621s-2.9.958-2.906,1.165.36.3,1.044.3a3.772,3.772,0,0,0,2.435-.906A3.269,3.269,0,0,0,642.805,241C642.856,240.369,642.685,239.992,642.581,240Z" transform="translate(-542.676 -206.324)"></path><path class="g" d="M643,223.444c.136.378,1.535.174,3.173.345s2.978.608,3.2.259c.092-.158-.147-.518-.681-.855a5.572,5.572,0,0,0-4.854-.463C643.238,222.973,642.94,223.264,643,223.444Z" transform="translate(-542.992 -205.164)"></path><path class="g" d="M629.53,224.994c.247.307,1.211-.021,2.37-.039s2.141.18,2.361-.152c.1-.166-.061-.484-.49-.771a3.257,3.257,0,0,0-1.937-.5,3.3,3.3,0,0,0-1.876.662C629.547,224.466,629.423,224.8,629.53,224.994Z" transform="translate(-542.104 -205.24)"></path><path class="k" d="M647.435,225.918a10.9,10.9,0,0,1,1.94.608,12.165,12.165,0,0,1,1.707,1.105,4.091,4.091,0,0,0-1.631-1.262A4,4,0,0,0,647.435,225.918Z" transform="translate(-543.285 -205.397)"></path><path class="k" d="M627.943,227.452a6.706,6.706,0,0,1,1.283-.93,6.614,6.614,0,0,1,1.525-.441,2.453,2.453,0,0,0-1.6.277A2.512,2.512,0,0,0,627.943,227.452Z" transform="translate(-542.002 -205.406)"></path><path class="k" d="M643.834,240.606a.638.638,0,0,0,.046-.347,1.451,1.451,0,0,0-1.076-1.219l-.354.006s.131.049.313.116a1.635,1.635,0,0,1,.631.439,1.691,1.691,0,0,1,.366.678C643.805,240.5,643.807,240.607,643.834,240.606Z" transform="translate(-542.957 -206.261)"></path><path class="k" d="M645.638,241.21a1.256,1.256,0,0,0,.137-.547,2.172,2.172,0,0,0-.35-1.318,2.222,2.222,0,0,0-1.071-.847,1,1,0,0,0-.555-.094,5.094,5.094,0,0,1,.505.207,2.328,2.328,0,0,1,1.35,2.054C645.669,240.993,645.612,241.2,645.638,241.21Z" transform="translate(-543.045 -206.218)"></path><path class="d" d="M634.361,208.268a2.41,2.41,0,0,1,1.015-1.436,14.422,14.422,0,0,1,10.963-2.693,25.7,25.7,0,0,1,10.632,4.4c2.8,1.875,5.454,4.176,6.906,7.215a24.469,24.469,0,0,1,1.76,6.894c.893,5.827,2.558,11.658,2.6,17.551.038,2.293.492,6.155.15,8.99-.365,3.035-3.236,6.77-6.211,7.5l-6.694.778s3.424-12.588,3.063-17.1c-.309-3.95-.347-8.294-.863-12.223a2.083,2.083,0,0,0-.248-.861,2.147,2.147,0,0,0-.93-.694l-4.232-2.067a41.712,41.712,0,0,1-3.982-2.14,39.881,39.881,0,0,1-4.051-3.082l-5.52-4.555a16.315,16.315,0,0,1-3.195-3.192,5.41,5.41,0,0,1-.936-4.242" transform="translate(-542.424 -203.952)"></path><path class="n" d="M658.859,232.591A2.959,2.959,0,0,1,663.143,230c1.04.58,1.839,1.761,1.8,4.046-.148,6.112-6.256,4.7-6.259,4.527S658.78,235.119,658.859,232.591Z" transform="translate(-544.025 -205.645)"></path><path class="k" d="M660.726,236.436a1.81,1.81,0,0,0,.289.16,1.055,1.055,0,0,0,.788.038,2.6,2.6,0,0,0,1.277-2.332,3.47,3.47,0,0,0-.26-1.536,1.221,1.221,0,0,0-.775-.853.537.537,0,0,0-.636.279c-.093.165-.056.286-.081.3s-.132-.1-.076-.353a.651.651,0,0,1,.245-.376.812.812,0,0,1,.6-.14,1.506,1.506,0,0,1,1.089.969,3.6,3.6,0,0,1,.323,1.725,2.817,2.817,0,0,1-1.61,2.623,1.158,1.158,0,0,1-.972-.183C660.737,236.583,660.709,236.445,660.726,236.436Z" transform="translate(-544.159 -205.772)"></path><path class="b" d="M659.6,256.339c-.147,0-.12-.967.13-2.6s.755-4.046,1.4-6.948a85.42,85.42,0,0,0,1.811-10.191,35.929,35.929,0,0,0,.1-5.99,19.4,19.4,0,0,0-1.323-6.1,8.664,8.664,0,0,0-1.6-2.5,11.172,11.172,0,0,0-2.334-1.847,33.576,33.576,0,0,0-5.383-2.61c-3.607-1.412-7.044-2.479-9.824-3.679s-4.954-2.782-5.9-4.315-.909-2.581-.762-2.584.4.859,1.463,2.053a13.633,13.633,0,0,0,5.777,3.365c2.769,1.01,6.193,1.869,9.98,3.226a32.862,32.862,0,0,1,5.8,2.672,13.315,13.315,0,0,1,2.8,2.135,10.934,10.934,0,0,1,2.034,3.142,20.855,20.855,0,0,1,1.456,6.848,33.735,33.735,0,0,1-.249,6.374,65.235,65.235,0,0,1-2.319,10.355c-.817,2.87-1.584,5.168-2.085,6.751S659.744,256.372,659.6,256.339Z" transform="translate(-542.522 -204.151)"></path><path class="b" d="M665.153,255.285a3.153,3.153,0,0,1,.136-.651c.1-.417.268-1.026.437-1.825a44.666,44.666,0,0,0,.993-6.828,48.13,48.13,0,0,0-.421-10.205c-.275-1.909-.654-3.9-1.156-5.935a29.324,29.324,0,0,0-2.007-6.12,13.776,13.776,0,0,0-4.191-4.785,27.072,27.072,0,0,0-5.242-2.924c-3.539-1.516-6.889-2.56-9.516-3.883a19.97,19.97,0,0,1-5.717-3.929,8.991,8.991,0,0,1-.724-.853,5.239,5.239,0,0,1-.487-.632,3.124,3.124,0,0,1-.355-.557,21.218,21.218,0,0,0,1.693,1.876,20.5,20.5,0,0,0,5.758,3.789c2.618,1.281,5.967,2.28,9.584,3.795a26.9,26.9,0,0,1,5.337,2.949,14.082,14.082,0,0,1,4.323,4.93,29.286,29.286,0,0,1,2.042,6.223,56.96,56.96,0,0,1,1.13,5.979,46.7,46.7,0,0,1,.336,10.285,39.515,39.515,0,0,1-1.149,6.848c-.186.8-.391,1.4-.515,1.809A3.4,3.4,0,0,1,665.153,255.285Z" transform="translate(-542.592 -204.097)"></path><path class="b" d="M633.927,257.989a4.421,4.421,0,0,1-.589.01,10.855,10.855,0,0,1-1.689-.058,4.161,4.161,0,0,1-2.45-1.111,12.864,12.864,0,0,1-1.824-3.05,30.5,30.5,0,0,1-3.2-8.6,41.618,41.618,0,0,1-.558-5.426c-.1-1.876-.16-3.831-.1-5.831.049-8,1.3-15.263,3.411-20.11a10.41,10.41,0,0,1,2.028-2.925,7.285,7.285,0,0,1,2.216-1.526,5.65,5.65,0,0,1,1.637-.461l.442-.007.147,0a7.374,7.374,0,0,0-2.153.624,7.635,7.635,0,0,0-2.146,1.533,10.192,10.192,0,0,0-1.925,2.88c-2,4.793-3.209,12.032-3.249,19.96-.019,1.991.066,3.937.1,5.814a43.963,43.963,0,0,0,.523,5.365,31.236,31.236,0,0,0,3.076,8.56,13.3,13.3,0,0,0,1.722,3.017,4.034,4.034,0,0,0,2.294,1.113C633.105,258.011,633.926,257.929,633.927,257.989Z" transform="translate(-541.709 -204.277)"></path><circle class="g" cx="1.203" cy="1.203" r="1.203" transform="matrix(1, -0.017, 0.017, 1, 116.262, 32.106)"></circle><path class="h" d="M580.074,540.046l.02,1.15s-19.375,7.888-19.565,11.578l38.551-.376-.647-12.824A21.98,21.98,0,0,1,580.074,540.046Z" transform="translate(-537.566 -226.036)"></path><g class="o" transform="translate(55.14 317.642)"><path class="e" d="M596.812,544a1.576,1.576,0,0,1,1.112,1.782,1.515,1.515,0,0,1-1.765,1.111A1.644,1.644,0,0,1,595,544.995a1.568,1.568,0,0,1,1.957-.95" transform="translate(-594.972 -543.967)"></path></g><g class="o" transform="translate(22.926 323.266)"><path class="e" d="M599.059,553.075l-.156-3.088-36.9,1.484s-1.718.764-1.5,1.972Z" transform="translate(-560.489 -549.987)"></path></g><path class="g" d="M581.307,541.41c0,.191.957.262,1.883.834s1.434,1.386,1.606,1.306-.115-1.157-1.26-1.866S581.27,541.239,581.307,541.41Z" transform="translate(-538.933 -226.144)"></path><path class="g" d="M577,543.251c-.04.182.779.5,1.424,1.283s.841,1.63,1.031,1.627.293-1.078-.528-2.052S577,543.069,577,543.251Z" transform="translate(-538.65 -226.272)"></path><path class="g" d="M574.488,548.238c.164,0,.419-.873-.076-1.937s-1.373-1.422-1.457-1.265.479.7.891,1.552S574.3,548.189,574.488,548.238Z" transform="translate(-538.383 -226.393)"></path><path class="n" d="M642.4,319.834l-14.193-6.184-9.718,1.141-4.5,5.519s.027,1.609,1.009,1.342a6.259,6.259,0,0,0,2.081-1.272l.19,4.082s1.509,1.706,2.475-.041l1.038,3.073s1.13,1.34,1.973-.034l1.548,3.557a2.7,2.7,0,0,0,1.71-1.267,2.745,2.745,0,0,0,.211-1.855s4.835.785,6.825,1.366,11.739.176,11.739.176l4.347,1.165,1.82-10.182Z" transform="translate(-541.084 -211.17)"></path><path class="k" d="M626.362,315.263a5.33,5.33,0,0,0-1.731.029c-.526.113-1.145.322-1.851.55s-1.464.466-2.237.774a13.094,13.094,0,0,0-2.1,1.039,1.161,1.161,0,0,0-.561.685c-.081.287-.136.6-.192.869a17.041,17.041,0,0,1-.326,1.736,4.19,4.19,0,0,1,.013-1.809,7,7,0,0,1,.149-.92,1.509,1.509,0,0,1,.686-.929,11.122,11.122,0,0,1,2.163-1.109c.8-.325,1.583-.555,2.289-.766a14.59,14.59,0,0,1,1.923-.456A2.306,2.306,0,0,1,626.362,315.263Z" transform="translate(-541.3 -211.252)"></path><path class="k" d="M627.381,318.3a4.361,4.361,0,0,1-1.572.7c-1.007.337-2.456.6-3.963,1.061a2.746,2.746,0,0,0-.962.432,4.08,4.08,0,0,0-.479.873,3.8,3.8,0,0,0-.42,1.739,7.947,7.947,0,0,0,.193,1.208,1.646,1.646,0,0,1,.008.467,3.209,3.209,0,0,1-.172-.43,7.5,7.5,0,0,1-.34-1.223,5.175,5.175,0,0,1,.9-2.915,2.9,2.9,0,0,1,1.142-.556c1.558-.485,3-.682,4.017-.932A13.181,13.181,0,0,1,627.381,318.3Z" transform="translate(-541.457 -211.476)"></path><path class="k" d="M623.434,328.394a2.682,2.682,0,0,1-.753-1.043,2.331,2.331,0,0,1-.189-1.441,1.9,1.9,0,0,1,1.232-1.319,4.577,4.577,0,0,1,1.727-.236l1.368.02a5.939,5.939,0,0,1,.928.071c.243.057.323.211.289.22a1.577,1.577,0,0,0-.312-.047c-.2,0-.518.052-.864.076l-1.367.075a4.877,4.877,0,0,0-1.562.242,1.515,1.515,0,0,0-.987.995,2.088,2.088,0,0,0,.081,1.228A10.028,10.028,0,0,1,623.434,328.394Z" transform="translate(-541.641 -211.874)"></path></g></g></svg>