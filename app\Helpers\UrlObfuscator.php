<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class UrlObfuscator
{
    /**
     * Generate a highly obfuscated URL segment
     */
    public static function obfuscate($value, $type = 'default')
    {
        try {
            // Create multiple layers of obfuscation
            $timestamp = now()->timestamp;
            $microtime = microtime(true);
            $random1 = Str::random(12);
            $random2 = Str::random(8);
            $random3 = Str::random(16);
            
            // Create a complex payload
            $payload = json_encode([
                'v' => $value,
                't' => $timestamp,
                'mt' => $microtime,
                'type' => $type,
                'r1' => $random1,
                'r2' => $random2,
                'checksum' => md5($value . $timestamp . $type)
            ]);
            
            // Encrypt the payload
            $encrypted = Crypt::encryptString($payload);
            
            // Base64 encode and make URL safe
            $encoded = rtrim(strtr(base64_encode($encrypted), '+/', '-_'), '=');
            
            // Add multiple random segments to make it extremely long
            $segments = [
                self::generateSegment(20),  // 20 chars
                $encoded,                   // encrypted data
                self::generateSegment(16), // 16 chars
                self::generateHexSegment(24), // 24 hex chars
                self::generateSegment(12),  // 12 chars
                self::generateNumericSegment(8), // 8 numbers
                self::generateSegment(18),  // 18 chars
            ];
            
            return implode('', $segments);
            
        } catch (\Exception $e) {
            // Fallback to simpler obfuscation
            return self::fallbackObfuscate($value);
        }
    }

    /**
     * Deobfuscate a URL segment
     */
    public static function deobfuscate($obfuscated)
    {
        try {
            // Extract the encrypted data (second segment)
            if (strlen($obfuscated) < 100) {
                return self::fallbackDeobfuscate($obfuscated);
            }
            
            // Remove the random segments
            $withoutPrefix = substr($obfuscated, 20); // Remove first 20 chars
            $withoutSuffix = substr($withoutPrefix, 0, -78); // Remove last 78 chars (16+24+12+8+18)
            
            // Restore base64 padding and decode
            $encoded = strtr($withoutSuffix, '-_', '+/');
            $encrypted = base64_decode($encoded);
            
            if ($encrypted === false) {
                return null;
            }
            
            // Decrypt the payload
            $payload = Crypt::decryptString($encrypted);
            $data = json_decode($payload, true);
            
            if (!$data || !isset($data['v'])) {
                return null;
            }
            
            // Verify checksum
            $expectedChecksum = md5($data['v'] . $data['t'] . $data['type']);
            if ($data['checksum'] !== $expectedChecksum) {
                return null;
            }
            
            return $data['v'];
            
        } catch (\Exception $e) {
            return self::fallbackDeobfuscate($obfuscated);
        }
    }

    /**
     * Generate a random alphanumeric segment
     */
    private static function generateSegment($length)
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        $segment = '';
        
        for ($i = 0; $i < $length; $i++) {
            $segment .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $segment;
    }

    /**
     * Generate a random hexadecimal segment
     */
    private static function generateHexSegment($length)
    {
        $characters = '0123456789ABCDEF';
        $segment = '';
        
        for ($i = 0; $i < $length; $i++) {
            $segment .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $segment;
    }

    /**
     * Generate a random numeric segment
     */
    private static function generateNumericSegment($length)
    {
        $segment = '';
        
        for ($i = 0; $i < $length; $i++) {
            $segment .= rand(0, 9);
        }
        
        return $segment;
    }

    /**
     * Fallback obfuscation method
     */
    private static function fallbackObfuscate($value)
    {
        $timestamp = time();
        $random = mt_rand(100000, 999999);
        $combined = $value . '|' . $timestamp . '|' . $random;
        
        $encoded = base64_encode($combined);
        $prefix = self::generateSegment(12);
        $suffix = self::generateSegment(16);
        
        return $prefix . $encoded . $suffix;
    }

    /**
     * Fallback deobfuscation method
     */
    private static function fallbackDeobfuscate($obfuscated)
    {
        try {
            if (strlen($obfuscated) < 29) {
                return null;
            }
            
            $encoded = substr($obfuscated, 12, -16);
            $decoded = base64_decode($encoded);
            
            if ($decoded === false) {
                return null;
            }
            
            $parts = explode('|', $decoded);
            return isset($parts[0]) ? $parts[0] : null;
            
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Create an extremely long and complex URL
     */
    public static function createComplexUrl($route, $parameters = [])
    {
        $baseUrl = route($route, $parameters);
        
        // Add multiple query parameters to make URL extremely long
        $queryParams = [
            'ref' => self::generateSegment(32),
            'token' => self::generateHexSegment(64),
            'session' => self::generateSegment(28),
            'cache' => self::generateNumericSegment(16),
            'version' => self::generateSegment(20),
            'timestamp' => time() . self::generateNumericSegment(8),
            'signature' => hash('sha256', time() . self::generateSegment(16)),
            'nonce' => self::generateSegment(24),
            'state' => self::generateHexSegment(32),
            'challenge' => self::generateSegment(40),
        ];
        
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        $queryString = http_build_query($queryParams);
        
        return $baseUrl . $separator . $queryString;
    }
}
