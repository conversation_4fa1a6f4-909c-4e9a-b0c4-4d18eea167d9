@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-add-line text-primary me-2"></i>Create Credit Note
                        </h4>
                        <p class="text-muted mb-0">
                            Create a credit note for refunds, discounts, or positive adjustments
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('credit-notes.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Credit Notes
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form action="{{ route('credit-notes.store') }}" method="POST">
            @csrf
            
            <div class="row">
                <!-- Credit Note Details -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Credit Note Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="tenant_id" class="form-label">Tenant <span class="text-danger">*</span></label>
                                    <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                            id="tenant_id" name="tenant_id" required>
                                        <option value="">Select Tenant</option>
                                        <!-- Tenants will be populated here -->
                                        <option value="1" {{ old('tenant_id') == '1' ? 'selected' : '' }}>John Doe - Unit 101</option>
                                        <option value="2" {{ old('tenant_id') == '2' ? 'selected' : '' }}>Jane Smith - Unit 202</option>
                                    </select>
                                    @error('tenant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                    <select class="form-select @error('property_id') is-invalid @enderror" 
                                            id="property_id" name="property_id" required>
                                        <option value="">Select Property</option>
                                        <!-- Properties will be populated here -->
                                        <option value="1" {{ old('property_id') == '1' ? 'selected' : '' }}>Sample Property 1</option>
                                        <option value="2" {{ old('property_id') == '2' ? 'selected' : '' }}>Sample Property 2</option>
                                    </select>
                                    @error('property_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="credit_note_number" class="form-label">Credit Note Number</label>
                                    <input type="text" class="form-control @error('credit_note_number') is-invalid @enderror" 
                                           id="credit_note_number" name="credit_note_number" value="{{ old('credit_note_number') }}" 
                                           placeholder="Auto-generated if left empty">
                                    @error('credit_note_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('issue_date') is-invalid @enderror" 
                                           id="issue_date" name="issue_date" value="{{ old('issue_date', date('Y-m-d')) }}" required>
                                    @error('issue_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reason_category" class="form-label">Reason Category <span class="text-danger">*</span></label>
                                    <select class="form-select @error('reason_category') is-invalid @enderror" 
                                            id="reason_category" name="reason_category" required>
                                        <option value="">Select Category</option>
                                        <option value="refund" {{ old('reason_category') == 'refund' ? 'selected' : '' }}>Refund</option>
                                        <option value="discount" {{ old('reason_category') == 'discount' ? 'selected' : '' }}>Discount</option>
                                        <option value="overpayment" {{ old('reason_category') == 'overpayment' ? 'selected' : '' }}>Overpayment</option>
                                        <option value="deposit_return" {{ old('reason_category') == 'deposit_return' ? 'selected' : '' }}>Deposit Return</option>
                                        <option value="billing_error" {{ old('reason_category') == 'billing_error' ? 'selected' : '' }}>Billing Error Correction</option>
                                        <option value="goodwill" {{ old('reason_category') == 'goodwill' ? 'selected' : '' }}>Goodwill Credit</option>
                                        <option value="other" {{ old('reason_category') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('reason_category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" class="form-control @error('amount') is-invalid @enderror" 
                                               id="amount" name="amount" value="{{ old('amount') }}" 
                                               placeholder="0.00" required>
                                        @error('amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="reason_description" class="form-label">Reason Description <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('reason_description') is-invalid @enderror" 
                                          id="reason_description" name="reason_description" rows="4" 
                                          placeholder="Detailed description of the reason for this credit note" required>{{ old('reason_description') }}</textarea>
                                @error('reason_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reference_bill_id" class="form-label">Reference Bill</label>
                                    <select class="form-select @error('reference_bill_id') is-invalid @enderror" 
                                            id="reference_bill_id" name="reference_bill_id">
                                        <option value="">Select Related Bill (Optional)</option>
                                        <!-- Bills will be populated here -->
                                        <option value="1" {{ old('reference_bill_id') == '1' ? 'selected' : '' }}>BILL-2024-001 - $1,200.00</option>
                                        <option value="2" {{ old('reference_bill_id') == '2' ? 'selected' : '' }}>BILL-2024-002 - $850.00</option>
                                    </select>
                                    @error('reference_bill_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status">
                                        <option value="draft" {{ old('status', 'draft') == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="issued" {{ old('status') == 'issued' ? 'selected' : '' }}>Issued</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="internal_notes" class="form-label">Internal Notes</label>
                                <textarea class="form-control @error('internal_notes') is-invalid @enderror" 
                                          id="internal_notes" name="internal_notes" rows="3" 
                                          placeholder="Internal notes (not visible to tenant)">{{ old('internal_notes') }}</textarea>
                                @error('internal_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary & Actions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Credit Note Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Tenant</small>
                                <div id="summary-tenant" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Property</small>
                                <div id="summary-property" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Category</small>
                                <div id="summary-category" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Amount</small>
                                <div id="summary-amount" class="fw-bold text-success fs-4">$0.00</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Status</small>
                                <div id="summary-status" class="fw-bold">
                                    <span class="badge bg-secondary">Draft</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i>Create Credit Note
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewCreditNote()">
                                    <i class="ri-eye-line me-1"></i>Preview
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" value="1">
                                <label class="form-check-label" for="send_notification">
                                    Send notification to tenant
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="apply_immediately" name="apply_immediately" value="1">
                                <label class="form-check-label" for="apply_immediately">
                                    Apply to account immediately
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Credit note creation scripts
    console.log('Credit Note Create page loaded');
    
    // Update summary
    function updateSummary() {
        const tenant = document.getElementById('tenant_id').selectedOptions[0]?.text || '-';
        const property = document.getElementById('property_id').selectedOptions[0]?.text || '-';
        const category = document.getElementById('reason_category').selectedOptions[0]?.text || '-';
        const amount = document.getElementById('amount').value || '0.00';
        const status = document.getElementById('status').selectedOptions[0]?.text || 'Draft';
        
        document.getElementById('summary-tenant').textContent = tenant;
        document.getElementById('summary-property').textContent = property;
        document.getElementById('summary-category').textContent = category;
        document.getElementById('summary-amount').textContent = `$${parseFloat(amount).toFixed(2)}`;
        
        const statusBadge = document.getElementById('summary-status');
        const badgeClass = status === 'Draft' ? 'bg-secondary' : 'bg-success';
        statusBadge.innerHTML = `<span class="badge ${badgeClass}">${status}</span>`;
    }
    
    function previewCreditNote() {
        // Implementation for credit note preview
        console.log('Previewing credit note...');
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('tenant_id').addEventListener('change', updateSummary);
        document.getElementById('property_id').addEventListener('change', updateSummary);
        document.getElementById('reason_category').addEventListener('change', updateSummary);
        document.getElementById('amount').addEventListener('input', updateSummary);
        document.getElementById('status').addEventListener('change', updateSummary);
    });
</script>
@endpush
