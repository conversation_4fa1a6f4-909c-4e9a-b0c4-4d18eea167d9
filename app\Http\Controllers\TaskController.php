<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Task::with(['property', 'unit', 'assignedTo', 'createdBy']);
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Task::whereIn('property_id', $propertyIds)->with(['property', 'unit', 'assignedTo', 'createdBy']);
        } else {
            // Staff see only assigned tasks
            $query = Task::where('assigned_to', $user->id)->with(['property', 'unit', 'createdBy']);
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $tasks = $query->latest()->paginate(15);

        // Calculate statistics
        $stats = [
            'total' => $query->count(),
            'pending' => $query->where('status', 'pending')->count(),
            'in_progress' => $query->where('status', 'in_progress')->count(),
            'completed' => $query->where('status', 'completed')->count(),
            'overdue' => $query->overdue()->count(),
            'due_today' => $query->dueToday()->count(),
        ];

        // Get filter options
        if ($user->hasRole('admin')) {
            $properties = Property::all();
            $staff = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['maintenance_staff', 'property_manager', 'receptionist']);
            })->get();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties;
            $staff = $user->employees;
        } else {
            $properties = collect();
            $staff = collect();
        }

        return view('tasks.index', compact('tasks', 'properties', 'staff', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Task::class);
        
        $user = Auth::user();
        
        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
            $staff = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['maintenance_staff', 'property_manager', 'receptionist']);
            })->get();
        } else {
            $properties = $user->ownedProperties()->with('units')->get();
            $staff = $user->employees;
        }

        $selectedProperty = $request->property_id ? Property::find($request->property_id) : null;
        $selectedUnit = $request->unit_id ? Unit::find($request->unit_id) : null;

        return view('tasks.create', compact('properties', 'staff', 'selectedProperty', 'selectedUnit'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Task::class);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:inspection,contract_renewal,maintenance,lease_expiry,document_expiry,other',
            'priority' => 'required|in:low,medium,high,urgent',
            'property_id' => 'nullable|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'assigned_to' => 'nullable|exists:users,id',
            'due_date' => 'nullable|date|after:now',
            'notes' => 'nullable|string',
            'is_recurring' => 'boolean',
            'recurrence_pattern' => 'nullable|in:daily,weekly,monthly,yearly',
            'reminder_days' => 'nullable|integer|min:1|max:365',
            'reminder_email' => 'boolean',
        ]);

        $validated['created_by'] = Auth::id();
        $validated['status'] = 'pending';

        // Set up reminder settings
        if ($request->filled('reminder_days') || $request->boolean('reminder_email')) {
            $validated['reminder_settings'] = [
                'days_before' => $request->reminder_days ?? 1,
                'email_enabled' => $request->boolean('reminder_email'),
            ];
        }

        // Set next occurrence for recurring tasks
        if ($validated['is_recurring'] && $validated['recurrence_pattern'] && $validated['due_date']) {
            $dueDate = \Carbon\Carbon::parse($validated['due_date']);
            switch ($validated['recurrence_pattern']) {
                case 'daily':
                    $validated['next_occurrence'] = $dueDate->addDay();
                    break;
                case 'weekly':
                    $validated['next_occurrence'] = $dueDate->addWeek();
                    break;
                case 'monthly':
                    $validated['next_occurrence'] = $dueDate->addMonth();
                    break;
                case 'yearly':
                    $validated['next_occurrence'] = $dueDate->addYear();
                    break;
            }
        }

        $task = Task::create($validated);

        // Create notification for assigned user
        if ($task->assigned_to) {
            Notification::createTaskReminder($task, $task->assignedTo, "You have been assigned a new task: {$task->title}");
        }

        return redirect()->route('tasks.show', $task)
            ->with('success', 'Task created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Task $task)
    {
        $this->authorize('view', $task);

        $task->load(['property', 'unit', 'assignedTo', 'createdBy', 'notifications']);

        return view('tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Task $task)
    {
        $this->authorize('update', $task);
        
        $user = Auth::user();
        
        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
            $staff = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['maintenance_staff', 'property_manager', 'receptionist']);
            })->get();
        } else {
            $properties = $user->ownedProperties()->with('units')->get();
            $staff = $user->employees;
        }

        return view('tasks.edit', compact('task', 'properties', 'staff'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Task $task)
    {
        $this->authorize('update', $task);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:inspection,contract_renewal,maintenance,lease_expiry,document_expiry,other',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'property_id' => 'nullable|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'assigned_to' => 'nullable|exists:users,id',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'is_recurring' => 'boolean',
            'recurrence_pattern' => 'nullable|in:daily,weekly,monthly,yearly',
            'reminder_days' => 'nullable|integer|min:1|max:365',
            'reminder_email' => 'boolean',
        ]);

        // Set completion timestamp if status is completed
        if ($validated['status'] === 'completed' && $task->status !== 'completed') {
            $validated['completed_at'] = now();
            
            // Create next occurrence if recurring
            if ($task->is_recurring) {
                $task->createNextOccurrence();
            }
        } elseif ($validated['status'] !== 'completed') {
            $validated['completed_at'] = null;
        }

        // Set up reminder settings
        if ($request->filled('reminder_days') || $request->boolean('reminder_email')) {
            $validated['reminder_settings'] = [
                'days_before' => $request->reminder_days ?? 1,
                'email_enabled' => $request->boolean('reminder_email'),
            ];
        }

        // Update next occurrence for recurring tasks
        if ($validated['is_recurring'] && $validated['recurrence_pattern'] && $validated['due_date']) {
            $dueDate = \Carbon\Carbon::parse($validated['due_date']);
            switch ($validated['recurrence_pattern']) {
                case 'daily':
                    $validated['next_occurrence'] = $dueDate->addDay();
                    break;
                case 'weekly':
                    $validated['next_occurrence'] = $dueDate->addWeek();
                    break;
                case 'monthly':
                    $validated['next_occurrence'] = $dueDate->addMonth();
                    break;
                case 'yearly':
                    $validated['next_occurrence'] = $dueDate->addYear();
                    break;
            }
        }

        $task->update($validated);

        // Create notification if task was reassigned
        if ($task->wasChanged('assigned_to') && $task->assigned_to) {
            Notification::createTaskReminder($task, $task->assignedTo, "You have been assigned to task: {$task->title}");
        }

        return redirect()->route('tasks.show', $task)
            ->with('success', 'Task updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Task $task)
    {
        $this->authorize('delete', $task);

        $task->delete();

        return redirect()->route('tasks.index')
            ->with('success', 'Task deleted successfully!');
    }

    /**
     * Mark task as completed
     */
    public function markCompleted(Task $task)
    {
        $this->authorize('update', $task);

        $task->markAsCompleted();

        // Create next occurrence if recurring
        if ($task->is_recurring) {
            $task->createNextOccurrence();
        }

        return redirect()->route('tasks.show', $task)
            ->with('success', 'Task marked as completed!');
    }

    /**
     * Get tasks dashboard data
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Build base query
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('admin', $userRoles)) {
            $baseQuery = Task::query();
        } elseif (in_array('property_owner', $userRoles)) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $baseQuery = Task::whereIn('property_id', $propertyIds);
        } else {
            $baseQuery = Task::where('assigned_to', $user->id);
        }

        // Get statistics
        $stats = [
            'total' => $baseQuery->count(),
            'pending' => $baseQuery->where('status', 'pending')->count(),
            'in_progress' => $baseQuery->where('status', 'in_progress')->count(),
            'completed' => $baseQuery->where('status', 'completed')->count(),
            'overdue' => $baseQuery->where('due_date', '<', now())->where('status', '!=', 'completed')->count(),
        ];

        // Get recent tasks
        $recentTasks = $baseQuery->with(['property', 'unit', 'assignedTo'])
            ->latest()
            ->take(10)
            ->get();

        return view('tasks.dashboard', compact('stats', 'recentTasks'));
    }
}
