@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-notification-line text-primary me-2"></i>Notification Details
                        </h4>
                        <p class="text-muted mb-0">
                            View notification information and details
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Notifications
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Notification Details -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                @switch($notification->type)
                                    @case('task_reminder')
                                        <i class="ri-task-line text-primary me-2"></i>
                                        @break
                                    @case('lease_expiry')
                                        <i class="ri-calendar-line text-warning me-2"></i>
                                        @break
                                    @case('document_expiry')
                                        <i class="ri-file-line text-danger me-2"></i>
                                        @break
                                    @case('payment_due')
                                        <i class="ri-money-dollar-circle-line text-success me-2"></i>
                                        @break
                                    @case('maintenance_request')
                                        <i class="ri-tools-line text-info me-2"></i>
                                        @break
                                    @case('system_alert')
                                        <i class="ri-alert-line text-secondary me-2"></i>
                                        @break
                                    @default
                                        <i class="ri-notification-line text-primary me-2"></i>
                                @endswitch
                                {{ $notification->title }}
                            </h5>
                            <div class="d-flex gap-2">
                                <span class="badge bg-{{ $notification->priority == 'urgent' ? 'danger' : ($notification->priority == 'high' ? 'warning' : ($notification->priority == 'medium' ? 'info' : 'secondary')) }}">
                                    {{ ucfirst($notification->priority) }} Priority
                                </span>
                                @if(!$notification->is_read)
                                    <span class="badge bg-primary">Unread</span>
                                @else
                                    <span class="badge bg-success">Read</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Message -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Message</h6>
                            <p class="mb-0">{{ $notification->message }}</p>
                        </div>

                        <!-- Related Information -->
                        @if($notification->relatedProperty || $notification->relatedUnit || $notification->relatedTask)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Related Information</h6>
                                <div class="row">
                                    @if($notification->relatedProperty)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-building-line text-primary me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Property</small>
                                                    <strong>{{ $notification->relatedProperty->name }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($notification->relatedUnit)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-home-line text-info me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Unit</small>
                                                    <strong>Unit {{ $notification->relatedUnit->unit_number }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($notification->relatedTask)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-task-line text-success me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Task</small>
                                                    <strong>{{ $notification->relatedTask->title }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Additional Data -->
                        @if($notification->data)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Additional Information</h6>
                                <div class="bg-light p-3 rounded">
                                    @foreach($notification->data as $key => $value)
                                        <div class="row mb-2">
                                            <div class="col-4">
                                                <strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong>
                                            </div>
                                            <div class="col-8">
                                                @if(is_array($value))
                                                    {{ implode(', ', $value) }}
                                                @elseif(is_bool($value))
                                                    {{ $value ? 'Yes' : 'No' }}
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Actions -->
                        <div class="d-flex gap-2">
                            @if(!$notification->is_read)
                                <button type="button" class="btn btn-success mark-read" 
                                        data-notification-id="{{ $notification->id }}">
                                    <i class="ri-check-line me-1"></i>Mark as Read
                                </button>
                            @endif
                            
                            @if($notification->relatedTask)
                                <a href="{{ route('tasks.show', $notification->relatedTask) }}" class="btn btn-outline-primary">
                                    <i class="ri-task-line me-1"></i>View Task
                                </a>
                            @endif
                            
                            @if($notification->relatedProperty)
                                <a href="{{ route('properties.show', $notification->relatedProperty) }}" class="btn btn-outline-info">
                                    <i class="ri-building-line me-1"></i>View Property
                                </a>
                            @endif
                            
                            @if($notification->relatedUnit)
                                <a href="{{ route('units.show', $notification->relatedUnit) }}" class="btn btn-outline-secondary">
                                    <i class="ri-home-line me-1"></i>View Unit
                                </a>
                            @endif

                            @can('delete', $notification)
                                <button type="button" class="btn btn-outline-danger delete-notification" 
                                        data-notification-id="{{ $notification->id }}">
                                    <i class="ri-delete-bin-line me-1"></i>Delete
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Metadata -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Notification Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Type -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Type</small>
                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $notification->type)) }}</span>
                        </div>

                        <!-- Priority -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Priority</small>
                            <span class="badge bg-{{ $notification->priority == 'urgent' ? 'danger' : ($notification->priority == 'high' ? 'warning' : ($notification->priority == 'medium' ? 'info' : 'secondary')) }}">
                                {{ ucfirst($notification->priority) }}
                            </span>
                        </div>

                        <!-- Created -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Created</small>
                            <strong>{{ $notification->created_at->format('M d, Y H:i') }}</strong>
                            <br><small class="text-muted">{{ $notification->time_ago }}</small>
                        </div>

                        <!-- Sender -->
                        @if($notification->createdBy)
                            <div class="mb-3">
                                <small class="text-muted d-block">Sent By</small>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        @if($notification->createdBy->profile_photo)
                                            <img src="{{ asset('storage/' . $notification->createdBy->profile_photo) }}" 
                                                 alt="{{ $notification->createdBy->name }}" class="rounded-circle">
                                        @else
                                            <div class="avatar-initial rounded-circle bg-primary">
                                                {{ substr($notification->createdBy->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </div>
                                    <strong>{{ $notification->createdBy->name }}</strong>
                                </div>
                            </div>
                        @endif

                        <!-- Read Status -->
                        @if($notification->is_read)
                            <div class="mb-3">
                                <small class="text-muted d-block">Read At</small>
                                <strong>{{ $notification->read_at->format('M d, Y H:i') }}</strong>
                            </div>
                        @endif

                        <!-- Email Status -->
                        @if($notification->is_email_sent)
                            <div class="mb-3">
                                <small class="text-muted d-block">Email Sent</small>
                                <span class="badge bg-success">
                                    <i class="ri-mail-check-line me-1"></i>Sent
                                </span>
                                @if($notification->email_sent_at)
                                    <br><small class="text-muted">{{ $notification->email_sent_at->format('M d, Y H:i') }}</small>
                                @endif
                            </div>
                        @endif

                        <!-- Scheduled -->
                        @if($notification->scheduled_for)
                            <div class="mb-3">
                                <small class="text-muted d-block">Scheduled For</small>
                                <strong>{{ $notification->scheduled_for->format('M d, Y H:i') }}</strong>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark notification as read
    document.querySelectorAll('.mark-read').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/notifications/${notificationId}/read`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    });

    // Delete notification
    document.querySelectorAll('.delete-notification').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;

            Swal.fire({
                title: 'Delete Notification',
                text: 'Are you sure you want to delete this notification?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/notifications/${notificationId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
