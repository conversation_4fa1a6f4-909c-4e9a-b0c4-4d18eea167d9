@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-file-text-line text-primary me-2"></i>Document Management
                        </h4>
                        <p class="text-muted mb-0">
                            Upload, store, and manage property documents with expiry alerts
                        </p>
                    </div>
                    <div>
                        @can('create', App\Models\Document::class)
                            <a href="{{ route('documents.create') }}" class="btn btn-primary">
                                <i class="ri-upload-line me-1"></i>Upload Document
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-file-text-line fs-1 text-primary mb-2"></i>
                        <h4 class="mb-1">{{ $stats['total'] }}</h4>
                        <p class="text-muted mb-0">Total Documents</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-alarm-warning-line fs-1 text-danger mb-2"></i>
                        <h4 class="mb-1">{{ $stats['expired'] }}</h4>
                        <p class="text-muted mb-0">Expired</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-time-line fs-1 text-warning mb-2"></i>
                        <h4 class="mb-1">{{ $stats['expiring_soon'] }}</h4>
                        <p class="text-muted mb-0">Expiring Soon</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-file-contract-line fs-1 text-info mb-2"></i>
                        <h4 class="mb-1">{{ $stats['contracts'] }}</h4>
                        <p class="text-muted mb-0">Contracts</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-shield-check-line fs-1 text-success mb-2"></i>
                        <h4 class="mb-1">{{ $stats['insurance'] }}</h4>
                        <p class="text-muted mb-0">Insurance</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('documents.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Document title, filename...">
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            @foreach($documentTypes as $type)
                                <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $type)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="expiring_soon" {{ request('status') == 'expiring_soon' ? 'selected' : '' }}>Expiring Soon</option>
                            <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="property_id" class="form-label">Property</label>
                        <select class="form-select" id="property_id" name="property_id">
                            <option value="">All Properties</option>
                            @foreach($properties as $property)
                                <option value="{{ $property->id }}" {{ request('property_id') == $property->id ? 'selected' : '' }}>
                                    {{ $property->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Documents List -->
        @if($documents->count() > 0)
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Document</th>
                                    <th>Type</th>
                                    <th>Property/Unit</th>
                                    <th>Expiry Date</th>
                                    <th>Status</th>
                                    <th>Uploaded By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($documents as $document)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    @if(in_array($document->file_type, ['jpg', 'jpeg', 'png']))
                                                        <i class="ri-image-line fs-4 text-info"></i>
                                                    @elseif($document->file_type === 'pdf')
                                                        <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                                    @elseif(in_array($document->file_type, ['doc', 'docx']))
                                                        <i class="ri-file-word-line fs-4 text-primary"></i>
                                                    @else
                                                        <i class="ri-file-line fs-4 text-muted"></i>
                                                    @endif
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">{{ $document->title }}</h6>
                                                    <small class="text-muted">{{ $document->file_name }}</small>
                                                    <br><small class="text-info">{{ $document->file_size_formatted }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $document->type)) }}</span>
                                        </td>
                                        <td>
                                            @if($document->property)
                                                <div>{{ $document->property->name }}</div>
                                                @if($document->unit)
                                                    <small class="text-muted">Unit {{ $document->unit->unit_number }}</small>
                                                @endif
                                            @else
                                                <span class="text-muted">No property assigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($document->expiry_date)
                                                {{ $document->expiry_date->format('M d, Y') }}
                                                @if($document->is_expired)
                                                    <br><small class="text-danger">Expired {{ $document->expiry_date->diffForHumans() }}</small>
                                                @elseif($document->is_expiring_soon)
                                                    <br><small class="text-warning">Expires in {{ $document->days_until_expiry }} days</small>
                                                @endif
                                            @else
                                                <span class="text-muted">No expiry date</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($document->is_expired)
                                                <span class="badge bg-danger">Expired</span>
                                            @elseif($document->is_expiring_soon)
                                                <span class="badge bg-warning">Expiring Soon</span>
                                            @else
                                                <span class="badge bg-success">Active</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($document->uploadedBy)
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        @if($document->uploadedBy->profile_photo)
                                                            <img src="{{ asset('storage/' . $document->uploadedBy->profile_photo) }}" alt="{{ $document->uploadedBy->name }}" class="rounded-circle">
                                                        @else
                                                            <div class="avatar-initial rounded-circle bg-primary">
                                                                {{ substr($document->uploadedBy->name, 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <span>{{ $document->uploadedBy->name }}</span>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="ri-more-line"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('documents.show', $document) }}">
                                                            <i class="ri-eye-line me-1"></i>View Details
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('documents.download', $document) }}">
                                                            <i class="ri-download-line me-1"></i>Download
                                                        </a>
                                                    </li>
                                                    @if(in_array($document->file_type, ['pdf', 'jpg', 'jpeg', 'png']))
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('documents.view', $document) }}" target="_blank">
                                                                <i class="ri-external-link-line me-1"></i>View File
                                                            </a>
                                                        </li>
                                                    @endif
                                                    @can('update', $document)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('documents.edit', $document) }}">
                                                                <i class="ri-edit-line me-1"></i>Edit Document
                                                            </a>
                                                        </li>
                                                    @endcan
                                                    @can('delete', $document)
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item text-danger delete-document" 
                                                                    data-document-id="{{ $document->id }}"
                                                                    data-document-title="{{ $document->title }}">
                                                                <i class="ri-delete-bin-line me-1"></i>Delete
                                                            </button>
                                                        </li>
                                                    @endcan
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $documents->links() }}
                    </div>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-file-text-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Documents Found</h5>
                    <p class="text-muted mb-4">
                        @if(request()->hasAny(['search', 'type', 'status', 'property_id']))
                            No documents match your current filters. Try adjusting your search criteria.
                        @else
                            You haven't uploaded any documents yet. Start by uploading your first document.
                        @endif
                    </p>
                    @can('create', App\Models\Document::class)
                        <a href="{{ route('documents.create') }}" class="btn btn-primary">
                            <i class="ri-upload-line me-1"></i>Upload Your First Document
                        </a>
                    @endcan
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete document functionality
    document.querySelectorAll('.delete-document').forEach(button => {
        button.addEventListener('click', function() {
            const documentId = this.dataset.documentId;
            const documentTitle = this.dataset.documentTitle;

            Swal.fire({
                title: 'Delete Document',
                html: `Are you sure you want to delete:<br><br><strong>${documentTitle}</strong>?<br><br><small class="text-danger">This action cannot be undone and will delete the file permanently.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/documents/${documentId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
