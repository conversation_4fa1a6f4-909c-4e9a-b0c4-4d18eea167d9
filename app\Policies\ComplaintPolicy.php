<?php

namespace App\Policies;

use App\Models\Complaint;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ComplaintPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_complaints') || $user->can('view_own_complaints');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Complaint $complaint): bool
    {
        // Admin can view all complaints
        if ($user->can('view_complaints')) {
            return true;
        }

        // Users can view their own complaints
        if ($user->can('view_own_complaints') && $complaint->complainant_id === $user->id) {
            return true;
        }

        // Property owners can view complaints for their properties
        if ($user->can('manage_own_properties') && $complaint->property && $complaint->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can view complaints for properties they manage
        if ($complaint->property && $complaint->property->manager_id === $user->id) {
            return true;
        }

        // Assigned staff can view complaints assigned to them
        if ($complaint->assigned_to === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_complaints');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Complaint $complaint): bool
    {
        // Admin can edit all complaints
        if ($user->can('edit_complaints')) {
            return true;
        }

        // Users can edit their own complaints (if not resolved)
        if ($user->can('view_own_complaints') && $complaint->complainant_id === $user->id && $complaint->status !== 'resolved') {
            return true;
        }

        // Property owners can edit complaints for their properties
        if ($user->can('manage_own_properties') && $complaint->property && $complaint->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can edit complaints for properties they manage
        if ($complaint->property && $complaint->property->manager_id === $user->id) {
            return true;
        }

        // Assigned staff can edit complaints assigned to them
        if ($complaint->assigned_to === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Complaint $complaint): bool
    {
        // Admin can delete all complaints
        if ($user->can('delete_complaints')) {
            return true;
        }

        // Users can delete their own complaints (if not resolved)
        if ($user->can('view_own_complaints') && $complaint->complainant_id === $user->id && $complaint->status === 'open') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Complaint $complaint): bool
    {
        return $this->delete($user, $complaint);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Complaint $complaint): bool
    {
        return $user->can('delete_complaints');
    }

    /**
     * Determine whether the user can resolve the complaint.
     */
    public function resolve(User $user, Complaint $complaint): bool
    {
        // Admin can resolve all complaints
        if ($user->can('resolve_complaints')) {
            return true;
        }

        // Property owners can resolve complaints for their properties
        if ($user->can('manage_own_properties') && $complaint->property && $complaint->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can resolve complaints for properties they manage
        if ($complaint->property && $complaint->property->manager_id === $user->id) {
            return true;
        }

        // Assigned staff can resolve complaints assigned to them
        if ($complaint->assigned_to === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can assign the complaint.
     */
    public function assign(User $user, Complaint $complaint): bool
    {
        // Admin can assign all complaints
        if ($user->can('assign_complaints')) {
            return true;
        }

        // Property owners can assign complaints for their properties
        if ($user->can('manage_own_properties') && $complaint->property && $complaint->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can assign complaints for properties they manage
        if ($complaint->property && $complaint->property->manager_id === $user->id) {
            return true;
        }

        return false;
    }
}
