<?php

namespace App\Policies;

use App\Models\Document;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class DocumentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All users can view documents
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Document $document): bool
    {
        // Admin can view all documents
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can view documents for their properties
        if ($user->hasRole('property_owner') && $document->property_id) {
            return $user->ownedProperties->contains($document->property_id);
        }

        // Tenants can view their own documents
        if ($document->tenant_id === $user->id) {
            return true;
        }

        // Document uploader can view their uploaded documents
        return $document->uploaded_by === $user->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'property_owner', 'property_manager', 'receptionist']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Document $document): bool
    {
        // Admin can update all documents
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can update documents for their properties
        if ($user->hasRole('property_owner') && $document->property_id) {
            return $user->ownedProperties->contains($document->property_id);
        }

        // Document uploader can update their uploaded documents
        return $document->uploaded_by === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Document $document): bool
    {
        // Admin can delete all documents
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can delete documents for their properties
        if ($user->hasRole('property_owner') && $document->property_id) {
            return $user->ownedProperties->contains($document->property_id);
        }

        // Document uploader can delete their uploaded documents
        return $document->uploaded_by === $user->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Document $document): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Document $document): bool
    {
        return $user->hasRole('admin');
    }
}
