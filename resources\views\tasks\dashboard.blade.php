@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-task-line text-primary me-2"></i>Tasks Dashboard
                        </h4>
                        <p class="text-muted mb-0">
                            Overview of all tasks, assignments, and progress tracking
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create Task
                        </a>
                        <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-list-check me-1"></i>All Tasks
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['total'] ?? 0 }}</h3>
                                <p class="mb-0">Total Tasks</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-task-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['pending'] ?? 0 }}</h3>
                                <p class="mb-0">Pending Tasks</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['in_progress'] ?? 0 }}</h3>
                                <p class="mb-0">In Progress</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-play-circle-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['overdue'] ?? 0 }}</h3>
                                <p class="mb-0">Overdue Tasks</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Tasks -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Tasks</h5>
                            <a href="{{ route('tasks.index') }}" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($recentTasks && $recentTasks->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Task</th>
                                            <th>Type</th>
                                            <th>Priority</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($recentTasks as $task)
                                            <tr>
                                                <td>
                                                    <div>
                                                        <h6 class="mb-1">{{ $task->title }}</h6>
                                                        <small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        {{ ucfirst(str_replace('_', ' ', $task->type)) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ $task->priority === 'urgent' ? 'danger' : ($task->priority === 'high' ? 'warning' : ($task->priority === 'medium' ? 'info' : 'secondary')) }}">
                                                        {{ ucfirst($task->priority) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($task->due_date)
                                                        <span class="{{ $task->is_overdue ? 'text-danger' : ($task->is_due_today ? 'text-warning' : 'text-muted') }}">
                                                            {{ $task->due_date->format('M d, Y') }}
                                                        </span>
                                                    @else
                                                        <span class="text-muted">No due date</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : 'warning') }}">
                                                        {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ route('tasks.show', $task) }}" class="btn btn-outline-primary" title="View">
                                                            <i class="ri-eye-line"></i>
                                                        </a>
                                                        @if($task->status !== 'completed')
                                                            <a href="{{ route('tasks.edit', $task) }}" class="btn btn-outline-warning" title="Edit">
                                                                <i class="ri-edit-line"></i>
                                                            </a>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="ri-task-line fs-1 text-muted"></i>
                                <h5 class="mt-3 mb-2">No Tasks Found</h5>
                                <p class="text-muted mb-3">Start by creating your first task to track your work.</p>
                                <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line me-1"></i>Create First Task
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Task Summary -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Task Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span>Completed</span>
                                <span class="text-success">{{ $stats['completed'] ?? 0 }}</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: {{ $stats['total'] > 0 ? (($stats['completed'] ?? 0) / $stats['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span>In Progress</span>
                                <span class="text-info">{{ $stats['in_progress'] ?? 0 }}</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: {{ $stats['total'] > 0 ? (($stats['in_progress'] ?? 0) / $stats['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span>Pending</span>
                                <span class="text-warning">{{ $stats['pending'] ?? 0 }}</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: {{ $stats['total'] > 0 ? (($stats['pending'] ?? 0) / $stats['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span>Overdue</span>
                                <span class="text-danger">{{ $stats['overdue'] ?? 0 }}</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-danger" style="width: {{ $stats['total'] > 0 ? (($stats['overdue'] ?? 0) / $stats['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                                <i class="ri-add-line me-2"></i>Create New Task
                            </a>
                            <a href="{{ route('tasks.index', ['status' => 'pending']) }}" class="btn btn-outline-warning">
                                <i class="ri-time-line me-2"></i>View Pending Tasks
                            </a>
                            <a href="{{ route('tasks.index', ['status' => 'overdue']) }}" class="btn btn-outline-danger">
                                <i class="ri-alarm-warning-line me-2"></i>View Overdue Tasks
                            </a>
                            <a href="{{ route('tasks.index', ['status' => 'completed']) }}" class="btn btn-outline-success">
                                <i class="ri-check-line me-2"></i>View Completed Tasks
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        window.location.reload();
    }, 300000); // 5 minutes
});
</script>
@endsection
