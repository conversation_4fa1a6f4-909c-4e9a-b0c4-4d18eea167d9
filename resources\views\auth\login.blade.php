<!DOCTYPE html>
<html lang="en">

  
<!-- Mirrored from bootstrapget.com/demos/clove-dental-care-admin/login.html by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 28 Jun 2025 15:02:22 GMT -->
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Welcome - {{ config('app.name')}}</title>

    <!-- Meta -->
    <meta name="description" content="Property Management System">
    <meta property="og:title" content="PMS - INFINUS">
    <meta property="og:description" content="Software to manage property system in malaysia">
    <meta property="og:type" content="Application Web">
    <!-- <link rel="shortcut icon" href="assets/images/favicon.svg"> -->

    <!-- *************
			************ CSS Files *************
		************* -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/remix/remixicon.css') }} ">
    <link rel="stylesheet" href="{{ asset('assets/css/main.min.css') }}">

  </head>

  <body class="login-bg">

    <!-- Container starts -->
    <div class="container">

      <!-- Auth wrapper starts -->
      <div class="auth-wrapper">

        <!-- Form starts -->
        <form action="{{ route('login.submit') }}" method="post">
          @csrf

          <div class="auth-box">
            <!-- <a href="index.html" > -->
              <!-- <img src="{{ asset('assets/images/logo.svg') }}" alt="Bootstrap Gallery"> -->

            <!-- </a> -->
            <h2 class="auth-logo mb-4"><b>INFINUS PMS</b></h2>

            <h4 class="mb-4">Login</h4>

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="mb-3">
              <label class="form-label" for="email">Your email <span class="text-danger">*</span></label>
              <input type="email" id="email" name="email" class="form-control @error('email') is-invalid @enderror"
                     placeholder="Enter your email" value="{{ old('email') }}" required>
              @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-2">
              <label class="form-label" for="password">Your password <span class="text-danger">*</span></label>
              <div class="input-group">
                <input type="password" id="password" name="password" class="form-control @error('password') is-invalid @enderror"
                       placeholder="Enter password" required>
                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                  <i class="ri-eye-line text-primary" id="toggleIcon"></i>
                </button>
              </div>
              @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                <label class="form-check-label" for="remember">
                  Remember me
                </label>
              </div>
            </div>

            <div class="mb-3 d-grid gap-2">
              <button type="submit" class="btn btn-primary">Login</button>
            </div>

            <div class="text-center">
              <small class="text-muted">
                Demo Admin: <EMAIL> / 12345678<br>
                Demo Owner: <EMAIL> / password<br>
                Demo Tenant: <EMAIL> / password
              </small>
            </div>

          </div>

        </form>
        <!-- Form ends -->

      </div>
      <!-- Auth wrapper ends -->

    </div>
    <!-- Container ends -->

    <script>
      function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
          passwordInput.type = 'text';
          toggleIcon.className = 'ri-eye-off-line text-primary';
        } else {
          passwordInput.type = 'password';
          toggleIcon.className = 'ri-eye-line text-primary';
        }
      }
    </script>
  </body>

</html>