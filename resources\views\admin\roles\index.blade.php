@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Role Management</h5>
                <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                    <i class="ri-add-line me-1"></i>Add New Role
                </a>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Role Name</th>
                                <th>Permissions Count</th>
                                <th>Users Count</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($roles as $role)
                                <tr>
                                    <td>{{ $role->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="ri-shield-user-line text-white"></i>
                                            </div>
                                            <div>
                                                <strong>{{ ucfirst(str_replace('_', ' ', $role->name)) }}</strong>
                                                @if(in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']))
                                                    <span class="badge bg-warning ms-2">Core Role</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $role->permissions->count() }} permissions</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ $role->users->count() }} users</span>
                                    </td>
                                    <td>{{ $role->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-outline-info" title="View Details">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-outline-warning" title="Edit Role">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            @if(!in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']))
                                                @if($role->users->count() > 0)
                                                    <button type="button" class="btn btn-outline-danger" title="Cannot delete - has users assigned" disabled>
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @else
                                                    <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete the role \'{{ $role->name }}\'? This action cannot be undone.')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-outline-danger" title="Delete Role">
                                                            <i class="ri-delete-bin-line"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            @else
                                                <button type="button" class="btn btn-outline-secondary" title="Core role - cannot be deleted" disabled>
                                                    <i class="ri-lock-line"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ri-shield-user-line fs-1"></i>
                                            <p class="mt-2">No roles found</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $roles->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role Details Modal -->
<div class="modal fade" id="roleDetailsModal" tabindex="-1" aria-labelledby="roleDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roleDetailsModalLabel">Role Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="roleDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function viewRoleDetails(roleId) {
    // You can implement AJAX loading here if needed
    $('#roleDetailsModal').modal('show');
}
</script>
@endsection
