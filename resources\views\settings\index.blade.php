@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-settings-line text-primary me-2"></i>System Settings
                        </h4>
                        <p class="text-muted mb-0">
                            Configure system preferences, company information, and application settings
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-info" onclick="testEmailConfig()">
                            <i class="ri-mail-send-line me-1"></i>Test Email
                        </button>
                        <button class="btn btn-outline-warning" onclick="createBackup()">
                            <i class="ri-database-line me-1"></i>Create Backup
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Form -->
        <form action="{{ route('settings.update') }}" method="POST" id="settingsForm">
            @csrf

            <!-- Company Information -->
            <div class="card mb-4" id="company">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ri-building-line me-2"></i>Company Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('company_name') is-invalid @enderror"
                                       id="company_name" name="company_name"
                                       value="{{ old('company_name', $settings['company']->where('key', 'company_name')->first()->value ?? 'Property Management System') }}"
                                       required>
                                @error('company_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_email" class="form-label">Company Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('company_email') is-invalid @enderror"
                                       id="company_email" name="company_email"
                                       value="{{ old('company_email', $settings['company']->where('key', 'company_email')->first()->value ?? '') }}"
                                       required>
                                @error('company_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_phone" class="form-label">Company Phone</label>
                                <input type="tel" class="form-control @error('company_phone') is-invalid @enderror"
                                       id="company_phone" name="company_phone"
                                       value="{{ old('company_phone', $settings['company']->where('key', 'company_phone')->first()->value ?? '') }}">
                                @error('company_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_website" class="form-label">Company Website</label>
                                <input type="url" class="form-control @error('company_website') is-invalid @enderror"
                                       id="company_website" name="company_website"
                                       value="{{ old('company_website', $settings['company']->where('key', 'company_website')->first()->value ?? '') }}">
                                @error('company_website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="company_address" class="form-label">Company Address</label>
                        <textarea class="form-control @error('company_address') is-invalid @enderror"
                                  id="company_address" name="company_address" rows="3">{{ old('company_address', $settings['company']->where('key', 'company_address')->first()->value ?? '') }}</textarea>
                        @error('company_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Logo Upload Section -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Header Logo</label>
                                <div class="card">
                                    <div class="card-body text-center">
                                        @php
                                            $headerLogo = $settings['company']->where('key', 'header_logo')->first()->value ?? '';
                                        @endphp
                                        @if($headerLogo && Storage::disk('public')->exists($headerLogo))
                                            <img src="{{ asset('storage/' . $headerLogo) }}" alt="Header Logo" class="img-fluid mb-2" style="max-height: 100px;">
                                        @else
                                            <i class="ri-image-line fs-1 text-muted mb-2"></i>
                                            <p class="text-muted">No header logo uploaded</p>
                                        @endif
                                        <form action="{{ route('settings.upload-logo') }}" method="POST" enctype="multipart/form-data" class="logo-upload-form">
                                            @csrf
                                            <input type="hidden" name="type" value="header">
                                            <input type="file" name="logo" accept="image/*" class="form-control mb-2">
                                            <button type="submit" class="btn btn-sm btn-primary">Upload Header Logo</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Login Page Logo</label>
                                <div class="card">
                                    <div class="card-body text-center">
                                        @php
                                            $loginLogo = $settings['company']->where('key', 'login_logo')->first()->value ?? '';
                                        @endphp
                                        @if($loginLogo && Storage::disk('public')->exists($loginLogo))
                                            <img src="{{ asset('storage/' . $loginLogo) }}" alt="Login Logo" class="img-fluid mb-2" style="max-height: 100px;">
                                        @else
                                            <i class="ri-image-line fs-1 text-muted mb-2"></i>
                                            <p class="text-muted">No login logo uploaded</p>
                                        @endif
                                        <form action="{{ route('settings.upload-logo') }}" method="POST" enctype="multipart/form-data" class="logo-upload-form">
                                            @csrf
                                            <input type="hidden" name="type" value="login">
                                            <input type="file" name="logo" accept="image/*" class="form-control mb-2">
                                            <button type="submit" class="btn btn-sm btn-primary">Upload Login Logo</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Preferences -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ri-settings-2-line me-2"></i>System Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                                <select class="form-select @error('timezone') is-invalid @enderror"
                                        id="timezone" name="timezone" required>
                                    <option value="UTC" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? 'UTC') == 'UTC' ? 'selected' : '' }}>UTC</option>
                                    <option value="America/New_York" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                    <option value="America/Chicago" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                    <option value="America/Denver" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                    <option value="America/Los_Angeles" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                    <option value="Europe/London" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'Europe/London' ? 'selected' : '' }}>London</option>
                                    <option value="Asia/Kuala_Lumpur" {{ old('timezone', $settings['system']->where('key', 'timezone')->first()->value ?? '') == 'Asia/Kuala_Lumpur' ? 'selected' : '' }}>Kuala Lumpur</option>
                                </select>
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_format" class="form-label">Date Format <span class="text-danger">*</span></label>
                                <select class="form-select @error('date_format') is-invalid @enderror"
                                        id="date_format" name="date_format" required>
                                    <option value="Y-m-d" {{ old('date_format', $settings['system']->where('key', 'date_format')->first()->value ?? 'Y-m-d') == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                    <option value="m/d/Y" {{ old('date_format', $settings['system']->where('key', 'date_format')->first()->value ?? '') == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                    <option value="d/m/Y" {{ old('date_format', $settings['system']->where('key', 'date_format')->first()->value ?? '') == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                    <option value="M d, Y" {{ old('date_format', $settings['system']->where('key', 'date_format')->first()->value ?? '') == 'M d, Y' ? 'selected' : '' }}>Mon DD, YYYY</option>
                                </select>
                                @error('date_format')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="currency" class="form-label">Currency <span class="text-danger">*</span></label>
                                <select class="form-select @error('currency') is-invalid @enderror"
                                        id="currency" name="currency" required>
                                    <option value="USD" {{ old('currency', $settings['system']->where('key', 'currency')->first()->value ?? 'USD') == 'USD' ? 'selected' : '' }}>USD ($)</option>
                                    <option value="EUR" {{ old('currency', $settings['system']->where('key', 'currency')->first()->value ?? '') == 'EUR' ? 'selected' : '' }}>EUR (€)</option>
                                    <option value="GBP" {{ old('currency', $settings['system']->where('key', 'currency')->first()->value ?? '') == 'GBP' ? 'selected' : '' }}>GBP (£)</option>
                                    <option value="MYR" {{ old('currency', $settings['system']->where('key', 'currency')->first()->value ?? '') == 'MYR' ? 'selected' : '' }}>MYR (RM)</option>
                                    <option value="SGD" {{ old('currency', $settings['system']->where('key', 'currency')->first()->value ?? '') == 'SGD' ? 'selected' : '' }}>SGD (S$)</option>
                                </select>
                                @error('currency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Configuration -->
            <div class="card mb-4" id="email">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ri-mail-settings-line me-2"></i>Email Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_driver" class="form-label">Mail Driver <span class="text-danger">*</span></label>
                                <select class="form-select @error('mail_driver') is-invalid @enderror"
                                        id="mail_driver" name="mail_driver" required>
                                    <option value="smtp" {{ old('mail_driver', $settings['email']->where('key', 'mail_driver')->first()->value ?? 'smtp') == 'smtp' ? 'selected' : '' }}>SMTP</option>
                                    <option value="sendmail" {{ old('mail_driver', $settings['email']->where('key', 'mail_driver')->first()->value ?? '') == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                                    <option value="mailgun" {{ old('mail_driver', $settings['email']->where('key', 'mail_driver')->first()->value ?? '') == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                                    <option value="ses" {{ old('mail_driver', $settings['email']->where('key', 'mail_driver')->first()->value ?? '') == 'ses' ? 'selected' : '' }}>Amazon SES</option>
                                    <option value="postmark" {{ old('mail_driver', $settings['email']->where('key', 'mail_driver')->first()->value ?? '') == 'postmark' ? 'selected' : '' }}>Postmark</option>
                                </select>
                                @error('mail_driver')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_host" class="form-label">Mail Host</label>
                                <input type="text" class="form-control @error('mail_host') is-invalid @enderror"
                                       id="mail_host" name="mail_host"
                                       value="{{ old('mail_host', $settings['email']->where('key', 'mail_host')->first()->value ?? '') }}"
                                       placeholder="smtp.gmail.com">
                                @error('mail_host')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_port" class="form-label">Mail Port</label>
                                <input type="number" class="form-control @error('mail_port') is-invalid @enderror"
                                       id="mail_port" name="mail_port"
                                       value="{{ old('mail_port', $settings['email']->where('key', 'mail_port')->first()->value ?? 587) }}"
                                       placeholder="587">
                                @error('mail_port')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_encryption" class="form-label">Encryption</label>
                                <select class="form-select @error('mail_encryption') is-invalid @enderror"
                                        id="mail_encryption" name="mail_encryption">
                                    <option value="">None</option>
                                    <option value="tls" {{ old('mail_encryption', $settings['email']->where('key', 'mail_encryption')->first()->value ?? 'tls') == 'tls' ? 'selected' : '' }}>TLS</option>
                                    <option value="ssl" {{ old('mail_encryption', $settings['email']->where('key', 'mail_encryption')->first()->value ?? '') == 'ssl' ? 'selected' : '' }}>SSL</option>
                                </select>
                                @error('mail_encryption')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_username" class="form-label">Mail Username</label>
                                <input type="text" class="form-control @error('mail_username') is-invalid @enderror"
                                       id="mail_username" name="mail_username"
                                       value="{{ old('mail_username', $settings['email']->where('key', 'mail_username')->first()->value ?? '') }}"
                                       placeholder="<EMAIL>">
                                @error('mail_username')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_password" class="form-label">Mail Password</label>
                                <input type="password" class="form-control @error('mail_password') is-invalid @enderror"
                                       id="mail_password" name="mail_password"
                                       value="{{ old('mail_password', $settings['email']->where('key', 'mail_password')->first()->value ?? '') }}"
                                       placeholder="Enter mail password">
                                @error('mail_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_from_address" class="form-label">From Address</label>
                                <input type="email" class="form-control @error('mail_from_address') is-invalid @enderror"
                                       id="mail_from_address" name="mail_from_address"
                                       value="{{ old('mail_from_address', $settings['email']->where('key', 'mail_from_address')->first()->value ?? '') }}"
                                       placeholder="<EMAIL>">
                                @error('mail_from_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mail_from_name" class="form-label">From Name</label>
                                <input type="text" class="form-control @error('mail_from_name') is-invalid @enderror"
                                       id="mail_from_name" name="mail_from_name"
                                       value="{{ old('mail_from_name', $settings['email']->where('key', 'mail_from_name')->first()->value ?? '') }}"
                                       placeholder="Your Company Name">
                                @error('mail_from_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-info" onclick="testEmailConfig()">
                            <i class="ri-mail-send-line me-1"></i>Test Email Configuration
                        </button>
                    </div>
                </div>
            </div>

            <!-- Backup Settings -->
            <div class="card mb-4" id="backup">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ri-database-line me-2"></i>Backup & Restore
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="backup_enabled" name="backup_enabled" value="1"
                                           {{ old('backup_enabled', $settings['backup']->where('key', 'backup_enabled')->first()->value ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="backup_enabled">
                                        Enable Automatic Backups
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label">Backup Frequency</label>
                                <select class="form-select @error('backup_frequency') is-invalid @enderror"
                                        id="backup_frequency" name="backup_frequency">
                                    <option value="daily" {{ old('backup_frequency', $settings['backup']->where('key', 'backup_frequency')->first()->value ?? 'weekly') == 'daily' ? 'selected' : '' }}>Daily</option>
                                    <option value="weekly" {{ old('backup_frequency', $settings['backup']->where('key', 'backup_frequency')->first()->value ?? 'weekly') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                    <option value="monthly" {{ old('backup_frequency', $settings['backup']->where('key', 'backup_frequency')->first()->value ?? '') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                </select>
                                @error('backup_frequency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="backup_retention_days" class="form-label">Retention Days</label>
                                <input type="number" class="form-control @error('backup_retention_days') is-invalid @enderror"
                                       id="backup_retention_days" name="backup_retention_days"
                                       value="{{ old('backup_retention_days', $settings['backup']->where('key', 'backup_retention_days')->first()->value ?? 30) }}"
                                       min="1" max="365">
                                @error('backup_retention_days')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2 mb-3">
                        <button type="button" class="btn btn-outline-warning" onclick="createBackup()">
                            <i class="ri-database-line me-1"></i>Create Backup Now
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="listBackups()">
                            <i class="ri-file-list-line me-1"></i>View Backups
                        </button>
                    </div>

                    <div id="backup-list" class="mt-3" style="display: none;">
                        <h6>Available Backups</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="backup-table-body">
                                    <!-- Backup list will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                    <i class="ri-refresh-line me-1"></i>Reset
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>Save Settings
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Logo upload forms
    document.querySelectorAll('.logo-upload-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="ri-loader-line me-1"></i>Uploading...';
            submitBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Success', data.message, 'success').then(() => {
                        window.location.reload();
                    });
                } else {
                    Swal.fire('Error', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error', 'An error occurred while uploading the logo.', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    });
});

function testEmailConfig() {
    Swal.fire({
        title: 'Test Email Configuration',
        input: 'email',
        inputLabel: 'Enter test email address',
        inputPlaceholder: '<EMAIL>',
        showCancelButton: true,
        confirmButtonText: 'Send Test Email',
        showLoaderOnConfirm: true,
        preConfirm: (email) => {
            return fetch('{{ route("settings.test-email") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ test_email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message);
                }
                return data;
            })
            .catch(error => {
                Swal.showValidationMessage(`Request failed: ${error.message}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Success', result.value.message, 'success');
        }
    });
}

function createBackup() {
    Swal.fire({
        title: 'Create Backup',
        text: 'This will create a backup of your database. Continue?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Create Backup',
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return fetch('{{ route("settings.backup") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message);
                }
                return data;
            })
            .catch(error => {
                Swal.showValidationMessage(`Request failed: ${error.message}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Success', result.value.message, 'success');
        }
    });
}

function listBackups() {
    fetch('{{ route("settings.backups") }}')
        .then(response => response.json())
        .then(backups => {
            const backupList = document.getElementById('backup-list');
            const tableBody = document.getElementById('backup-table-body');

            tableBody.innerHTML = '';

            if (backups.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No backups found</td></tr>';
            } else {
                backups.forEach(backup => {
                    const row = `
                        <tr>
                            <td>${backup.name}</td>
                            <td>${backup.size}</td>
                            <td>${backup.date}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('settings.backup.download', '') }}/${backup.name}" class="btn btn-outline-primary">
                                        <i class="ri-download-line"></i>
                                    </a>
                                    <button class="btn btn-outline-danger" onclick="deleteBackup('${backup.name}')">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });
            }

            backupList.style.display = 'block';
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to load backup list.', 'error');
        });
}

function deleteBackup(filename) {
    Swal.fire({
        title: 'Delete Backup',
        text: `Are you sure you want to delete ${filename}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`{{ route('settings.backup.delete', '') }}/${filename}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Deleted', data.message, 'success');
                    listBackups(); // Refresh the list
                } else {
                    Swal.fire('Error', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error', 'Failed to delete backup.', 'error');
            });
        }
    });
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('settingsForm').reset();
    }
}
</script>
@endsection