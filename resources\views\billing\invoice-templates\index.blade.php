@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-file-text-line text-primary me-2"></i>Invoice Templates
                        </h4>
                        <p class="text-muted mb-0">
                            Manage invoice templates for consistent billing
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('invoice-templates.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create Template
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-more-line me-1"></i>Actions
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ri-download-line me-2"></i>Export Templates</a></li>
                                <li><a class="dropdown-item" href="#"><i class="ri-upload-line me-2"></i>Import Templates</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $templates->count() }}</h3>
                                <p class="mb-0">Total Templates</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-file-text-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">1</h3>
                                <p class="mb-0">Default Template</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-star-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $templates->where('status', 'active')->count() }}</h3>
                                <p class="mb-0">Active Templates</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-check-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $templates->where('status', 'draft')->count() }}</h3>
                                <p class="mb-0">Draft Templates</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-draft-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Templates List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Invoice Templates</h5>
            </div>
            <div class="card-body">
                @if($templates->isEmpty())
                    <div class="text-center py-5">
                        <i class="ri-file-text-line fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">No Invoice Templates Found</h5>
                        <p class="text-muted mb-4">Create your first invoice template to get started with consistent billing.</p>
                        <a href="{{ route('invoice-templates.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First Template
                        </a>
                    </div>
                @else
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Template Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Default</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Placeholder for template data -->
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        Template data will be displayed here when implemented
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Template management scripts will go here
    console.log('Invoice Templates page loaded');
</script>
@endpush
