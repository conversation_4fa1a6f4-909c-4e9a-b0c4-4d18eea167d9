<svg xmlns="http://www.w3.org/2000/svg" width="538.728" height="500.904" viewBox="0 0 538.728 500.904">
  <g id="_2" data-name="2" transform="translate(0 -0.02)">
    <path id="Path_763" data-name="Path 763" d="M483.88,406.333H11.092A11.084,11.084,0,0,1,0,395.242H0A11.084,11.084,0,0,1,11.092,384.15H483.88a11.084,11.084,0,0,1,11.092,11.092h0a11.084,11.084,0,0,1-11.092,11.091Z" transform="translate(0 94.591)" fill="#e6edff"/>
    <path id="Path_764" data-name="Path 764" d="M485.707,273.123c-.2-.125-.4-.237-.611-.361-14.344-8.35-31.256-11.565-47.544-15.591-15.142-3.739-32.141-8.761-43.768-19.79-14.132-13.41-12.163-31.891-6.73-48.678,5.708-17.622,11.092-35.381,8.562-54.087A104.622,104.622,0,0,0,367.887,76.7c-23.666-24.825-60.343-37.986-95.475-34.259-18.151,1.925-35.053,7.926-50.828,16.685-24.751,13.743-46.357,34.374-64.437,56.033-19.292,23.1-30.67,49.339-52.679,71.285-21.747,21.685-46.186,41.226-64.481,65.8C15.623,284.963,3.759,326.675,11.66,365.981s36.8,75.174,76.37,89.368c25.2,9.035,52.841,9.222,79.785,9.3,55.981.15,112.025.287,167.82-3.951,36.714-2.792,73.927-7.664,107.377-22.246s63.073-40.191,74.039-73.441c10.892-33.038-.436-73.429-31.356-91.861Z" transform="translate(2.273 10.293)" fill="#d5e9e8"/>
    <path id="Path_765" data-name="Path 765" d="M38.091,167.606a18.64,18.64,0,0,0-4.811-8.25c-1.259-.972-2.517-1.944-3.789-2.929a18.581,18.581,0,0,0-9.434-2.555h-.922a14.037,14.037,0,0,0-7.2,1.608,14,14,0,0,0-6.019,3.863,14,14,0,0,0-3.863,6.019,14.037,14.037,0,0,0-1.608,7.2l.673,4.973a18.64,18.64,0,0,0,4.811,8.25c1.259.972,2.517,1.944,3.789,2.929a18.581,18.581,0,0,0,9.434,2.555h.922a14.037,14.037,0,0,0,7.2-1.608,14,14,0,0,0,6.019-3.863,14,14,0,0,0,3.863-6.019,14.037,14.037,0,0,0,1.608-7.2Z" transform="translate(0.106 37.883)" fill="#d5e9e8"/>
    <circle id="Ellipse_147" data-name="Ellipse 147" cx="40.391" cy="40.391" r="40.391" transform="translate(457.946 146.192)" fill="#88bebb"/>
    <path id="Path_766" data-name="Path 766" d="M282.87.282a26.911,26.911,0,0,0-5.022,1.508,4.237,4.237,0,0,1,.287.374,4.485,4.485,0,1,1-7.6,4.761c-.086-.151-.149-.313-.223-.462A28.369,28.369,0,0,0,266.7,10.29c.15.062.312.112.461.187a4.482,4.482,0,1,1-4.673,7.6,26.378,26.378,0,0,0-1.221,5.159,4.054,4.054,0,0,1,.561-.075,4.483,4.483,0,0,1,.312,8.961c-.212,0-.424-.025-.636-.037a27.133,27.133,0,0,0,1.52,5.135,4.856,4.856,0,0,1,.5-.386,4.485,4.485,0,1,1,4.761,7.6,5.809,5.809,0,0,1-.623.311,27.473,27.473,0,0,0,3.913,3.7,4.562,4.562,0,0,1,.262-.636,4.48,4.48,0,1,1,7.914,4.2,3.488,3.488,0,0,1-.411.611,26.605,26.605,0,0,0,5.272,1.234,4.271,4.271,0,0,1-.1-.7,4.483,4.483,0,0,1,8.961-.312,4.754,4.754,0,0,1-.05.735,26.835,26.835,0,0,0,5.184-1.57,3.958,3.958,0,0,1-.436-.561,4.485,4.485,0,1,1,7.6-4.762,4.982,4.982,0,0,1,.311.637,27.573,27.573,0,0,0,3.676-3.951,5.224,5.224,0,0,1-.611-.249,4.48,4.48,0,1,1,4.2-7.914,4.247,4.247,0,0,1,.548.374,26.738,26.738,0,0,0,1.2-5.222,4.942,4.942,0,0,1-.6.087,4.483,4.483,0,0,1-.312-8.961c.2,0,.4.025.586.037a27.82,27.82,0,0,0-1.558-5.072,5.345,5.345,0,0,1-.424.336,4.485,4.485,0,1,1-4.762-7.6,4.2,4.2,0,0,1,.475-.236,27.609,27.609,0,0,0-3.851-3.589c-.062.15-.112.3-.187.449a4.48,4.48,0,1,1-7.914-4.2,4.7,4.7,0,0,1,.274-.411A27.946,27.946,0,0,0,291.706.02a2.776,2.776,0,0,1,.062.449,4.483,4.483,0,0,1-8.961.312c0-.162.025-.324.037-.486Zm4.723,11.067a15.582,15.582,0,1,1-15.017,16.126,15.582,15.582,0,0,1,15.017-16.126Z" transform="translate(64.334)" fill="#d5e9e8"/>
    <path id="Ellipse_148" data-name="Ellipse 148" d="M46.143,0A46.3,46.3,0,1,1,0,46.3,46.221,46.221,0,0,1,46.143,0Z" transform="translate(8.911 68.19)" fill="#d5e9e8"/>
    <path id="Path_767" data-name="Path 767" d="M51.344,94.94,15.361,82.34a38.257,38.257,0,0,0,35.987,50.847,38.069,38.069,0,0,0,19.618-5.446L51.344,94.952Z" transform="translate(2.611 20.271)" fill="#566fe2"/>
    <path id="Path_768" data-name="Path 768" d="M48.4,91.87l19.868,32.639A38.258,38.258,0,0,0,86.53,91.87H48.411Z" transform="translate(7.532 22.618)" fill="#88bebb"/>
    <path id="Path_769" data-name="Path 769" d="M48.59,95.963l38.118-.523A38.309,38.309,0,0,0,74.4,67.81L48.6,95.963Z" transform="translate(7.558 16.693)" fill="#fefeff"/>
    <rect id="Rectangle_508" data-name="Rectangle 508" width="15.092" height="36.266" transform="translate(165.09 69.212)" fill="#566fe2"/>
    <rect id="Rectangle_509" data-name="Rectangle 509" width="15.092" height="54.012" transform="translate(188.42 51.465)" fill="#566fe2"/>
    <rect id="Rectangle_510" data-name="Rectangle 510" width="15.092" height="78.501" transform="translate(211.737 26.964)" fill="#566fe2"/>
    <rect id="Rectangle_511" data-name="Rectangle 511" width="15.092" height="44.167" transform="translate(235.067 61.31)" fill="#566fe2"/>
    <path id="Path_770" data-name="Path 770" d="M125.99,22.25V99.667h103.1" transform="translate(31.025 5.474)" fill="none" stroke="#566fe2" stroke-miterlimit="10" stroke-width="0.88"/>
    <path id="Path_771" data-name="Path 771" d="M320.315,41.058a52.251,52.251,0,0,0-9.87,2.966c.187.249.4.461.573.735a8.807,8.807,0,0,1-14.93,9.347,8.6,8.6,0,0,1-.449-.9,54.111,54.111,0,0,0-7.1,7.527,9.333,9.333,0,0,1,.91.374,8.812,8.812,0,0,1-8.263,15.566,9.378,9.378,0,0,1-.935-.623A53.978,53.978,0,0,0,277.83,86.2a7.748,7.748,0,0,1,1.1-.15,8.817,8.817,0,1,1,.623,17.622,8.263,8.263,0,0,1-1.246-.087,52.731,52.731,0,0,0,3,10.1,10.369,10.369,0,0,1,.985-.773,8.807,8.807,0,0,1,9.347,14.93,7.9,7.9,0,0,1-1.221.6,53.9,53.9,0,0,0,7.7,7.278,8.2,8.2,0,0,1,.511-1.246,8.812,8.812,0,1,1,15.566,8.263,9.076,9.076,0,0,1-.8,1.2,52.333,52.333,0,0,0,10.369,2.43,8.407,8.407,0,0,1-.187-1.383,8.817,8.817,0,0,1,17.622-.623,9.448,9.448,0,0,1-.1,1.458,53.635,53.635,0,0,0,10.194-3.091,9.169,9.169,0,0,1-.847-1.1,8.807,8.807,0,0,1,14.93-9.347A9.837,9.837,0,0,1,366,133.53a53.879,53.879,0,0,0,7.228-7.752,8.508,8.508,0,0,1-1.209-.5,8.812,8.812,0,1,1,8.263-15.566,10.921,10.921,0,0,1,1.084.723,53.089,53.089,0,0,0,2.343-10.257,9.026,9.026,0,0,1-1.184.162,8.821,8.821,0,1,1,.534-17.545,53.154,53.154,0,0,0-3.053-9.97,7.243,7.243,0,0,1-.847.648,8.807,8.807,0,0,1-9.347-14.93,9.284,9.284,0,0,1,.935-.461,52.735,52.735,0,0,0-7.577-7.054,7.752,7.752,0,0,1-.361.885,8.812,8.812,0,1,1-15.566-8.263,7.679,7.679,0,0,1,.548-.81,53.779,53.779,0,0,0-10.045-2.33c.037.3.112.586.125.885a8.817,8.817,0,0,1-17.622.623c0-.324.037-.636.062-.96Zm9.11,16.5h-.012Z" transform="translate(68.415 9.971)" fill="#88bebb"/>
    <path id="Path_772" data-name="Path 772" d="M276.636,82.261c-1.084-.8-2.43-1.458-2.841-2.729-.673-2.056,1.52-4.187.985-6.281-.461-1.757-2.542-2.468-4.337-2.729s-3.851-.511-4.885-2.019c-.847-1.246-.648-2.9-.847-4.4a8.063,8.063,0,0,0-5.646-6.53c-3.116-.885-5.832,1.944-8.437,3.078a8.961,8.961,0,0,0-5.209,6.73,21.484,21.484,0,0,0,.461,8.9,64.316,64.316,0,0,1,1.707,8.811,25.854,25.854,0,0,1-1.408,9.4c-.611,2.131-.76,5.122.386,7.091,1.134,1.932,3.415,2.841,5.571,3.465a30.362,30.362,0,0,0,12.886,1.209,17.377,17.377,0,0,0,10.68-5.645,13.993,13.993,0,0,0,4.25-5.857c1.6-4.337.424-9.758-3.3-12.487Z" transform="translate(60.361 14.131)" fill="#485070"/>
    <path id="Path_773" data-name="Path 773" d="M253.96,65.986a7.083,7.083,0,0,1-.661,5.459c-.312.536-.8,1.084-1.421,1.034-.71-.05-1.109-.823-1.333-1.508a24.484,24.484,0,0,1-1.171-5.758c-.1-1.2.125-3.365,1.832-2.954,1.321.324,2.393,2.555,2.729,3.714Z" transform="translate(61.403 15.314)" fill="#ff6ea1"/>
    <path id="Path_774" data-name="Path 774" d="M259.685,90.1c-1.259,4.374-8.375,6.493-12.475,1.446-.5-.623-.972-1.271-1.421-1.932-4.412.872-10.992,2.792-13.933,6.381a11.32,11.32,0,0,0-1.844,4.075A161.813,161.813,0,0,0,226.186,116a3.562,3.562,0,0,0-.012,1.844,4.125,4.125,0,0,0,1.533,1.682c6.107,4.811,7.7,12.587,9.721,19.6,3.053,10.655,4.449,25.411,7.5,36.066,9.982.773,51.6-8.674,56.592-13.061-7.178-12.924-12.986-36.241-15.267-52.467-.96-6.879-3.664-13.422-6.294-19.853-.611-1.508-1.171-4.985-2.318-5.957-1.52-1.3-16.014.9-17.547.947.05.86.1,1.707.112,2.567a8.776,8.776,0,0,1-.523,2.729Z" transform="translate(55.665 20.545)" fill="#ff6ea1"/>
    <path id="Path_775" data-name="Path 775" d="M241.527,85.245a19.457,19.457,0,0,0-1.957-4.3,12.291,12.291,0,0,0,6.829-2.318,20.249,20.249,0,0,0,2.742-2.418c.3-.312.773-1.1,1.259-1.67-.661.773.96,5.085,1.284,5.97a15.83,15.83,0,0,0,2.954,5.147c1.309,1.458,2.168,1.695,2.405,3.751A15.063,15.063,0,0,1,256.1,96a6.33,6.33,0,0,1-2.268,3.278,5.848,5.848,0,0,1-4.586.411c-2.58-.673-6.119-2.829-7.041-5.521-.486-1.408.062-2.131.187-3.589a13.9,13.9,0,0,0-.847-5.321Z" transform="translate(58.993 18.35)" fill="#ffc1a6"/>
    <path id="Path_776" data-name="Path 776" d="M242.4,89.856c0-.15,0-.287-.012-.436a19.122,19.122,0,0,0-.86-4.175,19.457,19.457,0,0,0-1.957-4.3,12.291,12.291,0,0,0,6.829-2.318,20.249,20.249,0,0,0,2.742-2.418c.3-.312.773-1.1,1.259-1.67.075.8.187,1.6.324,2.38a16.754,16.754,0,0,1-8.325,12.95Z" transform="translate(58.993 18.35)" fill="#e3856d"/>
    <path id="Path_777" data-name="Path 777" d="M228.782,74.523a19.085,19.085,0,0,0,6.555,10.269,7.852,7.852,0,0,0,2.941,1.62,3.754,3.754,0,0,0,3.215-.548c1.259-1,1.471-2.792,1.558-4.4a36.718,36.718,0,0,0-.287-8.985,14.285,14.285,0,0,0-4.038-7.889c-1.807-1.67-6.755-4.088-8.936-1.795-2.368,2.5-1.757,8.724-1.022,11.715Z" transform="translate(56.172 15.227)" fill="#485070"/>
    <path id="Path_778" data-name="Path 778" d="M230.732,67.083c.424-2.829,3.614-.723,6.206-1.919,2.256-1.047,3.963-1.944,6.505-1.77a9.343,9.343,0,0,1,6.094,2.7c.424.449.823.91,1.184,1.371a33.877,33.877,0,0,1,2.63,5.2.442.442,0,0,0,.025.125,17.546,17.546,0,0,1-2.343,9.995,9.151,9.151,0,0,1-7.951,4.8,7.837,7.837,0,0,1-5.06-1.969,17.318,17.318,0,0,1-3.714-4.91c-1.52-2.754-4.05-10.518-3.577-13.621Z" transform="translate(56.802 15.6)" fill="#ffc1a6"/>
    <path id="Path_779" data-name="Path 779" d="M243.472,74.32c-3.078-.723-6.256-1.171-9.085-2.567a10.224,10.224,0,0,1-4.9-4.848c-.947-2.181-.3-3.714.96-5.6a13.51,13.51,0,0,1,7.764-5.5c3.128-.823,7.041.386,10.045,1.433a12.286,12.286,0,0,1,5.483,3.564c2.443,2.954,3.278,8.038,3.078,11.777a19.986,19.986,0,0,1-.947,4.536c-.349,1.246-.985,2.655-2.43,2.829-1,.125-2.979-2.7-3.988-3.34a18.342,18.342,0,0,0-5.209-2.094c-.262-.062-.511-.125-.773-.187Z" transform="translate(56.4 13.672)" fill="#485070"/>
    <path id="Path_780" data-name="Path 780" d="M236.821,73.528a.909.909,0,1,0,.2-.668.908.908,0,0,0-.2.668Z" transform="translate(58.315 17.854)" fill="#485070"/>
    <path id="Path_781" data-name="Path 781" d="M238.257,71.261a2.83,2.83,0,0,0-1.807.237" transform="translate(58.225 17.529)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_782" data-name="Path 782" d="M232.331,74.728a.909.909,0,1,0,.2-.668.908.908,0,0,0-.2.668Z" transform="translate(57.21 18.15)" fill="#485070"/>
    <path id="Path_783" data-name="Path 783" d="M233.767,72.471a2.83,2.83,0,0,0-1.807.237" transform="translate(57.119 17.827)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_784" data-name="Path 784" d="M235.072,74.62a3.139,3.139,0,0,1-.361,2.318.849.849,0,0,0-.2.573.532.532,0,0,0,.474.4,1.7,1.7,0,0,0,.661-.1" transform="translate(57.746 18.37)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_785" data-name="Path 785" d="M239.218,78.24a3.633,3.633,0,0,1-3.028,1.122" transform="translate(58.161 19.261)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_786" data-name="Path 786" d="M250.359,71.35a3.316,3.316,0,1,1-1.2-2.238A3.32,3.32,0,0,1,250.359,71.35Z" transform="translate(60.019 16.83)" fill="#ffc1a6"/>
    <path id="Path_787" data-name="Path 787" d="M247.089,69.84a1.44,1.44,0,0,0-1.234.648,1.475,1.475,0,0,0-.1,1.383.993.993,0,0,1,1.059-.262,1.02,1.02,0,0,1,.636.9" transform="translate(60.485 17.193)" fill="none" stroke="#485070" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.75"/>
    <path id="Rectangle_512" data-name="Rectangle 512" d="M8.74,0h267.2a8.74,8.74,0,0,1,8.74,8.74V160.55a8.74,8.74,0,0,1-8.74,8.74H8.74A8.74,8.74,0,0,1,0,160.55V8.74A8.74,8.74,0,0,1,8.74,0Z" transform="translate(113.521 303.805)" fill="#ebf3f3" stroke="#3ba39d" stroke-width="0.75"/>
    <path id="Path_788" data-name="Path 788" d="M401.008,379.79H74.18a13.584,13.584,0,0,0,13.584,13.584H387.412A13.584,13.584,0,0,0,401,379.79Z" transform="translate(18.267 93.517)" fill="#aad5d3" stroke="#566fe2" stroke-miterlimit="10" stroke-width="0.75"/>
    <rect id="Rectangle_513" data-name="Rectangle 513" width="271.021" height="157.326" rx="6.83" transform="translate(120.35 310.46)" fill="#aad5d3"/>
    <path id="Path_789" data-name="Path 789" d="M209.172,246.746a2.1,2.1,0,1,1-.615-1.491,2.106,2.106,0,0,1,.615,1.491Z" transform="translate(50.471 60.237)" fill="#333"/>
    <rect id="Rectangle_514" data-name="Rectangle 514" width="316.098" height="215.127" transform="translate(148.303 466.864) rotate(-90)" fill="#fefeff"/>
    <path id="Path_790" data-name="Path 790" d="M236,363.631l-28.626-35.668-24.9,31.9-21.585-23.33,2.829-2.6,18.507,20,25.1-32.166L235.2,356.5l16.189-33.038,34.16-6.044.66,3.789-32.228,5.7Z" transform="translate(39.619 78.159)" fill="#d5e9e8"/>
    <path id="Path_791" data-name="Path 791" d="M267.09,319.915a4.93,4.93,0,1,0-1.444,3.491A4.935,4.935,0,0,0,267.09,319.915Z" transform="translate(63.34 77.558)" fill="#c5e0de"/>
    <path id="Path_792" data-name="Path 792" d="M226.91,352.615a4.93,4.93,0,1,0-1.444,3.491,4.935,4.935,0,0,0,1.444-3.491Z" transform="translate(53.445 85.61)" fill="#92cdca"/>
    <path id="Path_793" data-name="Path 793" d="M239.99,324.625a4.93,4.93,0,1,0-1.444,3.491,4.935,4.935,0,0,0,1.444-3.491Z" transform="translate(56.666 78.718)" fill="#aad5d3"/>
    <circle id="Ellipse_149" data-name="Ellipse 149" cx="4.935" cy="4.935" r="4.935" transform="translate(241.847 398.408)" fill="#6dc0bb"/>
    <circle id="Ellipse_150" data-name="Ellipse 150" cx="4.935" cy="4.935" r="4.935" transform="translate(217.62 429.539)" fill="#3ba39d"/>
    <path id="Path_794" data-name="Path 794" d="M168.79,333.445a4.93,4.93,0,1,0-1.444,3.491,4.935,4.935,0,0,0,1.444-3.491Z" transform="translate(39.133 80.889)" fill="#566fe2"/>
    <path id="Path_795" data-name="Path 795" d="M145.46,307.46v67.285H295.608" transform="translate(35.819 75.706)" fill="none" stroke="#3ba39d" stroke-miterlimit="10" stroke-width="0.75"/>
    <rect id="Rectangle_515" data-name="Rectangle 515" width="22.918" height="53.227" transform="translate(193.231 275.752)" fill="#566fe2"/>
    <path id="Rectangle_516" data-name="Rectangle 516" d="M0,0H22.918V79.274H0Z" transform="translate(228.649 249.705)" fill="#3ba39d"/>
    <path id="Path_796" data-name="Path 796" d="M151.62,219.685a211.218,211.218,0,0,0,72.033-53.925" transform="translate(37.336 40.813)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_797" data-name="Path 797" d="M211.972,163.78l-1.633,4.648-.673-2.044-2.106-.424Z" transform="translate(51.111 40.325)" fill="#485070"/>
    <rect id="Rectangle_517" data-name="Rectangle 517" width="22.918" height="115.228" transform="translate(264.08 213.751)" fill="#6dc0bb"/>
    <rect id="Rectangle_518" data-name="Rectangle 518" width="22.918" height="64.83" transform="translate(299.51 264.149)" fill="#aad5d3"/>
    <path id="Path_798" data-name="Path 798" d="M144.23,173.24V286.5h153.1" transform="translate(35.516 42.655)" fill="none" stroke="#6dc0bb" stroke-miterlimit="10" stroke-width="0.75"/>
    <rect id="Rectangle_519" data-name="Rectangle 519" width="153.749" height="7.739" transform="translate(178.874 340.083)" fill="#d5e9e8"/>
    <rect id="Rectangle_520" data-name="Rectangle 520" width="92.397" height="7.739" transform="translate(209.357 186.82)" fill="#88bebb"/>
    <rect id="Rectangle_521" data-name="Rectangle 521" width="153.749" height="7.739" transform="translate(178.687 173.298)" fill="#88bebb"/>
    <rect id="Rectangle_522" data-name="Rectangle 522" width="153.749" height="7.739" transform="translate(178.874 354.278)" fill="#d5e9e8"/>
    <rect id="Rectangle_523" data-name="Rectangle 523" width="153.749" height="7.739" transform="translate(178.874 368.473)" fill="#d5e9e8"/>
    <path id="Path_799" data-name="Path 799" d="M263.616,84.26c-1.8.424-3.577.922-5.334,1.421a2.123,2.123,0,0,0-.835.374,2,2,0,0,0-.523,1.147c-1.159,5.633.424,13.086,4.05,17.609,1.1,1.371,2.393,2.555,3.577,3.851,1.171,1.271,2.729,2.418,3.776,3.764,1.221,1.558,1.732,4.075,2.306,5.932a58.306,58.306,0,0,1,2.393,13.148c.187,2.966.324,6.331,2.6,8.238,2.256,1.894,5.82,1.408,8.138-.386s3.639-4.6,4.624-7.365a59.045,59.045,0,0,0,3.6-22.57c-.336-6.967-2.468-15.354-6.966-20.862-3.016-3.689-7.976-5.334-12.737-5.409a35.221,35.221,0,0,0-8.674,1.109Z" transform="translate(63.174 20.47)" fill="#ff6ea1"/>
    <path id="Path_800" data-name="Path 800" d="M265.64,122.834q2.486-13.422,4.96-26.844" transform="translate(65.413 23.632)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_801" data-name="Path 801" d="M267.03,97.34c.573,1.458,1.147,2.929,1.72,4.387" transform="translate(65.755 23.965)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_802" data-name="Path 802" d="M243.435,136.882a6.358,6.358,0,0,1,1.982-.374c6.044-.137,11.951-.76,18.046-1.371,4.088-.411,8.175-.648,12.238-.7,3.278-.05,7.079.673,10.282-.237,3.377-.96,5.683-1.907,7.378-4.7a45.794,45.794,0,0,0,2.218-4.138,4.217,4.217,0,0,0,.573-2.542c-.262-1.234-1.62-1.982-2.954-2.468-2.829-1.047-4.586-2.667-7.839-3a33.167,33.167,0,0,0-10.12.8c-6.48,1.358-12.973,1.919-19.329,3.814a173.58,173.58,0,0,0-20.862,8.163c-2.492,1.109-5.06,2.293-6.854,4.15-2.767,2.841-3.066,7.079-.71,9.733a3.251,3.251,0,0,0,2.929,1.234c1.2-.212,1.994-1.782.985-2.243,6.543,2.517,6.717-4.212,12.039-6.119Z" transform="translate(55.627 28.864)" fill="#ffc1a6"/>
    <path id="Path_803" data-name="Path 803" d="M223.6,133.909l.05-.4c-.1.249-.2.486-.3.735.087-.112.174-.224.249-.337Z" transform="translate(54.999 32.871)" fill="#ff6ea1"/>
    <path id="Path_804" data-name="Path 804" d="M243.578,93.652a4.285,4.285,0,0,0-1.67-1.732c-2.119-1.234-6.555.661-8.45,1.707a13.429,13.429,0,0,0-1.2.735,30.449,30.449,0,0,0-6.057,5.359c-.611.673-1.209,1.346-1.795,2.031-8.786,10.157-14.469,21.336-13.048,34.932.3,2.916.885,6.044,3.053,8.013a7.716,7.716,0,0,0,6.518,1.67,11.84,11.84,0,0,0,5.77-3.066l-.062.561c.112-.262.212-.523.324-.8,3.465-3.365,4.275-8.013,5.122-12.55v-.025q4.131-10.151,8.263-20.289c1.321-3.228,5.571-12.114,3.215-16.538Z" transform="translate(51.994 22.531)" fill="#ff6ea1"/>
    <path id="Path_805" data-name="Path 805" d="M229.4,185.93c3.789-6.194-3.128-7.228-4.125-12.762a6.07,6.07,0,0,1-.025-2.019c.96-6.032,1.4-11.989,1.869-18.145.324-4.113.823-8.213,1.5-12.276.461-2.742,1.52-5.783,1.658-8.624a27.115,27.115,0,0,0-.324-2.7,9.445,9.445,0,0,0-5.7-4.649,10.886,10.886,0,0,0-9.97,1.982,27,27,0,0,0-1.844,3.315,34.155,34.155,0,0,0-.735,9.085c.224,6.63-.374,13.16.424,19.728a158.725,158.725,0,0,0,4.648,21.8c.7,2.617,1.446,5.321,3.028,7.353a7.553,7.553,0,0,0,9.92,1.932,3.487,3.487,0,0,0,1.807-2.754c0-1.209-1.471-2.206-2.144-1.271Z" transform="translate(52.124 30.6)" fill="#ffc1a6"/>
    <path id="Path_806" data-name="Path 806" d="M226.33,133.068a70.677,70.677,0,0,1,9.47-22.918" transform="translate(55.733 27.119)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_807" data-name="Path 807" d="M231.37,114.554l3.24-2.094" transform="translate(56.974 27.688)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_808" data-name="Path 808" d="M81.14,241.681c4.636,5.3,6.543,12.45,8.113,19.142Q92.3,273.8,95.36,286.782a31.414,31.414,0,0,1,1.022,6.169c.1,3.352-.86,7.6-1.645,10.83-1.009,4.138-3.489,3.888-7.166,3.514-5.832-.573-13.759-2.343-17.7-7.091-1.869-2.256-2.468-5.284-3.016-8.163-.735-3.963-1.483-7.926-2.218-11.9-3.028-16.2-6.057-32.913-2.63-49.04a30.712,30.712,0,0,1,19.13,10.568Z" transform="translate(14.896 56.903)" fill="#fff"/>
    <path id="Path_809" data-name="Path 809" d="M87.764,281.169c2.58,1.221,5.047,2.792,7.527,4.287,3.938,2.368,8.051,4.038,12.089,6.244a12.19,12.19,0,0,0,3.577,1.471c2.156.386,4.437-.436,6.618-.137,2.9.386,5.384,2.979,6.443,6.406,1.77,5.77-2.38,3.627-5.135,3.116-2.144-.4-4.3-.785-6.443-1.184a192.838,192.838,0,0,0-25.2-2.742c-4.7-.249-12.986-.137-16.5-4.412-1.707-2.081-3.6-11.166-1.346-13.41a3.508,3.508,0,0,1,1.446-.785,21.926,21.926,0,0,1,13.5-.212,24.508,24.508,0,0,1,3.427,1.358Z" transform="translate(16.834 68.66)" fill="#9c5240"/>
    <path id="Path_810" data-name="Path 810" d="M98.734,336.216H16.308a4.9,4.9,0,0,1-4.9-4.9h0a4.9,4.9,0,0,1,4.9-4.9H98.734a4.9,4.9,0,0,1,4.9,4.9h0A4.9,4.9,0,0,1,98.734,336.216Z" transform="translate(2.81 80.375)" fill="#073d3a"/>
    <rect id="Rectangle_524" data-name="Rectangle 524" width="44.715" height="80.258" rx="6.33" transform="translate(90.664 315.108) rotate(90)" fill="#073d3a"/>
    <rect id="Rectangle_525" data-name="Rectangle 525" width="4.985" height="59.496" transform="translate(20.688 352.558)" fill="#073d3a"/>
    <rect id="Rectangle_526" data-name="Rectangle 526" width="4.985" height="59.496" transform="translate(74.463 352.558)" fill="#073d3a"/>
    <rect id="Rectangle_527" data-name="Rectangle 527" width="4.985" height="72.27" transform="translate(55.246 414.123)" fill="#073d3a"/>
    <rect id="Rectangle_528" data-name="Rectangle 528" width="4.985" height="72.27" transform="translate(17.784 414.123)" fill="#073d3a"/>
    <rect id="Rectangle_529" data-name="Rectangle 529" width="4.985" height="72.27" transform="translate(97.693 414.123)" fill="#485070"/>
    <rect id="Rectangle_530" data-name="Rectangle 530" width="4.985" height="72.27" transform="translate(72.108 414.123)" fill="#073d3a"/>
    <path id="Path_811" data-name="Path 811" d="M126.889,352.65c-2.829.6-4.773.735-8.113,1.209-4.86.685-9.721,1.259-14.606,1.67,1.271,8.786,2.43,16.513,2.729,18.058,1.271,6.73.573,23.591,3.577,29.985,3.652.449,6.817-.15,10.232-.424C122.527,386.286,124.01,369.362,126.889,352.65Z" transform="translate(25.651 86.834)" fill="#cf9378"/>
    <path id="Path_812" data-name="Path 812" d="M116.72,381.928a20.7,20.7,0,0,0,.312,4.125c3.863,4.088,9.658,6.181,15.229,6.456a26.157,26.157,0,0,0,9.372-1.309q.542-9.87,1.171-19.741c.548-8.562.985-17.111,2.056-25.635a123.523,123.523,0,0,0,.125-23.018c-2.181-8.362-7.739-10.643-7.739-10.643s-15.528-6.144-27.417-8.761c-14.594-3.2-29.436-5.583-44.117-8.786-7.054-1.533-18.856-1.259-22.507,6.468-4.212,8.923,4.187,20.787,10.955,25.947,7,5.334,15.64,8.026,24.239,9.908a157.252,157.252,0,0,0,37.1,3.564c-.4,0,1.184,38.322,1.209,41.413Z" transform="translate(10.36 72.361)" fill="#566fe2"/>
    <path id="Path_813" data-name="Path 813" d="M115.5,340.585a.935.935,0,0,0,.075-.062c-2.717-2.692-5.272-5.546-7.988-8.238-4.038-4.013-7.964-8.138-11.964-12.176-7.964-8.013-15.977-16-23.766-24.19-2.044-.411-4.088-.847-6.132-1.284-7.054-1.533-18.856-1.259-22.507,6.468C39,310.027,47.4,321.892,54.168,327.051c7,5.334,15.64,8.026,24.239,9.908a157.252,157.252,0,0,0,37.1,3.564l-.025.075Z" transform="translate(10.362 72.366)" fill="#073d3a"/>
    <path id="Path_814" data-name="Path 814" d="M101.08,335.742c.012-3.9.548-8.661.561-12.562" transform="translate(24.891 79.577)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_815" data-name="Path 815" d="M116.094,307.227c-10.444-3.352-19.965-6.468-35.294-8.537" transform="translate(19.897 73.546)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_816" data-name="Path 816" d="M107.79,397.36h33.424v3.614H108.986a1.2,1.2,0,0,1-1.2-1.2V397.36Z" transform="translate(26.543 97.844)" fill="#b3b3b3"/>
    <path id="Path_817" data-name="Path 817" d="M140.861,400.689a3.767,3.767,0,0,0,.312-.536c.81-1.782-.137-3.926-1.7-5.2a15.09,15.09,0,0,0-5.546-2.4,90.779,90.779,0,0,0-12.562-2.891c-1.134-.162-13.31-1.271-13.26-1.82q-.28,4.6-.561,9.21c-.075,1.271-.4,2.517-.012,3.627h33.312Z" transform="translate(26.435 95.499)" fill="#485070"/>
    <path id="Path_818" data-name="Path 818" d="M112.8,386.052a5.783,5.783,0,0,1,6.73-.187A7.191,7.191,0,0,1,122.5,392.1a1.676,1.676,0,0,1-.436,1.221,1.865,1.865,0,0,1-1.271.324c-1.919.025-3.838.05-5.758.062-1.2.013-2.729.287-3.552-.785-1.471-1.894-.112-5.459,1.321-6.879Z" transform="translate(27.294 94.77)" fill="#485070"/>
    <path id="Path_819" data-name="Path 819" d="M123.1,390.022a2.656,2.656,0,0,0-2.679,1.67" transform="translate(29.653 96.034)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_820" data-name="Path 820" d="M123.12,392.68a3.417,3.417,0,0,1,2.841-1.62" transform="translate(30.318 96.292)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_821" data-name="Path 821" d="M126.52,392.969a1.94,1.94,0,0,1,1.77-1.209" transform="translate(31.155 96.465)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_822" data-name="Path 822" d="M80.5,349.328q-6.413.9-12.874,1.5c3.253,15.989,4.748,32.278,7.4,48.379,3.652-.012,6.979-.785,10.867-.7.735-4.873,2.231-33.811,4.511-50.785a82.907,82.907,0,0,1-9.908,1.62Z" transform="translate(16.654 85.62)" fill="#cf9378"/>
    <path id="Path_823" data-name="Path 823" d="M76.969,385.83c.885,3.6,1.919,5.807,5.459,7.216a20.622,20.622,0,0,0,9.733,1.371,16.3,16.3,0,0,0,8.038-3.726s4.574-68.282-1.122-77.03c-6.967-10.7-22.233-12.9-34.122-15.516-10.419-2.293-20.962-4.15-31.48-6.194-2.567,9.733-.71,21.186,3,30.172.025.05.037.087.062.137a20.679,20.679,0,0,0,17.335,12.475,156.58,156.58,0,0,0,16.75.523c-.075,0,3.165,37.686,6.331,50.573Z" transform="translate(7.95 71.887)" fill="#566fe2"/>
    <path id="Path_824" data-name="Path 824" d="M63.14,331.132c.012-3.9.548-8.661.561-12.562" transform="translate(15.548 78.442)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_825" data-name="Path 825" d="M72.87,396.94h33.424v3.614H74.066a1.2,1.2,0,0,1-1.2-1.2V396.94Z" transform="translate(17.944 97.74)" fill="#b3b3b3"/>
    <path id="Path_826" data-name="Path 826" d="M105.931,400.269a3.766,3.766,0,0,0,.312-.536c.81-1.782-.137-3.926-1.695-5.2a15.089,15.089,0,0,0-5.548-2.4,90.793,90.793,0,0,0-12.56-2.891c-1.134-.162-13.31-1.271-13.26-1.819q-.28,4.6-.561,9.21c-.075,1.271-.4,2.517-.012,3.627h33.312Z" transform="translate(17.833 95.396)" fill="#485070"/>
    <path id="Path_827" data-name="Path 827" d="M77.876,385.632a5.783,5.783,0,0,1,6.73-.187,7.192,7.192,0,0,1,2.966,6.231,1.676,1.676,0,0,1-.436,1.221,1.865,1.865,0,0,1-1.271.324c-1.919.025-3.838.05-5.758.062-1.2.012-2.729.287-3.552-.785-1.471-1.894-.112-5.459,1.321-6.879Z" transform="translate(18.695 94.666)" fill="#485070"/>
    <path id="Path_828" data-name="Path 828" d="M88.169,389.6a2.656,2.656,0,0,0-2.679,1.67" transform="translate(21.052 95.93)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_829" data-name="Path 829" d="M88.2,392.26a3.417,3.417,0,0,1,2.841-1.62" transform="translate(21.719 96.189)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_830" data-name="Path 830" d="M91.59,392.549a1.94,1.94,0,0,1,1.77-1.209" transform="translate(22.554 96.361)" fill="none" stroke="#b3b3b3" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_831" data-name="Path 831" d="M33.184,320.4c.3,1.57.586,3.141.885,4.711,5.421-2.854,11.914-2.256,18.033-2.268A83.151,83.151,0,0,0,90.735,313.2a52.971,52.971,0,0,1-1.446-9.9c-.012-3.415-1.433-6.717-1.433-10.144-.224-4.624-.05-9.26.187-13.871.436-8.749,1.022-17.6-.611-26.209A38.429,38.429,0,0,0,80.89,237.4c-1.558-2.106-3.714-4.848-6.107-6.057-2.53-1.284-6.331-.86-9.185-1.109a23.9,23.9,0,0,0-6.256-.012,21.047,21.047,0,0,0-7.091,2.991c-11.8,7.079-13.235,18.494-16.787,30.782-1.77,6.144-4.561,11.951-6.331,18.083a34.8,34.8,0,0,0-1.321,10.568c.075,3.228,1.321,6.094,1.907,9.272q1.739,9.253,3.465,18.507Z" transform="translate(6.846 56.635)" fill="#fff"/>
    <path id="Path_832" data-name="Path 832" d="M58.91,239.345a11.415,11.415,0,0,0,7.951,2.667,5.11,5.11,0,0,0,2.8-.872,2.392,2.392,0,0,0,.9-2.63" transform="translate(14.506 58.727)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_833" data-name="Path 833" d="M56.73,259.83A28.6,28.6,0,0,0,74.2,278.212" transform="translate(13.97 63.977)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_834" data-name="Path 834" d="M62.87,265.06a13.288,13.288,0,0,0,8.612,4.636" transform="translate(15.482 65.265)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_835" data-name="Path 835" d="M50.78,264.65a61.265,61.265,0,0,0,15.678,25.374" transform="translate(12.504 65.164)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_836" data-name="Path 836" d="M72.57,238.8c3.39,5.832,3.44,9.147,4.2,14.294a109.82,109.82,0,0,1,1.171,17.4c-.125,9.758-2.281,18.694-.1,28.377" transform="translate(17.87 58.799)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_837" data-name="Path 837" d="M70.288,230.983a20.425,20.425,0,0,1,2.106-4.624,13.249,13.249,0,0,1-7.328-2.492,22.446,22.446,0,0,1-2.954-2.592c-.324-.336-.823-1.184-1.346-1.795-.536,5.87-2.729,11.391-7.727,14.743a19.382,19.382,0,0,0,7.565,7.166c2.6,1.508,9.11,4.437,9.26-.847.05-1.732-.636-3.327-.486-5.085a20.247,20.247,0,0,1,.922-4.474Z" transform="translate(13.061 54.041)" fill="#cf9378"/>
    <path id="Path_838" data-name="Path 838" d="M67.9,235.93c0-.15,0-.312.012-.474a21.568,21.568,0,0,1,3.028-9.1,13.249,13.249,0,0,1-7.328-2.492,22.442,22.442,0,0,1-2.954-2.592c-.324-.336-.823-1.184-1.346-1.795-.075.86-.2,1.707-.349,2.555a17.987,17.987,0,0,0,8.936,13.9Z" transform="translate(14.519 54.041)" fill="#9c5240"/>
    <path id="Path_839" data-name="Path 839" d="M38.412,241.681c-4.636,5.3-6.543,12.45-8.113,19.142q-3.047,12.973-6.107,25.959c-.71,3-1.844,7.1-1.171,10.157a13.2,13.2,0,0,0,6.019,8.15c3.078,1.844,7.764,2.692,11.154,1.122,3.315-1.545,7.1-3.1,9.509-6.007,1.869-2.256,2.468-5.284,3.016-8.163.735-3.963,1.483-7.926,2.218-11.9,3.028-16.2,6.057-32.913,2.63-49.04a30.712,30.712,0,0,0-19.13,10.568Z" transform="translate(5.618 56.903)" fill="#fff"/>
    <path id="Path_840" data-name="Path 840" d="M53.036,252.5c-6.742,1.757-6.132,10.381-6.742,15.7a157.585,157.585,0,0,1-4.474,23.28" transform="translate(10.298 62.172)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_841" data-name="Path 841" d="M46.76,257.247a12.862,12.862,0,0,1,4.088-6.007" transform="translate(11.514 61.862)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.4"/>
    <path id="Path_842" data-name="Path 842" d="M48.445,282.17c3.614.785,7.166,1.944,10.705,3.016,5.633,1.707,11.341,2.692,17.061,4.212a19.828,19.828,0,0,0,4.96.872c2.891.025,5.683-1.147,8.574-1.209a11.636,11.636,0,0,1,9.746,5.284c3.539,5.409-2.33,3.963-6.019,3.9-2.879-.037-5.758-.087-8.636-.125-11.316-.162-22.021.411-33.374,1.358-6.169.523-16.924,1.969-22.408-1.7-2.667-1.782-7.066-10.431-4.611-13.023a4.738,4.738,0,0,1,1.707-1.009,37.006,37.006,0,0,1,17.522-2.393,46.8,46.8,0,0,1,4.761.785Z" transform="translate(5.842 69.229)" fill="#cf9378"/>
    <rect id="Rectangle_531" data-name="Rectangle 531" width="77.803" height="3.539" rx="1.21" transform="translate(72.856 379.627)" fill="#073d3a"/>
    <path id="Path_843" data-name="Path 843" d="M140.337,315.455H73.85l5.783-38.2a2.7,2.7,0,0,1,2.63-2.318h61.216a2.593,2.593,0,0,1,2.53,3.028l-5.683,37.487Z" transform="translate(18.185 67.698)" fill="#757e96"/>
    <circle id="Ellipse_151" data-name="Ellipse 151" cx="4.412" cy="4.412" r="4.412" transform="translate(126.357 357.705)" fill="#fff"/>
    <path id="Path_844" data-name="Path 844" d="M82.1,209.731c-.76-2.966-3.938-.386-6.842-1.371-2.517-.872-4.437-1.645-7.129-1.171a9.93,9.93,0,0,0-6.206,3.552c-.411.523-.773,1.059-1.109,1.583a34.74,34.74,0,0,0-2.231,5.832.511.511,0,0,1-.012.137A18.755,18.755,0,0,0,62.159,228.7a9.826,9.826,0,0,0,9.01,4.262,8.445,8.445,0,0,0,5.184-2.655,18.68,18.68,0,0,0,3.427-5.646c1.321-3.1,3.165-11.665,2.33-14.93Z" transform="translate(14.423 50.979)" fill="#cf9378"/>
    <path id="Path_845" data-name="Path 845" d="M68.808,214.775c.025-.125.05-.274.087-.449.262-1.234.523-2.48.785-3.714,3.128,2.343,6.518,5.67,10.568,7.016a7.7,7.7,0,0,0,8.462-2.667,9.718,9.718,0,0,0,.872-1.471,11.706,11.706,0,0,1,.536-1.309,2.808,2.808,0,0,1-3.514-.773,11.732,11.732,0,0,1-1.77-3.5,16.2,16.2,0,0,0-5.645-7.191,12.9,12.9,0,0,0-8.774-2.281,11,11,0,0,0-5.346,2.144,9.631,9.631,0,0,0-2.044,2.044c-.262.361-1.358,2.343-.91,2.729a2.273,2.273,0,0,0-2.542-.075,5.106,5.106,0,0,0-1.757,2.019,17.329,17.329,0,0,0-2.006,11.141,21.688,21.688,0,0,0,1.5,4.748c.511,1.284,1.346,2.729,2.9,2.754,2.156.05,1.919-3.577,2.53-5.446.237-.735.187-1.658,1.059-1.869s2.318,1.458,3,.087a2.523,2.523,0,0,0,.012-1.633,2.193,2.193,0,0,1,.1-1.62c.025-.05,1.147,0,1.309-.05a.757.757,0,0,0,.573-.648Z" transform="translate(13.693 48.839)" fill="#485070"/>
    <path id="Path_846" data-name="Path 846" d="M62.226,199.48a8.09,8.09,0,0,0-.872,8" transform="translate(14.941 49.116)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_847" data-name="Path 847" d="M57.14,203.465a3.519,3.519,0,0,1,4.4.7" transform="translate(14.071 49.979)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <circle id="Ellipse_152" data-name="Ellipse 152" cx="0.972" cy="0.972" r="0.972" transform="translate(87.412 269.845)" fill="#485070"/>
    <path id="Path_848" data-name="Path 848" d="M70.31,215.237a3.092,3.092,0,0,1,1.957.05" transform="translate(17.314 52.963)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_849" data-name="Path 849" d="M77,218.3a.972.972,0,1,1-.972-.972A.972.972,0,0,1,77,218.3Z" transform="translate(18.483 53.512)" fill="#485070"/>
    <path id="Path_850" data-name="Path 850" d="M75.24,216.037a3.092,3.092,0,0,1,1.957.05" transform="translate(18.528 53.16)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_851" data-name="Path 851" d="M73.869,218.48a3.387,3.387,0,0,0,.636,2.43.967.967,0,0,1,.287.586c0,.237-.237.424-.461.486a1.713,1.713,0,0,1-.71-.037" transform="translate(18.129 53.795)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_852" data-name="Path 852" d="M70.31,222.75a3.854,3.854,0,0,0,3.352.86" transform="translate(17.314 54.846)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_853" data-name="Path 853" d="M58.36,216.6a3.564,3.564,0,1,0,3.564-3.564A3.564,3.564,0,0,0,58.36,216.6Z" transform="translate(14.371 52.455)" fill="#cf9378"/>
    <path id="Path_854" data-name="Path 854" d="M60.685,214.672a1.577,1.577,0,0,1,1.383.548,1.56,1.56,0,0,1,.249,1.471,1.062,1.062,0,0,0-1.171-.162,1.1,1.1,0,0,0-.586,1.022" transform="translate(14.912 52.855)" fill="none" stroke="#485070" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.75"/>
    <path id="Path_855" data-name="Path 855" d="M384.947,379.4c-.1-.449-12.824,1.782-14.232,1.994-.05.05-.1.087-.15.137-4.4,3.739-9.982,5.77-15.441,7.752a5.263,5.263,0,0,0-2.306,1.321c-2.48,2.9,2.779,4.162,4.648,4.312a35.026,35.026,0,0,0,9.048-.885c6.132-1.084,12.388-.723,18.457-2.231a1.113,1.113,0,0,0,.636-.324,1.08,1.08,0,0,0,.15-.561,37.356,37.356,0,0,0-.8-11.515Z" transform="translate(86.72 93.407)" fill="#485070"/>
    <path id="Path_856" data-name="Path 856" d="M357.62,327.491c3.078,30.633,8.674,62.063,12.188,92.671,7.178.623,9.783.15,16.75-1.034,4.574-33.287,6.505-49.351,2.38-83.125-.424-3.465-.062-32.178-.187-35.867-.735-21.946,2.206-47.008-10.979-65.54-3.328-4.686-8.786-8.275-14.332-7.839a12.915,12.915,0,0,0-9.509,5.7c-6.8,9.247-3.016,34.359-1.8,45.326,1.608,14.531,4.025,35.119,5.483,49.713Z" transform="translate(86.181 55.824)" fill="#757e96"/>
    <path id="Path_857" data-name="Path 857" d="M359.783,346.546c1.246-5.309,2.767-10.568,4.262-15.815,5.247-18.357,7.4-37.213,10.032-56.081a87.254,87.254,0,0,0,1.171-13.472c-.087-4.586-.685-9.185-1.047-13.759a118.75,118.75,0,0,0-2.58-18.557,13.892,13.892,0,0,0-8.163-2.106,12.915,12.915,0,0,0-9.509,5.7c-6.8,9.247-3.016,34.359-1.8,45.326,1.608,14.531,4.025,35.119,5.483,49.713.623,6.256,1.358,12.624,2.156,19.055Z" transform="translate(86.186 55.824)" fill="#545d78"/>
    <path id="Path_858" data-name="Path 858" d="M346.282,382.825a1.825,1.825,0,0,0-.374.025,49.445,49.445,0,0,1-12.288-.062,4.369,4.369,0,0,0-1.558-.025,4.431,4.431,0,0,0-1.271.611,36.858,36.858,0,0,1-12.425,4.748c-2.53.461-5.247.723-7.241,2.33-.935.748-1.67,2.144-.96,3.1a2.566,2.566,0,0,0,.985.71c3.826,1.795,9.609,1.1,13.721.872,6.642-.374,13.223.037,19.778-1.3a1.629,1.629,0,0,0,.76-.3,1.789,1.789,0,0,0,.486-1.022,50.968,50.968,0,0,0,1-5.508c.087-.735.5-3.365,0-3.976a.761.761,0,0,0-.561-.224Z" transform="translate(76.307 94.233)" fill="#485070"/>
    <path id="Path_859" data-name="Path 859" d="M358.553,230.131c-15.1-.237-19.728,15.528-21.523,27.654q-5.7,38.416-7.988,77.23c-.76,12.861-1.77,23.716-2.193,36.59-.449,13.21-1.159,36.079-.586,49.227a1.247,1.247,0,0,0,.2.76,1.266,1.266,0,0,0,.823.312,91.011,91.011,0,0,0,16.687.237,1.522,1.522,0,0,0,.748-.2c.237-.174-1.894-1.009-1.807-1.3,4.25-13.123,7.153-36.577,8.487-50.3,2.792-28.676,8.749-56.58,19.441-83.062,10.693-25.012,16.737-56.679-12.276-57.153Z" transform="translate(80.288 56.663)" fill="#757e96"/>
    <path id="Path_860" data-name="Path 860" d="M357.1,297.231a398.562,398.562,0,0,0,15.578-44.391" transform="translate(87.935 62.256)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_861" data-name="Path 861" d="M359.3,265.3s.1.012.15.025q2.075.336,4.15.6c1.022.125,2.044.237,3.066.324.336.037.673.062,1.009.087.86.075,1.707.137,2.567.174.486.025.972.05,1.446.062.374.012.76.037,1.134.05h0a95.3,95.3,0,0,0,19.479-1.371c-.96-11.578-3.489-22.732-10.219-32.178-3.328-4.686-8.786-8.275-14.332-7.839a12.915,12.915,0,0,0-9.509,5.7,12.765,12.765,0,0,0-1.333,2.33c-13.123-.3-18.918,12.924-21.834,24.077a49.338,49.338,0,0,0,9.4,4.275,89.119,89.119,0,0,0,14.843,3.689Z" transform="translate(82.51 55.45)" fill="#545d78"/>
    <path id="Path_862" data-name="Path 862" d="M360.42,165.533a20.162,20.162,0,0,0-.361-5.06,13.211,13.211,0,0,0,7.739.224,21.38,21.38,0,0,0,3.664-1.408c.424-.2,1.184-.823,1.882-1.209-1.545,5.683-1.421,11.627,2.106,16.513a19.387,19.387,0,0,1-9.584,4.075c-2.979.511-10.082.972-8.375-4.025.561-1.633,1.757-2.891,2.231-4.6a20.6,20.6,0,0,0,.7-4.51Z" transform="translate(87.967 38.922)" fill="#f7b699"/>
    <path id="Path_863" data-name="Path 863" d="M366.849,160.337A13.184,13.184,0,0,1,359.6,160a21.483,21.483,0,0,1-.336,9.571c-.037.15-.1.3-.15.449.3-.037.586-.087.885-.15a17.635,17.635,0,0,0,6.854-9.546Z" transform="translate(88.43 39.395)" fill="#c46e58"/>
    <path id="Path_864" data-name="Path 864" d="M357.3,142.268c1.745-2.517,3.826,1.022,6.892,1.1,2.667.062,4.736,0,7.091,1.4a10.026,10.026,0,0,1,4.586,5.5,18.822,18.822,0,0,1,.486,1.869,34.969,34.969,0,0,1,.062,6.256.476.476,0,0,1-.037.125,18.768,18.768,0,0,1-6.991,8.5,9.84,9.84,0,0,1-9.92.847,8.429,8.429,0,0,1-3.938-4.287,18.5,18.5,0,0,1-1.246-6.493c-.162-3.365,1.1-12.039,3.028-14.805Z" transform="translate(87.237 34.806)" fill="#f7b699"/>
    <path id="Path_865" data-name="Path 865" d="M361.524,149.815c-2.393.349-4.337.723-6.53-.561a7.785,7.785,0,0,1-3.5-4.424,9.743,9.743,0,0,1-.312-1.682,9.747,9.747,0,0,0-.05-1.408,2.831,2.831,0,0,0,3.564.511,11.67,11.67,0,0,0,2.879-2.667,16.2,16.2,0,0,1,7.8-4.761,12.911,12.911,0,0,1,9.023.922,11.186,11.186,0,0,1,4.262,3.876,9.825,9.825,0,0,1,1.2,2.63c.112.436.449,2.679-.1,2.879a2.259,2.259,0,0,1,2.4.823,5.255,5.255,0,0,1,.947,2.5A16.908,16.908,0,0,1,382,157.4a67.942,67.942,0,0,1-3.028,6.206,10.086,10.086,0,0,1-4.113,4.063c-2.218,1.072-4.8,1-7.266.86-2.268-.125-4-.935-4.935-3.116a10.328,10.328,0,0,1-.636-3.253c-.025-.461-.249-3.664.137-3.851a3.929,3.929,0,0,1-1.471.523,1.332,1.332,0,0,1-1.321-.648,2.041,2.041,0,0,1,.062-1.433c.685-2.318,1.383-4.636,2.069-6.954Z" transform="translate(86.465 33.092)" fill="#485070"/>
    <path id="Path_866" data-name="Path 866" d="M375.029,138.41a8.1,8.1,0,0,1-1.969,7.8" transform="translate(91.865 34.078)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_867" data-name="Path 867" d="M378.624,143.976a3.524,3.524,0,0,0-4.374-.885" transform="translate(92.158 35.13)" fill="none" stroke="#485070" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_868" data-name="Path 868" d="M365.372,155.978a3.557,3.557,0,1,1-2.094-4.574A3.557,3.557,0,0,1,365.372,155.978Z" transform="translate(88.274 37.223)" fill="#f7b699"/>
    <path id="Path_869" data-name="Path 869" d="M362.723,153.113a1.59,1.59,0,0,0-1.5.037,1.559,1.559,0,0,0-.748,1.284,1.042,1.042,0,0,1,1.333,1.408" transform="translate(88.767 37.657)" fill="none" stroke="#485070" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.75"/>
    <path id="Path_870" data-name="Path 870" d="M324.832,189.527a3.277,3.277,0,0,1,2.106-2.443c1.1-.386,2.954-.386,3.327-1.5.187-.548.361-.76-.075-1.134a2.686,2.686,0,0,0-1.62-.561,4.825,4.825,0,0,0-4.437,2.468c-.262.486-.636,1.122-1.171.96a.893.893,0,0,1-.511-.548c-.623-1.458.237-3.078,1.072-4.424a7.608,7.608,0,0,1,2.43-2.779,7.21,7.21,0,0,1,4.088-.548c2.817.2,5.758.411,8.138,2.081.623.436,1.2.947,1.819,1.4a19.562,19.562,0,0,0,5.334,2.38c-.573,2.006-1.159,4.013-1.732,6.032a.421.421,0,0,1-.162.274.4.4,0,0,1-.336,0c-3.415-.835-7.353-1.533-10.78-2.069a25.988,25.988,0,0,0-7.477.411Z" transform="translate(79.35 44.065)" fill="#f7b699"/>
    <path id="Path_871" data-name="Path 871" d="M340.311,180.11a21.587,21.587,0,0,0-4.648,14.008q3.29,1.309,6.58,2.63c.76-3.477,1.508-6.954,2.268-10.419.224-1.047,1.159-3.265.561-4.125-.636-.922-3.7-1.62-4.761-2.081Z" transform="translate(82.653 44.347)" fill="#fff"/>
    <path id="Path_872" data-name="Path 872" d="M362.4,188.328a47.779,47.779,0,0,0-5.658-2.979c-3.377-1.52-6.942-2.767-10.344-3.938a31.816,31.816,0,0,0-3.976-1.221c-3.7,3.951-5,10.643-4.549,16.264,3.9,3.415,9.609,5.446,13.908,8.126a45.637,45.637,0,0,0,7.44,4.113,13.166,13.166,0,0,0,8.337.723,5.3,5.3,0,0,0,3.452-2.592,6.074,6.074,0,0,0,.411-2.717,19.51,19.51,0,0,0-9.035-15.79Z" transform="translate(83.18 44.366)" fill="#545d78"/>
    <path id="Path_873" data-name="Path 873" d="M306.248,206.511a10.3,10.3,0,0,1-.723-8.636,3.768,3.768,0,0,1,2.119-2.393A3.332,3.332,0,0,1,311.1,196.9a16.011,16.011,0,0,1,1.682,3.589,16.456,16.456,0,0,0,2.144,3.714c.91,1.184,2.006,2.33,2.268,3.789a3.713,3.713,0,0,1-1.034,3.253c-3.128,3.078-8.387-2.044-9.908-4.736Z" transform="translate(75.078 48.105)" fill="#f7b699"/>
    <path id="Path_874" data-name="Path 874" d="M342.95,207.277l-18.906,9.172L299.02,193.742l18.918-9.172Z" transform="translate(73.633 45.445)" fill="#f4ae2d"/>
    <path id="Path_875" data-name="Path 875" d="M339.271,205.906l-16.239,7.876L301.41,194.166l16.251-7.876Z" transform="translate(74.221 45.868)" fill="#fff"/>
    <path id="Path_876" data-name="Path 876" d="M319.358,189.856l-13.5,6.543-1.321-1.2,13.5-6.543Z" transform="translate(74.992 46.452)" fill="#c8ddf7"/>
    <path id="Path_877" data-name="Path 877" d="M324.888,194.876l-13.5,6.543-1.321-1.2,13.5-6.543Z" transform="translate(76.354 47.688)" fill="#c8ddf7"/>
    <path id="Path_878" data-name="Path 878" d="M326.715,196.546l-13.5,6.543-1.309-1.2,13.5-6.543Z" transform="translate(76.807 48.099)" fill="#c8ddf7"/>
    <path id="Path_879" data-name="Path 879" d="M328.558,198.206l-13.5,6.543-1.321-1.2,13.5-6.543Z" transform="translate(77.257 48.508)" fill="#c8ddf7"/>
    <path id="Path_880" data-name="Path 880" d="M330.388,199.864l-13.5,6.543-1.321-1.2,13.5-6.53Z" transform="translate(77.708 48.919)" fill="#c8ddf7"/>
    <path id="Path_881" data-name="Path 881" d="M332.218,201.536l-13.5,6.543-1.321-1.2,13.509-6.543Z" transform="translate(78.159 49.328)" fill="#c8ddf7"/>
    <path id="Path_882" data-name="Path 882" d="M317,188.634l-11.341,5.508-4.835-4.387,11.353-5.5Z" transform="translate(74.076 45.368)" fill="#485070"/>
    <path id="Path_883" data-name="Path 883" d="M307.73,204.292a5.565,5.565,0,0,0-.511.7c-1.433,2.293,1.333,4.437,3.1,5.67,4,2.816,8.886,4.162,13.522,5.508,2.617.76,10.194-1.109,8.1-5.222-1.433-2.8-5.658-4.424-8.437-5.172-2.891-.536-5.359-1.745-6.443-4.673-.486-1.3-.673-2.692-1.1-4.013a1.823,1.823,0,0,0-.486-.885.758.758,0,0,0-.935-.1,1.226,1.226,0,0,0-.3.486c-.81,2.256.648,3.926-2.144,5.259-.922.436-1.932.685-2.829,1.171a5.807,5.807,0,0,0-1.545,1.259Z" transform="translate(75.554 48.255)" fill="#f7b699"/>
    <path id="Path_884" data-name="Path 884" d="M319.386,200.68a21.6,21.6,0,0,0-3.116,14.432l6.829,1.907c.374-3.539.748-7.066,1.134-10.606.112-1.059.8-3.377.112-4.162-.735-.847-3.851-1.209-4.948-1.558Z" transform="translate(77.83 49.412)" fill="#fff"/>
    <path id="Path_885" data-name="Path 885" d="M356.678,168.211c1.333-1.2,3.265-1.421,5.047-1.6q6.935-.654,13.858-1.3l2.617,9.272c-.037-.125-14.98.461-16.413.424-1.5-.05-6.368.436-6.991-.96-.7-1.558.723-4.811,1.869-5.845Z" transform="translate(87.322 40.705)" fill="#fff"/>
    <path id="Path_886" data-name="Path 886" d="M392.236,244.294c.012-4.923.287-9.833.6-14.743.411-6.393.885-12.786.972-19.192a130.062,130.062,0,0,0-1.583-23.816q-.5-2.936-1.234-5.783c-.1-.386-.212-.81-.324-1.234-1.471-3.527-4.262-8.213-7.988-10.294a15.6,15.6,0,0,0-1.869.037q-6.412.43-12.774,1.408c-2.916.449-4.985.823-7.129,3.053-5.471,5.7-10.606,18.469-12.039,26.308-1.371,7.49-.835,7.926-1.608,15.478q-1.383,13.5-3.926,26.844c-1.421,7.49-3.564,15.628-9.783,20.04,10.319,4.337,20.762,8.7,31.817,10.431s22.956.573,32.365-5.5c-4.437-6.842-5.508-14.868-5.483-23.031Z" transform="translate(82.136 41.664)" fill="#757e96"/>
    <path id="Path_887" data-name="Path 887" d="M370.515,189.343a5.476,5.476,0,0,0-1.72-2.144,4.688,4.688,0,0,0-2.841-.648c-4.324.187-7.752,3.726-10.057,7.39s-4.088,7.8-7.39,10.606c-5.745,4.91-19.591.9-26.022-.536-4.9,4.786-5.421,10.331-2.879,16.787,8.624,3.789,26.769,12.774,36.179,8.288a19.658,19.658,0,0,0,7.739-7.353,62.578,62.578,0,0,0,3.141-5.546,53.873,53.873,0,0,0,5.184-13.073c.985-4.6.823-9.584-1.333-13.771Z" transform="translate(78.333 45.931)" fill="#757e96"/>
    <path id="Path_888" data-name="Path 888" d="M368.431,176.806a9.111,9.111,0,0,0-11.266-1.209c-2.031,1.271-3.053,3.9-4.1,5.982a56.831,56.831,0,0,0-3.265,7.914c-.922,2.8-1.633,5.683-2.492,8.5-1.421,4.624-3.265,9.135-4.324,13.858s-1.3,9.8.486,14.294c2.542,6.431,11,3.016,14.768-.561,3.253-3.078,4.8-7.49,6.256-11.727q2.187-6.393,4.362-12.774a50.853,50.853,0,0,0,2.891-12.949c.237-4.25-.511-8.375-3.29-11.328Z" transform="translate(84.255 42.89)" fill="#757e96"/>
    <path id="Path_889" data-name="Path 889" d="M344.84,203.032A90.224,90.224,0,0,1,355.284,176.2" transform="translate(84.916 43.384)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_890" data-name="Path 890" d="M371.359,191.25l-5.571,14.992c-1.333,3.589-2.6,8.175-5.135,11.141s-4.873,6.169-7.951,8.6c-3,2.355-6.792,3.963-10.593,3.589" transform="translate(84.243 47.09)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_891" data-name="Path 891" d="M368,192.32a10.583,10.583,0,0,0-3.988,4.063" transform="translate(89.636 47.353)" fill="none" stroke="#485070" stroke-miterlimit="10" stroke-width="0.75"/>
    <path id="Path_892" data-name="Path 892" d="M320.931,193.566l-2.231,1.209,1.134-2.268,13.447-13.921a.779.779,0,0,1,1.084-.025h0a.779.779,0,0,1,.025,1.084l-13.447,13.921Z" transform="translate(78.479 43.914)" fill="#485070"/>
  </g>
</svg>
