<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="887.735" height="380.18" viewBox="0 0 887.735 380.18">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_15" data-name="Rectangle 15" width="99.14" height="52.46" transform="translate(391.57 197.26)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_22" data-name="Rectangle 22" width="119.67" height="155.78" transform="translate(370.13 278.74)" fill="none"/>
    </clipPath>
  </defs>
  <g id="maintenance-bg" transform="translate(191.615 -54.48)">
    <g id="OBJECTS">
      <g id="Group_45" data-name="Group 45">
        <path id="Path_1" data-name="Path 1" d="M637.46,112.89c-31.19,4.49-159.27,15.31-84.05,40.23L441.14,250.34c4.23,40.87-402.69,58.05-506.08,80.61-138.57,30.24-61.69,40.62-61.69,66.05,0,17.59-66.74,14.3-64.95,37.65h887.7V54.48H559.08s-1.43,20.24,67.15,28.54c69.89,8.46,48.08,24.56,11.23,29.86Z" fill="#04a17f"/>
        <path id="Path_2" data-name="Path 2" d="M416.5,434.65s-35.3-12.97-23.08-41.7,128.57-14.35,146.04-25.13-48.19-52.05,21.11-54.41,130.75-7.14,110.26-38.35,25.29-57.08,25.29-57.08V434.66H416.5Z" fill="#04877f"/>
        <g id="Group_11" data-name="Group 11">
          <g id="Group_1" data-name="Group 1">
            <rect id="Rectangle_3" data-name="Rectangle 3" width="36.66" height="5.28" transform="translate(407.12 269.71)" fill="#bbc5ef"/>
            <rect id="Rectangle_4" data-name="Rectangle 4" width="36.66" height="5.28" transform="translate(570.74 269.71)" fill="#bbc5ef"/>
          </g>
          <path id="Path_3" data-name="Path 3" d="M635.39,278.06a10.907,10.907,0,0,0-7.93-3.41H387.06a10.924,10.924,0,0,0-7.93,3.41L341.6,317.65H672.91l-37.53-39.59Z" fill="#d7dcf9"/>
          <path id="Path_4" data-name="Path 4" d="M636.96,299.56l-18.62-19.64H396.18l-18.62,19.64Z" fill="#bbc5ef"/>
          <path id="Path_5" data-name="Path 5" d="M463.59,317.65h87.34l-7.7-13.23H471.29Z" fill="#bbc5ef"/>
          <path id="Path_6" data-name="Path 6" d="M527.44,317.65l-1.26-4.13H488.34l-1.26,4.13Z" fill="#d7dcf9"/>
          <g id="Group_2" data-name="Group 2">
            <rect id="Rectangle_5" data-name="Rectangle 5" width="260.4" height="162.13" rx="3.69" transform="translate(377.06 107.59)" fill="#d7dcf9"/>
            <rect id="Rectangle_6" data-name="Rectangle 6" width="248.9" height="142.01" rx="5.3" transform="translate(382.81 113.33)" fill="#04877f"/>
            <rect id="Rectangle_7" data-name="Rectangle 7" width="99.14" height="35.22" transform="translate(391.57 169.48)" fill="#04a17f"/>
            <path id="Path_117" data-name="Path 117" d="M0,0H121.59V44.24H0Z" transform="translate(501.36 211.1)" fill="#04a17f"/>
          </g>
          <path id="Path_7" data-name="Path 7" d="M665.73,324.84H348.79a7.191,7.191,0,0,1-7.19-7.19H672.91a7.191,7.191,0,0,1-7.19,7.19Z" fill="#bbc5ef"/>
          <g id="Group_10" data-name="Group 10">
            <g id="Group_9" data-name="Group 9">
              <g id="Group_8" data-name="Group 8">
                <g id="Group_3" data-name="Group 3">
                  <line id="Line_1" data-name="Line 1" x1="15.9" y2="21.07" transform="translate(396.5 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                  <line id="Line_2" data-name="Line 2" x1="15.9" y1="21.07" transform="translate(602.13 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                </g>
                <g id="Group_4" data-name="Group 4">
                  <line id="Line_3" data-name="Line 3" x1="13.3" y2="21.07" transform="translate(414.64 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                  <line id="Line_4" data-name="Line 4" x1="13.3" y1="21.07" transform="translate(586.58 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                </g>
                <g id="Group_5" data-name="Group 5">
                  <line id="Line_5" data-name="Line 5" x1="10.23" y2="21.07" transform="translate(436.02 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                  <line id="Line_6" data-name="Line 6" x1="10.23" y1="21.07" transform="translate(568.27 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                </g>
                <g id="Group_6" data-name="Group 6">
                  <line id="Line_7" data-name="Line 7" x1="7.14" y2="21.07" transform="translate(457.54 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                  <line id="Line_8" data-name="Line 8" x1="7.14" y1="21.07" transform="translate(549.84 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                </g>
                <g id="Group_7" data-name="Group 7">
                  <line id="Line_9" data-name="Line 9" x1="2.39" y2="12.57" transform="translate(480.94 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                  <line id="Line_10" data-name="Line 10" x1="2.39" y1="12.57" transform="translate(531.19 279.21)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
                </g>
              </g>
              <line id="Line_11" data-name="Line 11" y2="11.86" transform="translate(507.26 279.92)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
            </g>
            <line id="Line_12" data-name="Line 12" x2="259.4" transform="translate(377.56 291.78)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_13" data-name="Line 13" x2="259.4" transform="translate(377.56 284.29)" fill="none" stroke="#d7dcf9" stroke-miterlimit="10" stroke-width="1"/>
          </g>
        </g>
        <path id="Path_8" data-name="Path 8" d="M558.73,255.84s-.43,2.84-1.3,3.6-1.31-.84-1.31-.84l2.61-2.76Z" fill="#fed4d5"/>
        <g id="Group_12" data-name="Group 12">
          <rect id="Rectangle_9" data-name="Rectangle 9" width="50.75" height="4.9" rx="1.45" transform="translate(256.23 360.63)" fill="#f26f05"/>
          <path id="Path_9" data-name="Path 9" d="M281.61,360.63H260.79L271.2,333.6l8.07-20.96a2.5,2.5,0,0,1,4.67,0l8.07,20.96,10.41,27.03H281.6Z" fill="#f26f05"/>
          <path id="Path_10" data-name="Path 10" d="M274.06,326.17h15.09l-2.47-6.42H276.53Z" fill="#fff"/>
          <path id="Path_11" data-name="Path 11" d="M268.72,340.05H294.5l-2.47-6.42H271.19Z" fill="#fff"/>
          <path id="Path_12" data-name="Path 12" d="M263.37,353.93h36.47l-2.47-6.42H265.84Z" fill="#fff"/>
        </g>
        <g id="Group_13" data-name="Group 13">
          <rect id="Rectangle_10" data-name="Rectangle 10" width="36.24" height="3.5" rx="1.22" transform="translate(488.04 271.18)" fill="#f26f05"/>
          <path id="Path_13" data-name="Path 13" d="M506.17,271.18H491.3l7.43-19.3,5.77-14.97a1.789,1.789,0,0,1,3.34,0l5.77,14.97,7.43,19.3H506.17Z" fill="#f26f05"/>
          <path id="Path_14" data-name="Path 14" d="M500.78,246.57h10.78l-1.77-4.58h-7.25Z" fill="#fff"/>
          <path id="Path_15" data-name="Path 15" d="M496.96,256.48h18.41l-1.76-4.58H498.72Z" fill="#fff"/>
          <path id="Path_16" data-name="Path 16" d="M493.14,266.4h26.05l-1.77-4.59H494.91Z" fill="#fff"/>
        </g>
        <g id="Group_23" data-name="Group 23">
          <path id="Path_17" data-name="Path 17" d="M572.34,265.07s3.53-1.86-4.83-5.21c-4.27-.12-11.95,14.76-12.05,12.63s4.15-11.23,4.6-13.1-1.94-6.02-4.85-2.17-7.65,12.63-8.65,18.05,4.38,8.83,15.79.76c8.37-5.92,9.44-7.42,10-10.96Z" fill="#f7c21e"/>
          <path id="Path_18" data-name="Path 18" d="M560.83,256.05s.14,3.54-1.97,3.59.9-4,.9-4l1.07.41Z" fill="#fed4d5"/>
          <g id="Group_20" data-name="Group 20">
            <path id="Path_19" data-name="Path 19" d="M566.58,382.34v5.87l4.04.12.12-6.26Z" fill="#f7b0b5"/>
            <path id="Path_20" data-name="Path 20" d="M593.71,382.34v5.87l4.04.12.12-6.26Z" fill="#f7b0b5"/>
            <g id="Group_19" data-name="Group 19">
              <g id="Group_14" data-name="Group 14">
                <path id="Path_21" data-name="Path 21" d="M565.87,386.88s-1.37,2.7-3.2,2.79-3.33-.08-3.65,1.62c-.69,3.66,12.25,2.1,13.31-.3.21-.48-.96-3.88-.96-3.88l-5.5-.23Z" fill="#181f66"/>
                <path id="Path_22" data-name="Path 22" d="M593.15,386.88s-1.37,2.7-3.2,2.79-3.33-.08-3.65,1.62c-.69,3.66,12.25,2.1,13.31-.3.21-.48-.96-3.88-.96-3.88l-5.5-.23Z" fill="#181f66"/>
                <path id="Path_23" data-name="Path 23" d="M567.4,304.51l-3.7,43.85,1.97,34.3,5.7.23,1.17-13.5s2.62-15.64.6-21.28c0,0,7.62-19.46,7.23-34.78l.64-8.81h-13.6Z" fill="#181f66"/>
                <path id="Path_24" data-name="Path 24" d="M571.07,304.51s1.19,6.11,8.77,8.38l6.4,36.4,6.92,33.89,5.43.23-.23-15.74s.24-12.19-2.09-18.16c0,0,3.35-26.18-1.55-44.99H571.07Z" fill="#212c7a"/>
              </g>
              <g id="Group_17" data-name="Group 17">
                <g id="Group_15" data-name="Group 15">
                  <path id="Path_25" data-name="Path 25" d="M567.4,304.51h27.35l1.15-44.65H567.51Z" fill="#f79f1e"/>
                  <path id="Path_26" data-name="Path 26" d="M593.4,260.57s-5.39,2.77.95,9.37,13.33,13,11.94,14.61-10.73,5.31-12.34,6.37-2.72,5.7,2.04,4.91,14.24-3.93,18.69-7.17,1.74-6.92-7.14-17.7-11.29-12.57-14.12-10.38Z" fill="#f7c21e"/>
                </g>
                <g id="Group_16" data-name="Group 16">
                  <path id="Path_27" data-name="Path 27" d="M573.98,245.75s-.08.07-.13.05a.1.1,0,1,1,.13-.05Z" fill="#0a3c5f"/>
                  <path id="Path_28" data-name="Path 28" d="M574.25,245.87s-.08.07-.13.05a.1.1,0,1,1,.13-.05Z" fill="#0a3c5f"/>
                </g>
              </g>
              <g id="Group_18" data-name="Group 18">
                <path id="Path_29" data-name="Path 29" d="M573.08,237.61a44.658,44.658,0,0,0-.55,11.62c.57,4.61,5.08,7.89,9.5,6.48a7.4,7.4,0,0,0,4.54-4.33s3.96-1.04,4.75-4.18c.81-3.23-2.91-2.94-3.69-.8l-.96-8.69-4.78-.94-4.22,1.44-4.6-.6Z" fill="#fed4d5"/>
                <path id="Path_30" data-name="Path 30" d="M585.94,241.81c-.8,4.02,1.29,5.76,1.29,5.76.75-3.18,3.07-2.74,3.07-2.74s3.55-6.08,2.58-9.72-3.39-4.14-3.39-4.14-1.28-2.93-8.16-2.12-2.69,2.71-9.56,4.95c0,0,2.96,1.4-1.13,3.76a8.142,8.142,0,0,0,1.73,6.62s.26-3.99.71-6.55c0,0,2.61.84,4.08,1.17.88.2,6.76-.26,8.23,0,1.31.23.54,3.02.54,3.02Z" fill="#181f66"/>
                <path id="Path_31" data-name="Path 31" d="M575.99,243.91s-2.17,3.39-1.81,4.21,2.15.69,2.15.69l-.34-4.9Z" fill="#f7b0b5"/>
              </g>
              <path id="Path_32" data-name="Path 32" d="M585.48,250.89l-.94,9.26a3.015,3.015,0,0,1-3,2.71h0a3.016,3.016,0,0,1-3.01-2.85l-.29-5.1,7.24-4.02Z" fill="#fed4d5"/>
            </g>
          </g>
          <g id="Group_22" data-name="Group 22">
            <g id="Group_21" data-name="Group 21">
              <path id="Path_33" data-name="Path 33" d="M538.7,251.43h0a1.314,1.314,0,0,1-.77-1.69l6.85-18.27a1.314,1.314,0,0,1,1.69-.77h0a1.314,1.314,0,0,1,.77,1.69l-6.85,18.27A1.314,1.314,0,0,1,538.7,251.43Z" fill="#f7b51e"/>
              <path id="Path_34" data-name="Path 34" d="M557.69,241.92a18.468,18.468,0,0,1-10.45-9.53l-3.43,9.14-3.43,9.14a18.51,18.51,0,0,1,14.14-.31l2.68,1.01,1.58-4.22,1.58-4.22-2.68-1.01Z" fill="#181f66"/>
              <path id="Path_35" data-name="Path 35" d="M562.11,253.2l-1.26-.47,3.16-8.44,1.26.47a4.508,4.508,0,0,1,2.64,5.8h0A4.508,4.508,0,0,1,562.11,253.2Z" fill="#181f66"/>
              <path id="Path_36" data-name="Path 36" d="M556.89,252.2l.2,7.4,1.56.59,2.41-6.43Z" fill="#181f66"/>
              <path id="Path_37" data-name="Path 37" d="M556.92,253.14s-1.64-.46-1.71.8.09,4.02.84,4.47,1.75.7,2.16.44.6-4.17.43-4.88-1.71-.83-1.71-.83Z" fill="#fed4d5"/>
              <rect id="Rectangle_11" data-name="Rectangle 11" width="10.8" height="7.04" rx="0.84" transform="translate(555.742 251.734) rotate(-69.45)" fill="#f7b51e"/>
            </g>
            <path id="Path_38" data-name="Path 38" d="M560.56,255.11s-1.72-1.26-2.25-.57,1.57,2.4,1.57,2.4,1.59-.27.69-1.83Z" fill="#fed4d5"/>
          </g>
          <path id="Path_39" data-name="Path 39" d="M590.63,300.08c-.21,3.15-2.35-.99-3.06-4.06s1.84-3.67,4.92-4.38,2.65-1.4,4.28,1.34c2.13,3.6-5.92,3.83-6.13,7.1Z" fill="#fed4d5"/>
          <path id="Path_40" data-name="Path 40" d="M569.71,239.5l23.09,2.73.3-1.45-23.3-2.83Z" fill="#f79f1e"/>
          <path id="Path_41" data-name="Path 41" d="M585.97,227.02s10.78,1.03,6.57,13.69l-22.38-2.72s1.46-13.84,15.81-10.98Z" fill="#f7b51e"/>
          <path id="Path_42" data-name="Path 42" d="M573.8,238.53s2.98-10.66,9.63-12.31a5.519,5.519,0,0,1,3.52.76,19.523,19.523,0,0,0-8.47,12.11l-4.68-.57Z" fill="#f79f1e"/>
          <path id="Path_43" data-name="Path 43" d="M593.51,235.17a3.394,3.394,0,0,0-2.24-.6c-4.05.36-5.11,5.37-5.11,5.37l6.39.78a16.444,16.444,0,0,0,.96-5.55Z" fill="#f79f1e"/>
        </g>
        <g id="Group_27" data-name="Group 27">
          <g id="Group_24" data-name="Group 24">
            <rect id="Rectangle_12" data-name="Rectangle 12" width="10.92" height="84.15" transform="translate(402.53 190.51)" fill="#bbc5ef"/>
            <rect id="Rectangle_13" data-name="Rectangle 13" width="10.92" height="84.15" transform="translate(468.83 190.51)" fill="#bbc5ef"/>
          </g>
          <circle id="Ellipse_1" data-name="Ellipse 1" cx="8.52" cy="8.52" r="8.52" transform="translate(395.939 185.21) rotate(-45)" fill="#f26f05"/>
          <path id="Path_44" data-name="Path 44" d="M413.81,185.21a5.82,5.82,0,1,1-5.82-5.82A5.826,5.826,0,0,1,413.81,185.21Z" fill="#f7c21e"/>
          <circle id="Ellipse_2" data-name="Ellipse 2" cx="8.52" cy="8.52" r="8.52" transform="translate(462.24 185.209) rotate(-45)" fill="#f26f05"/>
          <path id="Path_45" data-name="Path 45" d="M480.11,185.21a5.82,5.82,0,1,1-5.82-5.82A5.826,5.826,0,0,1,480.11,185.21Z" fill="#f7c21e"/>
          <rect id="Rectangle_14" data-name="Rectangle 14" width="99.14" height="52.46" transform="translate(391.57 197.26)" fill="#f7c21e"/>
          <g id="Group_26" data-name="Group 26" clip-path="url(#clip-path)">
            <g id="Group_25" data-name="Group 25">
              <path id="Path_46" data-name="Path 46" d="M341.61,194.53l64.22,64.23h5.45L347.02,194.5Z" fill="#f26f05"/>
              <path id="Path_47" data-name="Path 47" d="M355.78,194.53,420,258.76h5.45L361.19,194.5Z" fill="#f26f05"/>
              <path id="Path_48" data-name="Path 48" d="M369.95,194.53l64.22,64.23h5.45L375.36,194.5Z" fill="#f26f05"/>
              <path id="Path_49" data-name="Path 49" d="M384.12,194.53l64.22,64.23h5.45L389.53,194.5Z" fill="#f26f05"/>
              <path id="Path_50" data-name="Path 50" d="M398.29,194.53l64.22,64.23h5.45L403.7,194.5Z" fill="#f26f05"/>
              <path id="Path_51" data-name="Path 51" d="M412.46,194.53l64.22,64.23h5.45L417.87,194.5Z" fill="#f26f05"/>
              <path id="Path_52" data-name="Path 52" d="M426.63,194.53l64.22,64.23h5.45L432.04,194.5Z" fill="#f26f05"/>
              <path id="Path_53" data-name="Path 53" d="M440.8,194.53l64.22,64.23h5.45L446.21,194.5Z" fill="#f26f05"/>
              <path id="Path_54" data-name="Path 54" d="M454.97,194.53l64.22,64.23h5.45L460.38,194.5Z" fill="#f26f05"/>
              <path id="Path_55" data-name="Path 55" d="M469.14,194.53l64.22,64.23h5.45L474.55,194.5Z" fill="#f26f05"/>
              <path id="Path_56" data-name="Path 56" d="M483.31,194.53l64.22,64.23h5.45L488.72,194.5Z" fill="#f26f05"/>
            </g>
          </g>
          <rect id="Rectangle_16" data-name="Rectangle 16" width="90.7" height="44.1" transform="translate(395.79 201.45)" fill="#fff"/>
        </g>
        <g id="Group_28" data-name="Group 28">
          <text id="UNDER" transform="translate(403.23 225.07)" fill="#f26f05" font-size="20.93" font-family="OpenSans-Bold, Open Sans" font-weight="700"><tspan x="0" y="0">UNDER</tspan></text>
          <text id="CONSTRUCTION" transform="translate(403.23 236.17)" fill="#f26f05" font-size="9.43" font-family="OpenSans-Bold, Open Sans" font-weight="700"><tspan x="0" y="0">CONSTRUCTION</tspan></text>
        </g>
        <rect id="Rectangle_17" data-name="Rectangle 17" width="99.14" height="35.22" transform="translate(391.57 127.08)" fill="#04a17f"/>
        <rect id="Rectangle_18" data-name="Rectangle 18" width="69.03" height="35.22" transform="translate(501.36 169.48)" fill="#04a17f"/>
        <rect id="Rectangle_19" data-name="Rectangle 19" width="121.59" height="6.29" transform="translate(501.36 127.08)" fill="#04a17f"/>
        <rect id="Rectangle_20" data-name="Rectangle 20" width="121.59" height="6.29" transform="translate(501.36 141.54)" fill="#04a17f"/>
        <rect id="Rectangle_21" data-name="Rectangle 21" width="69.03" height="6.29" transform="translate(501.36 156.01)" fill="#04a17f"/>
        <path id="Path_118" data-name="Path 118" d="M17.61,0A17.61,17.61,0,1,1,0,17.61,17.61,17.61,0,0,1,17.61,0Z" transform="translate(585.246 201.765) rotate(-81.08)" fill="#04a17f"/>
        <g id="Group_32" data-name="Group 32">
          <path id="Path_57" data-name="Path 57" d="M353.01,242.26s-2.08,7,1.89,15.53-2.7,20.91,3.23,23.43,19.42-4.78,23.21-13.49,1.38-20.4-5.42-29.36c-6.69-8.82-22.9,3.9-22.9,3.9Z" fill="#181f66"/>
          <g id="Group_30" data-name="Group 30">
            <path id="Path_58" data-name="Path 58" d="M358.13,263.26s-2.21,15.68-6.25,22.84-15.86,3.13-27.38-3.73l2.04-5.69a59.218,59.218,0,0,0,12.16,4.44c7.44,1.56,7.56-10.93,7.56-10.93s.14-6.08,1.28-8.83c2.75-6.65,10.56-4.51,10.59,1.89Z" fill="#f7b51e"/>
            <path id="Path_59" data-name="Path 59" d="M326.57,350.22,328.16,336s3.46-10.06,2.52-17.13c0,0-.2-3.37.84-3.72s20.99,1.86,20.99,1.86c14.38,1.99,12.45-13.58,12.45-13.58s-26.76-9.55-37.8.04c-9.11,7.92-6.05,46.52-6.05,46.52l5.45.22" fill="#4753a5"/>
            <g id="Group_29" data-name="Group 29">
              <path id="Path_60" data-name="Path 60" d="M352.7,301.88h27.29l1.15-44.55H352.81Z" fill="#fff"/>
              <path id="Path_61" data-name="Path 61" d="M370.04,257.33v44.55h9.95l1.15-44.55Z" fill="#f79f1e"/>
              <path id="Path_62" data-name="Path 62" d="M360.62,257.33l-1.05,44.48-10.78-.7,4.02-43.78Z" fill="#f79f1e"/>
              <path id="Path_63" data-name="Path 63" d="M346.33,315.89h30.63l1.78-14.03H346.33Z" fill="#4753a5"/>
              <path id="Path_64" data-name="Path 64" d="M366.46,299.24c-.7,3.02-2.13-1.33-2.33-4.42s2.37-3.27,5.46-3.48,2.8-.95,3.94,1.98c1.5,3.83-6.34,2.79-7.07,5.93Z" fill="#fed4d5"/>
            </g>
            <path id="Path_65" data-name="Path 65" d="M356.35,357.59l-2.48-14.09s.48-10.63-2.42-17.15c0,0-1.15-3.18-.24-3.8s20.66-4.13,20.66-4.13c14.36-2.15,8.12-16.54,8.12-16.54s-28.37-1.62-36.25,10.7c-6.5,10.16,7.26,46.1,7.26,46.1l5.35-1.08" fill="#5565b7"/>
            <path id="Path_66" data-name="Path 66" d="M355.85,301.11H374.5a1.075,1.075,0,0,1,1.08,1.08v.11a1.075,1.075,0,0,1-1.08,1.08H355.85a1.075,1.075,0,0,1-1.08-1.08v-.11A1.075,1.075,0,0,1,355.85,301.11Z" fill="#d7dcf9"/>
            <path id="Path_67" data-name="Path 67" d="M316.92,273.92h39.34a1.874,1.874,0,0,1,1.87,1.87v25.72a1.874,1.874,0,0,1-1.87,1.87H316.92a1.874,1.874,0,0,1-1.87-1.87V275.79A1.874,1.874,0,0,1,316.92,273.92Z" fill="#d7dcf9"/>
            <path id="Path_68" data-name="Path 68" d="M356.26,273.92h-1.4a1.874,1.874,0,0,1,1.87,1.87v25.72a1.874,1.874,0,0,1-1.87,1.87h1.4a1.874,1.874,0,0,0,1.87-1.87V275.79A1.874,1.874,0,0,0,356.26,273.92Z" fill="#fff"/>
          </g>
          <path id="Path_69" data-name="Path 69" d="M321.12,350s-1.36,2.67-3.17,2.76-3.3-.08-3.61,1.6c-.68,3.62,12.14,2.08,13.19-.3.21-.48-.95-3.84-.95-3.84l-5.45-.22Z" fill="#4753a5"/>
          <path id="Path_70" data-name="Path 70" d="M351,358.68s-.69,2.92-2.42,3.44-3.22.71-3.13,2.42c.2,3.68,12.28-.87,12.74-3.43.09-.52-1.84-3.51-1.84-3.51L351,358.68Z" fill="#5565b7"/>
          <path id="Path_71" data-name="Path 71" d="M320.79,343.97c.14,3.67.33,6.03.33,6.03l5.45.22.65-5.85-6.44-.4Z" fill="#fed4d5"/>
          <path id="Path_72" data-name="Path 72" d="M351,358.68l5.35-1.08-.95-5.38-6.17,1.52c1.04,3.04,1.77,4.94,1.77,4.94Z" fill="#fed4d5"/>
          <path id="Path_73" data-name="Path 73" d="M393.3,275.25a92.617,92.617,0,0,0-8.92-15.96,4.94,4.94,0,0,0-7-1.13h0a4.934,4.934,0,0,0-1.15,6.67c2.05,3.09,12,15.85,11.64,16.31s-16.89,9.78-16.89,9.78l2.25,4.52s19.4-7.31,22.18-10.94c1.56-2.03-2.11-9.26-2.11-9.26Z" fill="#f7b51e"/>
          <path id="Path_74" data-name="Path 74" d="M333.6,288.65v-2.91l2.52,1.45,2.53,1.46-2.53,1.46-2.52,1.45Z" fill="#fff"/>
          <g id="Group_31" data-name="Group 31">
            <path id="Path_75" data-name="Path 75" d="M354.9,239.64a43.741,43.741,0,0,0,2.31,11.2c1.66,4.25,6.76,6.28,10.63,3.86a7.21,7.21,0,0,0,3.26-5.23s3.52-1.96,3.51-5.14c-.01-3.27-3.49-2.09-3.7.14l-3.03-8.04-4.78.27-3.67,2.39-4.52.54Z" fill="#fed4d5"/>
            <path id="Path_76" data-name="Path 76" d="M355.12,241.86s10.59,1.38,15.97,7.62l-1.36-6.79.09-2.34s.24-1.51-1.65-1.36a27.271,27.271,0,0,0-5.36.62c-1.06.41-7.69,2.27-7.69,2.27Z" fill="#181f66"/>
            <path id="Path_77" data-name="Path 77" d="M368.17,240.51c.22,4.02,2.63,5.17,2.63,5.17-.06-3.21,2.26-3.36,2.26-3.36s1.9-6.66.09-9.89-4.24-3.12-4.24-3.12-1.93-2.48-8.29-.03-1.9,3.23-7.9,7.04c0,0,3.16.61-.16,3.85a8.013,8.013,0,0,0,3.25,5.88s-.72-3.86-.92-6.41c0,0,2.69.16,4.18.12.89-.03,6.38-1.89,7.84-2,1.31-.1,1.25,2.75,1.25,2.75Z" fill="#181f66"/>
            <path id="Path_78" data-name="Path 78" d="M359.21,244.93s-1.24,3.75-.7,4.45,2.21.13,2.21.13l-1.52-4.58Z" fill="#f7b0b5"/>
          </g>
          <path id="Path_79" data-name="Path 79" d="M369.27,249.86l-.93,9.1a2.962,2.962,0,0,1-2.95,2.66h0a2.969,2.969,0,0,1-2.96-2.8l-.28-5.01,7.11-3.95Z" fill="#fed4d5"/>
          <path id="Path_80" data-name="Path 80" d="M352.13,242.29l24.88-3.42-.06-1.45-25.12,3.37Z" fill="#f79f1e"/>
          <path id="Path_81" data-name="Path 81" d="M364.57,226.44s12.37-1.92,11.45,11.16l-23.83,3.15s-1.97-13.53,12.38-14.3Z" fill="#f7b51e"/>
          <path id="Path_82" data-name="Path 82" d="M355.81,240.34s.24-10.88,6.17-14.06a5.409,5.409,0,0,1,3.54-.13,19.24,19.24,0,0,0-5.12,13.6l-4.59.6Z" fill="#f79f1e"/>
          <path id="Path_83" data-name="Path 83" d="M371.1,249.47s4.52,1.12,4.49.55,1.43-2.21.28-5.34a5.952,5.952,0,0,0-4.15-3.71,2.024,2.024,0,0,0-1.68.44c-.75.66-1.59,1.15-1.31,1.76s2.36,6.3,2.36,6.3Z" fill="#181f66"/>
        </g>
        <g id="Group_36" data-name="Group 36" clip-path="url(#clip-path-2)">
          <g id="Group_35" data-name="Group 35">
            <g id="Group_33" data-name="Group 33">
              <path id="Path_84" data-name="Path 84" d="M474.67,435.6,444.32,289.75H412.67L443.02,435.6" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_14" data-name="Line 14" x1="31.64" transform="translate(440.63 421.73)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_15" data-name="Line 15" x1="31.65" transform="translate(435.51 398.88)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_16" data-name="Line 16" x1="31.65" transform="translate(430.4 376.02)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_17" data-name="Line 17" x1="31.65" transform="translate(425.29 353.17)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_18" data-name="Line 18" x1="31.65" transform="translate(420.18 330.32)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_19" data-name="Line 19" x1="31.65" transform="translate(415.07 307.47)" fill="none" stroke="#5565b7" stroke-miterlimit="10" stroke-width="2.72"/>
            </g>
            <g id="Group_34" data-name="Group 34">
              <path id="Path_85" data-name="Path 85" d="M377.2,435.6l30.34-145.85h31.65L408.85,435.6" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_20" data-name="Line 20" x2="31.65" transform="translate(379.59 421.73)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_21" data-name="Line 21" x2="31.64" transform="translate(384.71 398.88)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_22" data-name="Line 22" x2="31.65" transform="translate(389.82 376.02)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_23" data-name="Line 23" x2="31.65" transform="translate(394.93 353.17)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_24" data-name="Line 24" x2="31.65" transform="translate(400.04 330.32)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
              <line id="Line_25" data-name="Line 25" x2="31.65" transform="translate(405.15 307.47)" fill="none" stroke="#6c80d3" stroke-miterlimit="10" stroke-width="2.72"/>
            </g>
          </g>
        </g>
        <g id="Group_43" data-name="Group 43">
          <g id="Group_40" data-name="Group 40">
            <g id="Group_39" data-name="Group 39">
              <path id="Path_86" data-name="Path 86" d="M635.9,252.43c-3.62.71-8.66,3.64-7.62,14.63.44,4.7-1.61,22.44,1.28,23.94,5.41,2.81,22.96,7.84,24.79-4.15,0,0,1.64-19.75.32-26.32-1.85-13.24-12.24-13.28-18.78-8.1Z" fill="#212c7a"/>
              <path id="Path_87" data-name="Path 87" d="M654.28,292.66s6.98,6.03,12.73,4.6c6.96-1.72,4.82-13.89-9.87-26.79l-2.78,3.38s11.04,14.09,9.66,15.24-7.25-11.08-11.22-11.51,1.48,15.07,1.48,15.07Z" fill="#fed4d5"/>
              <path id="Path_88" data-name="Path 88" d="M647.53,267.67l2.88-1.29h0l2.88-1.29a.582.582,0,0,1,.77.3L659,276.45a.582.582,0,0,1-.3.77l-2.88,1.29-2.88,1.29a.582.582,0,0,1-.77-.3l-4.94-11.06A.582.582,0,0,1,647.53,267.67Z" fill="#f7941e"/>
              <path id="Path_89" data-name="Path 89" d="M647.99,268.06l1.07-.48a.256.256,0,0,1,.34.13.209.209,0,0,0,.28.11l1.15-.51,1.15-.51a.209.209,0,0,0,.11-.28.256.256,0,0,1,.13-.34l1.07-.48a.293.293,0,0,1,.39.15l4.73,10.59a.293.293,0,0,1-.15.39l-2.65,1.18h0l-2.65,1.18a.293.293,0,0,1-.39-.15l-4.73-10.59a.293.293,0,0,1,.15-.39Z" fill="#fff"/>
              <path id="Path_90" data-name="Path 90" d="M655.41,266.5s-1.19-.06-1.13,1.74,1.52,5.8,3.17,6.2c.44.11,1.52-.27,1.3-2.53s-1.49-5.65-3.34-5.41Z" fill="#fed4d5"/>
              <g id="Group_37" data-name="Group 37">
                <path id="Path_91" data-name="Path 91" d="M658.41,402.92s1.31,2.56,3.04,2.65,3.17-.08,3.47,1.54c.65,3.48-11.65,2-12.66-.29-.2-.46.91-3.69.91-3.69l5.23-.22Z" fill="#181f66"/>
                <path id="Path_92" data-name="Path 92" d="M632.47,402.92s1.31,2.56,3.04,2.65,3.17-.08,3.47,1.54c.65,3.48-11.65,2-12.66-.29-.2-.46.91-3.69.91-3.69l5.23-.22Z" fill="#181f66"/>
                <path id="Path_93" data-name="Path 93" d="M656.96,324.61l3.52,41.69-1.88,36.63-5.42.22-1.11-16.85s-2.49-14.87-.57-20.23c0,0-7.25-18.51-6.87-33.07l-.6-8.38h12.93Z" fill="#fed4d5"/>
                <path id="Path_94" data-name="Path 94" d="M653.47,324.61s-1.13,5.81-8.34,7.96l-6.08,34.61-6.58,35.74-5.16.22.22-18.48s-.23-11.59,1.99-17.27c0,0-3.19-24.89,1.47-42.78h22.48Z" fill="#fed4d5"/>
                <path id="Path_95" data-name="Path 95" d="M656.96,324.61H630.99c.42-2.86.89-4.99.66-7.55,2.86-15.72-.06-18.8-.77-22.71-1.51-8.28-3.35-17.19,4.86-17.19h13.45a4.715,4.715,0,0,1,4.06,2.29c2.59,4.32,6.74,11.14,3.8,18.82-3.48,9.12-.1,26.34-.1,26.34Z" fill="#f79f1e"/>
              </g>
              <g id="Group_38" data-name="Group 38">
                <path id="Path_96" data-name="Path 96" d="M653.12,263.34s.57,1.9-.48,6.5a6.665,6.665,0,0,1-8.97,4.85,6.5,6.5,0,0,1-3.6-4.23s-3.4-1.28-3.81-4.12c-.42-2.92,3.02-3.15,3.77,1.06,0,0,1.09-1.03.39-6.19,3.81.37,7.39-.92,8.43-3.23a31.787,31.787,0,0,1,4.27,5.37Z" fill="#fed4d5"/>
                <path id="Path_97" data-name="Path 97" d="M650.07,264.83s1.6,3.19,1.21,3.87-1.96.41-1.96.41l.75-4.29Z" fill="#f7b0b5"/>
              </g>
              <path id="Path_98" data-name="Path 98" d="M640.04,269.54l1.08,10.61h4.1l1.23-7.05Z" fill="#fed4d5"/>
              <path id="Path_99" data-name="Path 99" d="M627.4,394.91l-.1,8.23,5.17-.22,1.47-8.01Z" fill="#fed4d5"/>
              <path id="Path_100" data-name="Path 100" d="M652.64,394.91l.54,8.23,5.42-.22.41-8.01Z" fill="#fed4d5"/>
            </g>
            <path id="Path_101" data-name="Path 101" d="M655.99,317.05H631.65a61.621,61.621,0,0,1-.66,7.55h25.97A67.423,67.423,0,0,1,655.99,317.05Z" fill="#f7c21e"/>
            <path id="Path_102" data-name="Path 102" d="M656.3,320.27H631.47c-.11,1.38-.26,2.81-.49,4.33h25.97s-.34-1.72-.66-4.33Z" fill="#181f66"/>
          </g>
          <path id="Path_103" data-name="Path 103" d="M628.65,360.89h31.83c1.16-22.21-3.52-36.29-3.52-36.29H630.99s-5.37,17.82-2.33,36.29Z" fill="#212c7a"/>
          <g id="Group_41" data-name="Group 41">
            <rect id="Rectangle_23" data-name="Rectangle 23" width="26.55" height="18.16" rx="1.15" transform="translate(623.64 316.07)" fill="#d7dcf9"/>
            <path id="Path_104" data-name="Path 104" d="M649.05,316.07h-.86a1.15,1.15,0,0,1,1.15,1.15v15.86a1.144,1.144,0,0,1-1.15,1.15h.86a1.15,1.15,0,0,0,1.15-1.15V317.22A1.144,1.144,0,0,0,649.05,316.07Z" fill="#fff"/>
            <path id="Path_105" data-name="Path 105" d="M635.07,325.15v-1.79l1.56.9,1.55.89-1.55.9-1.56.9Z" fill="#fff"/>
          </g>
          <path id="Path_106" data-name="Path 106" d="M643.14,282.36c3.57.52,4.66-2.95,4.49-5.18v-.01h-8.06l-.05.01S639.58,281.85,643.14,282.36Z" fill="#fed4d5"/>
          <g id="Group_42" data-name="Group 42">
            <path id="Path_107" data-name="Path 107" d="M634.87,322.65c-1.11,2.6,2.28.17,4.09-2s-.07-4.61-2.23-6.42-1.46-1.27-3.88.26c-3.16,2.01,3.16,5.46,2.01,8.16Z" fill="#fed4d5"/>
            <path id="Path_108" data-name="Path 108" d="M630.9,279.14a4.786,4.786,0,0,1,6.82-.81h0c1.72,1.64,1.41,3.77.03,5.83-1.88,2.81-7.86,12.81-7.58,13.26s5.65,16.07,5.65,16.07l-3.86,2.93s-7.08-13.06-8.69-17.84c-.86-2.56,3.78-13.77,7.62-19.44Z" fill="#fed4d5"/>
          </g>
        </g>
        <path id="Path_109" data-name="Path 109" d="M640.03,267.4s7.62-4.28,8.82-9.43c1.35-5.76-8.76,1.08-8.81,1.14a3.584,3.584,0,0,0-1.14,1.02c-.35.6,1.09,4.88,1.13,7.26Z" fill="#212c7a"/>
        <g id="Group_44" data-name="Group 44">
          <rect id="Rectangle_24" data-name="Rectangle 24" width="61.59" height="5.94" rx="1.87" transform="translate(488.79 409.03)" fill="#f26f05"/>
          <path id="Path_110" data-name="Path 110" d="M519.58,409.03H494.32l12.63-32.81,9.8-25.44a3.04,3.04,0,0,1,5.67,0l9.8,25.44,12.63,32.81H519.59Z" fill="#f26f05"/>
          <path id="Path_111" data-name="Path 111" d="M510.42,367.21h18.32l-3-7.79H513.43Z" fill="#fff"/>
          <path id="Path_112" data-name="Path 112" d="M503.94,384.05h31.29l-3-7.79H506.94Z" fill="#fff"/>
          <path id="Path_113" data-name="Path 113" d="M497.45,400.9h44.26l-3-7.8H500.45Z" fill="#fff"/>
        </g>
        <path id="Path_114" data-name="Path 114" d="M476.25,95.01a12.559,12.559,0,0,0-9.15,3.96,19.448,19.448,0,0,0-38.47-1.94,12.575,12.575,0,0,0-19.41,10.56h79.59a12.577,12.577,0,0,0-12.58-12.58Z" fill="#bbc5ef"/>
        <path id="Path_115" data-name="Path 115" d="M667.62,153.64a11.02,11.02,0,0,0-8.04,3.48,17.1,17.1,0,0,0-33.82-1.71,11.039,11.039,0,0,0-17.05,9.27h69.96a11.047,11.047,0,0,0-11.05-11.05Z" fill="#bbc5ef"/>
        <path id="Path_116" data-name="Path 116" d="M357.53,187.61a11.02,11.02,0,0,0-8.04,3.48,17.1,17.1,0,0,0-33.82-1.71,11.039,11.039,0,0,0-17.05,9.27h69.96a11.047,11.047,0,0,0-11.05-11.05Z" fill="#bbc5ef"/>
      </g>
    </g>
  </g>
</svg>
