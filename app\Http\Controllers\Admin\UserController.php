<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $currentUser = auth()->user();

        if ($currentUser->hasRole('admin')) {
            // Admin view: Show all users grouped by property owners
            if ($request->has('owner_id') && $request->owner_id) {
                // Show employees of specific property owner
                $owner = User::findOrFail($request->owner_id);
                $query = User::with(['roles', 'creator'])
                    ->where('created_by', $owner->id);
                $pageTitle = "Employees of " . $owner->name;
            } else {
                // Show all users with their creators
                $query = User::with(['roles', 'creator']);
                $pageTitle = "All Users";
            }
        } else if ($currentUser->hasRole('property_owner')) {
            // Property owner view: Show only their employees
            $query = User::with(['roles', 'creator'])
                ->where('created_by', $currentUser->id);
            $pageTitle = "My Employees";
        } else {
            // Other roles: No access
            abort(403, 'You do not have permission to view users.');
        }

        // Filter by role if specified
        if ($request->has('role') && $request->role) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $users = $query->paginate(10);

        // Get property owners for admin view
        $propertyOwners = null;
        if ($currentUser->hasRole('admin')) {
            $propertyOwners = User::whereHas('roles', function ($q) {
                $q->where('name', 'property_owner');
            })->withCount('employees')->get();
        }

        // Determine which view to use based on user role
        if ($currentUser->hasRole('admin')) {
            return view('admin.users.index', compact('users', 'propertyOwners', 'pageTitle'));
        } else {
            return view('owner.users.index', compact('users', 'pageTitle'));
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if user can create users
        if (!auth()->user()->canCreateUsers()) {
            abort(403, 'You do not have permission to create users.');
        }

        // Get allowed roles for current user
        $allowedRoles = auth()->user()->getAllowedRoles();
        $roles = Role::whereIn('name', $allowedRoles)->get();
        $allowedUserTypes = auth()->user()->getAllowedUserTypes();

        // Determine which view to use based on user role
        if (auth()->user()->hasRole('admin')) {
            return view('admin.users.create', compact('roles', 'allowedUserTypes'));
        } else {
            return view('owner.users.create', compact('roles', 'allowedUserTypes'));
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if user can create users
        if (!auth()->user()->canCreateUsers()) {
            abort(403, 'You do not have permission to create users.');
        }

        // Get allowed types and roles for validation
        $allowedUserTypes = auth()->user()->getAllowedUserTypes();
        $allowedRoles = auth()->user()->getAllowedRoles();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'zip_code' => 'nullable|string|max:10',
            'user_type' => 'required|in:' . implode(',', $allowedUserTypes),
            'role' => 'required|in:' . implode(',', $allowedRoles),
            'is_active' => 'boolean',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Verify the role exists and is allowed
        if (!in_array($request->role, $allowedRoles)) {
            return back()->withErrors(['role' => 'You are not authorized to assign this role.']);
        }

        $currentUser = auth()->user();

        // Handle profile photo upload
        $profilePhotoPath = null;
        if ($request->hasFile('profile_photo')) {
            $profilePhotoPath = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'zip_code' => $request->zip_code,
            'user_type' => $request->user_type,
            'is_active' => $request->boolean('is_active', true),
            'email_verified_at' => now(),
            'created_by' => $currentUser->hasRole('property_owner') ? $currentUser->id : null,
            'profile_photo' => $profilePhotoPath,
        ]);

        $user->assignRole($request->role);

        // Redirect based on user role
        if (auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully.');
        } else {
            return redirect()->route('owner.users.index')
                ->with('success', 'Employee created successfully.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $currentUser = auth()->user();

        // Check if user can view this user
        if (!$currentUser->hasRole('admin') && !$user->canBeManagedBy($currentUser)) {
            abort(403, 'You do not have permission to view this user.');
        }

        $user->load('roles', 'ownedProperties', 'managedProperties', 'rentedUnits', 'complaints');

        // If viewing a property owner, load their employees
        $employees = null;
        $employeesByRole = null;
        if ($user->hasRole('property_owner')) {
            $employees = $user->employees()->with('roles')->get();
            $employeesByRole = $user->getEmployeesByRole();
        }

        // Determine which view to use based on user role
        if ($currentUser->hasRole('admin')) {
            return view('admin.users.show', compact('user', 'employees', 'employeesByRole'));
        } else {
            return view('owner.users.show', compact('user', 'employees', 'employeesByRole'));
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        // Check if user can edit this user
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        // Get allowed roles for current user
        $allowedRoles = auth()->user()->getAllowedRoles();
        $roles = Role::whereIn('name', $allowedRoles)->get();
        $allowedUserTypes = auth()->user()->getAllowedUserTypes();

        // Determine which view to use based on user role
        if (auth()->user()->hasRole('admin')) {
            return view('admin.users.edit', compact('user', 'roles', 'allowedUserTypes'));
        } else {
            return view('owner.users.edit', compact('user', 'roles', 'allowedUserTypes'));
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        // Check if user can edit this user
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        // Get allowed types and roles for validation
        $allowedUserTypes = auth()->user()->getAllowedUserTypes();
        $allowedRoles = auth()->user()->getAllowedRoles();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'zip_code' => 'nullable|string|max:10',
            'user_type' => 'required|in:' . implode(',', $allowedUserTypes),
            'role' => 'required|in:' . implode(',', $allowedRoles),
            'is_active' => 'boolean',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Verify the role is allowed
        if (!in_array($request->role, $allowedRoles)) {
            return back()->withErrors(['role' => 'You are not authorized to assign this role.']);
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'zip_code' => $request->zip_code,
            'user_type' => $request->user_type,
            'is_active' => $request->boolean('is_active', true),
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        // Handle profile photo update
        if ($request->hasFile('profile_photo')) {
            // Delete old profile photo if exists
            if ($user->profile_photo && file_exists(public_path('storage/' . $user->profile_photo))) {
                unlink(public_path('storage/' . $user->profile_photo));
            }

            // Store new profile photo
            $updateData['profile_photo'] = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        $user->update($updateData);
        $user->syncRoles([$request->role]);

        // Redirect based on user role
        if (auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.users.index')
                ->with('success', 'User updated successfully.');
        } else {
            return redirect()->route('owner.users.index')
                ->with('success', 'Employee updated successfully.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Check if user can manage this user
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to delete this user.');
        }

        // Prevent deletion of the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Activate a user account
     */
    public function activate(User $user)
    {
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to activate this user.');
        }

        $user->activate();

        return redirect()->back()
            ->with('success', 'User activated successfully.');
    }

    /**
     * Deactivate a user account
     */
    public function deactivate(User $user)
    {
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to deactivate this user.');
        }

        // Prevent deactivating own account
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot deactivate your own account.');
        }

        $user->deactivate();

        return redirect()->back()
            ->with('success', 'User deactivated successfully.');
    }

    /**
     * Toggle user status (activate/deactivate)
     */
    public function toggleStatus(User $user)
    {
        if (!$this->canManageUser($user)) {
            abort(403, 'You do not have permission to change this user\'s status.');
        }

        // Prevent deactivating own account
        if ($user->id === auth()->id() && $user->is_active) {
            return redirect()->back()
                ->with('error', 'You cannot deactivate your own account.');
        }

        $user->toggleStatus();
        $status = $user->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
            ->with('success', "User {$status} successfully.");
    }

    /**
     * Check if current user can manage the given user
     */
    private function canManageUser(User $user): bool
    {
        return $user->canBeManagedBy(auth()->user());
    }
}
