<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-user-add-line text-primary me-2"></i>Check In Visitor
                        </h4>
                        <p class="text-muted mb-0">
                            Register a new visitor to the property
                        </p>
                    </div>
                    <div>
                        <a href="<?php echo e(route('visitors.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Visitors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Check-in Form -->
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('visitors.store')); ?>" method="POST" id="checkInForm">
                    <?php echo csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Visitor Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-user-line me-2"></i>Visitor Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                       id="name" name="name" value="<?php echo e(old('name')); ?>" 
                                                       placeholder="Enter visitor's full name" required>
                                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                                <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                       id="phone" name="phone" value="<?php echo e(old('phone')); ?>" 
                                                       placeholder="Enter phone number" required>
                                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email Address</label>
                                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                       id="email" name="email" value="<?php echo e(old('email')); ?>" 
                                                       placeholder="Enter email address (optional)">
                                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                                <input type="tel" class="form-control <?php $__errorArgs = ['emergency_contact'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                       id="emergency_contact" name="emergency_contact" value="<?php echo e(old('emergency_contact')); ?>" 
                                                       placeholder="Emergency contact number">
                                                <?php $__errorArgs = ['emergency_contact'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="identification_type" class="form-label">ID Type <span class="text-danger">*</span></label>
                                                <select class="form-select <?php $__errorArgs = ['identification_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                        id="identification_type" name="identification_type" required>
                                                    <option value="">Select ID Type</option>
                                                    <option value="national_id" <?php echo e(old('identification_type') == 'national_id' ? 'selected' : ''); ?>>National ID</option>
                                                    <option value="passport" <?php echo e(old('identification_type') == 'passport' ? 'selected' : ''); ?>>Passport</option>
                                                    <option value="driver_license" <?php echo e(old('identification_type') == 'driver_license' ? 'selected' : ''); ?>>Driver's License</option>
                                                    <option value="other" <?php echo e(old('identification_type') == 'other' ? 'selected' : ''); ?>>Other</option>
                                                </select>
                                                <?php $__errorArgs = ['identification_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="identification_number" class="form-label">ID Number <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control <?php $__errorArgs = ['identification_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                       id="identification_number" name="identification_number" value="<?php echo e(old('identification_number')); ?>" 
                                                       placeholder="Enter ID number" required>
                                                <?php $__errorArgs = ['identification_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="vehicle_number" class="form-label">Vehicle Number</label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['vehicle_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="vehicle_number" name="vehicle_number" value="<?php echo e(old('vehicle_number')); ?>" 
                                               placeholder="Enter vehicle registration number (if applicable)">
                                        <?php $__errorArgs = ['vehicle_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Visit Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-map-pin-line me-2"></i>Visit Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                                <select class="form-select <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                        id="property_id" name="property_id" required>
                                                    <option value="">Select Property</option>
                                                    <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($property->id); ?>" <?php echo e(old('property_id') == $property->id ? 'selected' : ''); ?>>
                                                            <?php echo e($property->name); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="visiting_unit_id" class="form-label">Unit</label>
                                                <select class="form-select <?php $__errorArgs = ['visiting_unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                        id="visiting_unit_id" name="visiting_unit_id">
                                                    <option value="">Select Unit (Optional)</option>
                                                </select>
                                                <?php $__errorArgs = ['visiting_unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="visiting_tenant_id" class="form-label">Visiting Tenant</label>
                                                <select class="form-select <?php $__errorArgs = ['visiting_tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                        id="visiting_tenant_id" name="visiting_tenant_id">
                                                    <option value="">Select Tenant (Optional)</option>
                                                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($tenant->id); ?>" <?php echo e(old('visiting_tenant_id') == $tenant->id ? 'selected' : ''); ?>>
                                                            <?php echo e($tenant->name); ?> (<?php echo e($tenant->email); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <?php $__errorArgs = ['visiting_tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="purpose_of_visit" class="form-label">Purpose of Visit <span class="text-danger">*</span></label>
                                                <select class="form-select <?php $__errorArgs = ['purpose_of_visit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                        id="purpose_of_visit" name="purpose_of_visit" required>
                                                    <option value="">Select Purpose</option>
                                                    <option value="Personal Visit" <?php echo e(old('purpose_of_visit') == 'Personal Visit' ? 'selected' : ''); ?>>Personal Visit</option>
                                                    <option value="Business Meeting" <?php echo e(old('purpose_of_visit') == 'Business Meeting' ? 'selected' : ''); ?>>Business Meeting</option>
                                                    <option value="Maintenance" <?php echo e(old('purpose_of_visit') == 'Maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                                                    <option value="Delivery" <?php echo e(old('purpose_of_visit') == 'Delivery' ? 'selected' : ''); ?>>Delivery</option>
                                                    <option value="Inspection" <?php echo e(old('purpose_of_visit') == 'Inspection' ? 'selected' : ''); ?>>Inspection</option>
                                                    <option value="Emergency" <?php echo e(old('purpose_of_visit') == 'Emergency' ? 'selected' : ''); ?>>Emergency</option>
                                                    <option value="Other" <?php echo e(old('purpose_of_visit') == 'Other' ? 'selected' : ''); ?>>Other</option>
                                                </select>
                                                <?php $__errorArgs = ['purpose_of_visit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Additional Notes</label>
                                        <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                  id="notes" name="notes" rows="3" 
                                                  placeholder="Any additional information about the visit..."><?php echo e(old('notes')); ?></textarea>
                                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-time-line me-2"></i>Check-in Details
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Check-in Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="ri-calendar-line"></i>
                                            </span>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo e(now()->format('M d, Y H:i')); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Checked in by</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="ri-user-line"></i>
                                            </span>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo e(auth()->user()->name); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="ri-information-line me-2"></i>
                                        <strong>Security Notice:</strong> All visitors are required to provide valid identification and will be logged for security purposes.
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Visitors -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ri-history-line me-2"></i>Recent Visitors
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <!-- This would be populated with recent visitors -->
                                        <div class="text-center text-muted py-3">
                                            <i class="ri-user-line fs-1 mb-2"></i>
                                            <p class="mb-0">Recent visitors will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?php echo e(route('visitors.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In Visitor
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('visiting_unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = <?php echo json_encode($properties, 15, 512) ?>;
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Form validation
    const form = document.getElementById('checkInForm');
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        const phone = document.getElementById('phone').value;
        const idType = document.getElementById('identification_type').value;
        const idNumber = document.getElementById('identification_number').value;
        const propertyId = document.getElementById('property_id').value;
        const purpose = document.getElementById('purpose_of_visit').value;

        if (!name || !phone || !idType || !idNumber || !propertyId || !purpose) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Show confirmation
        Swal.fire({
            title: 'Check In Visitor',
            html: `Are you sure you want to check in:<br><br><strong>${name}</strong><br>Phone: ${phone}`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, Check In',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (!result.isConfirmed) {
                e.preventDefault();
            }
        });
    });

    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        this.value = value;
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/visitors/create.blade.php ENDPATH**/ ?>