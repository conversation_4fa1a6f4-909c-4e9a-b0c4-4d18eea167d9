<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use App\Traits\HasObfuscatedRoutes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasObfuscatedRoutes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'city',
        'state',
        'zip_code',
        'user_type',
        'is_active',
        'created_by',
        'profile_photo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];

    // Property Management Relationships
    public function ownedProperties(): HasMany
    {
        return $this->hasMany(Property::class, 'owner_id');
    }

    public function managedProperties(): HasMany
    {
        return $this->hasMany(Property::class, 'manager_id');
    }

    public function rentedUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'tenant_id');
    }

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'complainant_id');
    }

    public function assignedComplaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'assigned_to');
    }

    // Helper methods for role checking
    public function isPropertyOwner(): bool
    {
        return $this->hasRole('property_owner');
    }

    public function isPropertyManager(): bool
    {
        return $this->hasRole('property_manager');
    }

    public function isTenant(): bool
    {
        return $this->hasRole('tenant');
    }

    public function isMaintenanceStaff(): bool
    {
        return $this->hasRole('maintenance_staff');
    }

    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    // User status methods
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    public function toggleStatus(): bool
    {
        return $this->update(['is_active' => !$this->is_active]);
    }

    // Check if user can create other users
    public function canCreateUsers(): bool
    {
        return $this->hasAnyRole(['admin', 'property_owner']);
    }

    // Get allowed user types this user can create
    public function getAllowedUserTypes(): array
    {
        if ($this->hasRole('admin')) {
            // Admin can create all user types
            return ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff'];
        }

        if ($this->hasRole('property_owner')) {
            // Property owner can create staff for their condominium
            return ['tenant', 'receptionist', 'maintenance_staff'];
        }

        return [];
    }

    // Get allowed roles this user can assign
    public function getAllowedRoles(): array
    {
        if ($this->hasRole('admin')) {
            // Admin can assign all roles
            return ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff'];
        }

        if ($this->hasRole('property_owner')) {
            // Property owner can assign staff roles for their condominium
            return ['tenant', 'receptionist', 'maintenance_staff'];
        }

        return [];
    }

    // Check if this user can be managed by the given user
    public function canBeManagedBy(User $manager): bool
    {
        // Admin can manage all users
        if ($manager->hasRole('admin')) {
            return true;
        }

        // Property owner can manage users they created
        if ($manager->hasRole('property_owner')) {
            return $this->created_by === $manager->id &&
                   $this->hasAnyRole(['tenant', 'receptionist', 'maintenance_staff']);
        }

        return false;
    }

    // Relationship: User who created this user (Property Owner)
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relationship: Users created by this user (Employees/Staff)
    public function employees()
    {
        return $this->hasMany(User::class, 'created_by');
    }

    // Get employees grouped by role
    public function getEmployeesByRole()
    {
        return $this->employees()->with('roles')->get()->groupBy(function ($user) {
            return $user->roles->first()->name ?? 'no_role';
        });
    }

    // Check if user is an employee of another user
    public function isEmployeeOf(User $owner): bool
    {
        return $this->created_by === $owner->id;
    }

    // Get the property owner this user belongs to
    public function getPropertyOwner()
    {
        if ($this->hasRole('property_owner') || $this->hasRole('admin')) {
            return $this;
        }

        return $this->creator;
    }

    // Profile photo methods
    public function getProfilePhotoUrlAttribute()
    {
        if ($this->profile_photo && file_exists(public_path('storage/' . $this->profile_photo))) {
            return asset('storage/' . $this->profile_photo);
        }

        // Return default avatar based on user's initials
        return $this->getDefaultAvatarUrl();
    }

    public function getDefaultAvatarUrl()
    {
        $initials = $this->getInitials();
        $backgroundColor = $this->getAvatarBackgroundColor();

        // Generate a simple avatar URL using UI Avatars service
        return "https://ui-avatars.com/api/?name=" . urlencode($initials) .
               "&background=" . $backgroundColor .
               "&color=ffffff&size=200&font-size=0.6&bold=true";
    }

    public function getInitials()
    {
        $words = explode(' ', trim($this->name));
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        }
        return strtoupper(substr($this->name, 0, 2));
    }

    public function getAvatarBackgroundColor()
    {
        // Generate a consistent color based on user ID
        $colors = ['007bff', '28a745', 'dc3545', 'ffc107', '17a2b8', '6f42c1', 'fd7e14', '20c997'];
        return $colors[$this->id % count($colors)];
    }

    public function hasProfilePhoto()
    {
        return !empty($this->profile_photo) && file_exists(public_path('storage/' . $this->profile_photo));
    }
}
