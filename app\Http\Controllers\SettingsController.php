<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Only admin can access settings
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            $userRoles = $user->roles->pluck('name')->toArray();
            
            if (!in_array('admin', $userRoles)) {
                abort(403, 'Unauthorized access to system settings.');
            }
            
            return $next($request);
        });
    }

    /**
     * Display system settings
     */
    public function index()
    {
        $settings = Setting::getAllGrouped();
        
        // Initialize default settings if they don't exist
        $this->initializeDefaultSettings();

        // Refresh settings after initialization
        $settings = Setting::getAllGrouped();

        return view('settings.index', compact('settings'));
    }

    /**
     * Update system settings
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|max:255',
            'company_phone' => 'nullable|string|max:20',
            'company_address' => 'nullable|string|max:500',
            'company_website' => 'nullable|url|max:255',
            'timezone' => 'required|string|max:50',
            'date_format' => 'required|string|max:20',
            'currency' => 'required|string|max:10',
            'mail_driver' => 'required|string|in:smtp,sendmail,mailgun,ses,postmark',
            'mail_host' => 'nullable|string|max:255',
            'mail_port' => 'nullable|integer|min:1|max:65535',
            'mail_username' => 'nullable|string|max:255',
            'mail_password' => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|in:tls,ssl',
            'mail_from_address' => 'nullable|email|max:255',
            'mail_from_name' => 'nullable|string|max:255',
            'backup_enabled' => 'boolean',
            'backup_frequency' => 'required|string|in:daily,weekly,monthly',
            'backup_retention_days' => 'required|integer|min:1|max:365',
        ]);

        // Update company settings
        Setting::set('company_name', $validated['company_name'], 'string', 'company', 'Company name');
        Setting::set('company_email', $validated['company_email'], 'string', 'company', 'Company email');
        Setting::set('company_phone', $validated['company_phone'], 'string', 'company', 'Company phone');
        Setting::set('company_address', $validated['company_address'], 'string', 'company', 'Company address');
        Setting::set('company_website', $validated['company_website'], 'string', 'company', 'Company website');

        // Update system settings
        Setting::set('timezone', $validated['timezone'], 'string', 'system', 'System timezone');
        Setting::set('date_format', $validated['date_format'], 'string', 'system', 'Date format');
        Setting::set('currency', $validated['currency'], 'string', 'system', 'System currency');

        // Update email settings
        Setting::set('mail_driver', $validated['mail_driver'], 'string', 'email', 'Mail driver');
        Setting::set('mail_host', $validated['mail_host'], 'string', 'email', 'Mail host');
        Setting::set('mail_port', $validated['mail_port'], 'integer', 'email', 'Mail port');
        Setting::set('mail_username', $validated['mail_username'], 'string', 'email', 'Mail username');
        Setting::set('mail_password', $validated['mail_password'], 'string', 'email', 'Mail password');
        Setting::set('mail_encryption', $validated['mail_encryption'], 'string', 'email', 'Mail encryption');
        Setting::set('mail_from_address', $validated['mail_from_address'], 'string', 'email', 'Mail from address');
        Setting::set('mail_from_name', $validated['mail_from_name'], 'string', 'email', 'Mail from name');

        // Update backup settings
        Setting::set('backup_enabled', $validated['backup_enabled'], 'boolean', 'backup', 'Backup enabled');
        Setting::set('backup_frequency', $validated['backup_frequency'], 'string', 'backup', 'Backup frequency');
        Setting::set('backup_retention_days', $validated['backup_retention_days'], 'integer', 'backup', 'Backup retention days');

        return back()->with('success', 'Settings updated successfully!');
    }

    /**
     * Upload company logo
     */
    public function uploadLogo(Request $request)
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'type' => 'required|in:header,login'
        ]);

        $type = $request->type;
        $file = $request->file('logo');
        
        // Delete old logo if exists
        $oldLogo = Setting::get("{$type}_logo");
        if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
            Storage::disk('public')->delete($oldLogo);
        }

        // Store new logo
        $path = $file->store('logos', 'public');
        
        // Update setting
        Setting::set("{$type}_logo", $path, 'string', 'company', ucfirst($type) . ' logo path');

        // Return JSON response for AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => ucfirst($type) . ' logo uploaded successfully!',
                'logo_url' => asset('storage/' . $path),
                'logo_path' => $path,
                'type' => $type
            ]);
        }

        return back()->with('success', ucfirst($type) . ' logo uploaded successfully!');
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Update mail configuration temporarily
            $this->updateMailConfig();

            // Send test email
            Mail::raw('This is a test email from your Property Management System.', function ($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('Test Email - Property Management System');
            });

            return response()->json(['success' => true, 'message' => 'Test email sent successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to send test email: ' . $e->getMessage()]);
        }
    }

    /**
     * Create database backup
     */
    public function createBackup()
    {
        try {
            Artisan::call('backup:run');
            
            return response()->json(['success' => true, 'message' => 'Backup created successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to create backup: ' . $e->getMessage()]);
        }
    }

    /**
     * List available backups
     */
    public function listBackups()
    {
        $backupDisk = Storage::disk('local');
        $backups = [];

        if ($backupDisk->exists('backups')) {
            $files = $backupDisk->files('backups');
            
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'zip') {
                    $backups[] = [
                        'name' => basename($file),
                        'path' => $file,
                        'size' => $this->formatBytes($backupDisk->size($file)),
                        'date' => date('Y-m-d H:i:s', $backupDisk->lastModified($file))
                    ];
                }
            }
        }

        return response()->json($backups);
    }

    /**
     * Download backup file
     */
    public function downloadBackup($filename)
    {
        $path = "backups/{$filename}";
        
        if (!Storage::disk('local')->exists($path)) {
            abort(404, 'Backup file not found.');
        }

        return Storage::disk('local')->download($path);
    }

    /**
     * Delete backup file
     */
    public function deleteBackup($filename)
    {
        $path = "backups/{$filename}";
        
        if (Storage::disk('local')->exists($path)) {
            Storage::disk('local')->delete($path);
            return response()->json(['success' => true, 'message' => 'Backup deleted successfully!']);
        }

        return response()->json(['success' => false, 'message' => 'Backup file not found.']);
    }

    /**
     * Initialize default settings
     */
    private function initializeDefaultSettings()
    {
        $defaults = [
            // Company settings
            'company_name' => ['value' => 'Property Management System', 'type' => 'string', 'group' => 'company'],
            'company_email' => ['value' => '<EMAIL>', 'type' => 'string', 'group' => 'company'],
            'company_phone' => ['value' => '', 'type' => 'string', 'group' => 'company'],
            'company_address' => ['value' => '', 'type' => 'string', 'group' => 'company'],
            'company_website' => ['value' => '', 'type' => 'string', 'group' => 'company'],
            'header_logo' => ['value' => '', 'type' => 'string', 'group' => 'company'],
            'login_logo' => ['value' => '', 'type' => 'string', 'group' => 'company'],
            
            // System settings
            'timezone' => ['value' => 'UTC', 'type' => 'string', 'group' => 'system'],
            'date_format' => ['value' => 'Y-m-d', 'type' => 'string', 'group' => 'system'],
            'currency' => ['value' => 'USD', 'type' => 'string', 'group' => 'system'],
            
            // Email settings
            'mail_driver' => ['value' => 'smtp', 'type' => 'string', 'group' => 'email'],
            'mail_host' => ['value' => '', 'type' => 'string', 'group' => 'email'],
            'mail_port' => ['value' => 587, 'type' => 'integer', 'group' => 'email'],
            'mail_username' => ['value' => '', 'type' => 'string', 'group' => 'email'],
            'mail_password' => ['value' => '', 'type' => 'string', 'group' => 'email'],
            'mail_encryption' => ['value' => 'tls', 'type' => 'string', 'group' => 'email'],
            'mail_from_address' => ['value' => '', 'type' => 'string', 'group' => 'email'],
            'mail_from_name' => ['value' => '', 'type' => 'string', 'group' => 'email'],
            
            // Backup settings
            'backup_enabled' => ['value' => false, 'type' => 'boolean', 'group' => 'backup'],
            'backup_frequency' => ['value' => 'weekly', 'type' => 'string', 'group' => 'backup'],
            'backup_retention_days' => ['value' => 30, 'type' => 'integer', 'group' => 'backup'],
        ];

        foreach ($defaults as $key => $config) {
            if (!Setting::where('key', $key)->exists()) {
                Setting::set($key, $config['value'], $config['type'], $config['group']);
            }
        }
    }

    /**
     * Update mail configuration
     */
    private function updateMailConfig()
    {
        Config::set('mail.default', Setting::get('mail_driver', 'smtp'));
        Config::set('mail.mailers.smtp.host', Setting::get('mail_host'));
        Config::set('mail.mailers.smtp.port', Setting::get('mail_port', 587));
        Config::set('mail.mailers.smtp.username', Setting::get('mail_username'));
        Config::set('mail.mailers.smtp.password', Setting::get('mail_password'));
        Config::set('mail.mailers.smtp.encryption', Setting::get('mail_encryption', 'tls'));
        Config::set('mail.from.address', Setting::get('mail_from_address'));
        Config::set('mail.from.name', Setting::get('mail_from_name'));
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
