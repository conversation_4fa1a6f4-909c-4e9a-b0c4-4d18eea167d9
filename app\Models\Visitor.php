<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Visitor extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'identification_number',
        'identification_type',
        'purpose_of_visit',
        'visiting_unit_id',
        'visiting_tenant_id',
        'property_id',
        'check_in_time',
        'check_out_time',
        'checked_in_by',
        'checked_out_by',
        'status',
        'notes',
        'vehicle_number',
        'emergency_contact',
    ];

    protected $casts = [
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
    ];

    // Relationships
    public function visitingUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'visiting_unit_id');
    }

    public function visitingTenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'visiting_tenant_id');
    }

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function checkedInBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_in_by');
    }

    public function checkedOutBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_out_by');
    }

    // Scopes
    public function scopeCheckedIn($query)
    {
        return $query->whereNotNull('check_in_time')->whereNull('check_out_time');
    }

    public function scopeCheckedOut($query)
    {
        return $query->whereNotNull('check_out_time');
    }

    public function scopeByProperty($query, $propertyId)
    {
        return $query->where('property_id', $propertyId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('check_in_time', today());
    }

    // Accessors
    public function getIsCheckedInAttribute(): bool
    {
        return !is_null($this->check_in_time) && is_null($this->check_out_time);
    }

    public function getVisitDurationAttribute(): ?string
    {
        if (!$this->check_in_time) {
            return null;
        }

        $endTime = $this->check_out_time ?? now();
        $duration = $this->check_in_time->diff($endTime);

        return $duration->format('%h hours %i minutes');
    }
}
