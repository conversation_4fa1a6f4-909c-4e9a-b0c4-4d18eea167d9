/* Schedule Calendar CSS */
.schedule-calendar .fc-theme-standard .fc-scrollgrid {
  border: 0;
}

.schedule-calendar .fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 0.5rem;
}

.schedule-calendar .fc .fc-button {
  border: 1px solid transparent;
  padding: 0.4em 0;
  font-size: 15px;
}

.schedule-calendar .fc .fc-non-business {
  background: transparent;
}

.schedule-calendar .fc .fc-bg-event {
  background: #e2f1fe !important;
  opacity: 1;
}

.schedule-calendar .fc .fc-button-primary {
  background-color: transparent;
  border-color: transparent;
  color: #566fe2;
}

.schedule-calendar .fc .fc-toolbar-title {
  font-size: 0.85em;
  margin: 0;
}

.schedule-calendar .fc .fc-col-header-cell-cushion {
  padding: 4px 4px;
  color: #242424;
  font-weight: 600;
}

.schedule-calendar .fc .fc-button:not(:disabled),
.schedule-calendar .fc a[data-navlink],
.schedule-calendar .fc-event.fc-event-draggable,
.schedule-calendar .fc-event[href] {
  color: #566fe2;
}

.schedule-calendar .fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  color: #566fe2;
  background-color: transparent;
  border-color: transparent;
}

.schedule-calendar .fc-theme-standard td,
.fc-theme-standard th {
  border: 1px solid #dbe3ea;
  font-size: 15px;
}

.schedule-calendar .fc-direction-ltr .fc-daygrid-event.fc-event-end,
.schedule-calendar .fc-direction-rtl .fc-daygrid-event.fc-event-start {
  width: 45px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px !important;
  font-size: 15px;
  margin: 5px;
  outline: 1px solid #c6d2dc;
  outline-offset: 2px;
}

.schedule-calendar .fc-h-event {
  background: #566fe2;
  border: 0;
}

.fc-theme-standard .fc-scrollgrid {
  border: 1px solid #dbe3ea;
  border-right: 0;
  border-bottom: 0;
}

.fc-theme-standard .fc-list-day-cushion {
  background-color: #f2f4f9;
}

.fc-theme-standard .fc-list {
  border: 1px solid #dedeef;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: 1px solid #dbe3ea;
}

.fc .fc-list-event:hover td {
  background-color: #e9ecf4;
}

.fc .fc-list-event-dot {
  border: 7px solid #566fe2;
}

.fc-daygrid-dot-event .fc-event-title {
  font-weight: 600;
}

.fc-daygrid-event-dot {
  border: 7px solid #566fe2;
}

.fc-daygrid-dot-event:hover {
  background-color: #e9ecf4;
}

.fc-daygrid-event {
  font-size: 13px;
  padding: 6px 0;
}

.fc-theme-standard .fc-popover-header {
  background: #566fe2;
  color: #fff;
  font-size: 13px;
  padding: 6px 8px;
}

.fc .fc-toolbar-title {
  font-size: 14px;
}

.fc .fc-button-primary {
  border-color: #f2f4f9;
  background-color: #f2f4f9;
  color: #272727;
}

.fc .fc-button-primary:hover {
  border-color: #e9e7ff;
  background-color: #e9e7ff;
  color: #566fe2;
}

.fc .fc-button-primary:not(:disabled).fc-button-active {
  border-color: #566fe2;
  background-color: #566fe2;
}

.fc .fc-col-header-cell-cushion {
  padding: 6px 4px;
  color: #828992;
}

.fc .fc-list-empty {
  border: 0 solid #dedeef;
  background: #f4f4fb;
}

/* Draggable */
#external-events {
  padding: 1rem;
  border: 1px solid #dedeef;
  background: #f4f4fb;
  text-align: left;
  margin-bottom: 20px;
  border-radius: 3px;
}

#external-events h4 {
  font-size: 0.9rem;
  margin: 0 0 0.7rem 0;
}

#external-events .fc-event {
  margin: 3px 2px;
  cursor: move;
}

#external-events p {
  margin: 0.5rem 0 0 0;
  font-size: 15px;
}

#external-events p input {
  margin: 0;
  vertical-align: middle;
}

#external-events label {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 400;
  vertical-align: middle;
}

.fc .fc-toolbar.fc-header-toolbar {
  flex-wrap: wrap;
}

.fc .fc-toolbar.fc-header-toolbar .fc-toolbar-chunk {
  margin: 2px 0;
}

.fc-h-event {
  border: 1px solid #566fe2;
  background: #566fe2;
  padding: 5px 8px;
  font-size: 15px;
  border-radius: 2px;
}

.fc-v-event {
  border: 1px solid #566fe2;
  background: #566fe2;
}

.fc table {
  font-size: 14px;
  color: #566fe2;
}