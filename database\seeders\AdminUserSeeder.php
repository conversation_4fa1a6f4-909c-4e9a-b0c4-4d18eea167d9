<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('12345678'),
            'phone' => '+1234567890',
            'address' => '123 Admin Street',
            'city' => 'Admin City',
            'state' => 'Admin State',
            'zip_code' => '12345',
            'user_type' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Assign admin role
        $admin->assignRole('admin');

        // Create sample users for other roles

        // Property Owner
        $owner = User::create([
            'name' => 'John Property Owner',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+1234567891',
            'user_type' => 'owner',
            'email_verified_at' => now(),
        ]);
        $owner->assignRole('property_owner');

        // Tenant
        $tenant = User::create([
            'name' => 'Jane Tenant',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+1234567892',
            'user_type' => 'tenant',
            'email_verified_at' => now(),
        ]);
        $tenant->assignRole('tenant');

        // Receptionist
        $receptionist = User::create([
            'name' => 'Mary Receptionist',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+1234567893',
            'user_type' => 'receptionist',
            'email_verified_at' => now(),
        ]);
        $receptionist->assignRole('receptionist');

        // Maintenance Staff
        $maintenance = User::create([
            'name' => 'Bob Maintenance',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+1234567894',
            'user_type' => 'maintenance_staff',
            'email_verified_at' => now(),
        ]);
        $maintenance->assignRole('maintenance_staff');
    }
}
