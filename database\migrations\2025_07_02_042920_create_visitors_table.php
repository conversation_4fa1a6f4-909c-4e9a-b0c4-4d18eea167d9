<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visitors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('identification_number')->nullable();
            $table->string('identification_type')->nullable(); // passport, driver_license, national_id
            $table->text('purpose_of_visit')->nullable();
            $table->foreignId('visiting_unit_id')->nullable()->constrained('units')->onDelete('set null');
            $table->foreignId('visiting_tenant_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->timestamp('check_in_time')->nullable();
            $table->timestamp('check_out_time')->nullable();
            $table->foreignId('checked_in_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('checked_out_by')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('status', ['checked_in', 'checked_out', 'cancelled'])->default('checked_in');
            $table->text('notes')->nullable();
            $table->string('vehicle_number')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visitors');
    }
};
