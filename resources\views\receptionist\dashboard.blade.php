@extends('layouts.base')

@section('content')
<!-- Welcome Section -->
<div class="row gx-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">Welcome to Receptionist Dashboard, {{ auth()->user()->name }}!</h4>
                        <p class="text-muted mb-0">
                            Manage visitors, handle check-ins, and maintain visitor records.
                        </p>
                    </div>
                    <div>
                        <form action="{{ route('logout') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="ri-logout-box-line me-1"></i>Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Receptionist Features -->
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Receptionist Features</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-user-add-line fs-1 text-primary mb-3"></i>
                                <h6>Visitor Check-in</h6>
                                <p class="text-muted small">Register new visitors and manage check-ins</p>
                                <button class="btn btn-outline-primary btn-sm">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-history-line fs-1 text-success mb-3"></i>
                                <h6>Visitor History</h6>
                                <p class="text-muted small">View and search visitor records</p>
                                <button class="btn btn-outline-success btn-sm">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ri-checkbox-line fs-1 text-info mb-3"></i>
                                <h6>Checklist Management</h6>
                                <p class="text-muted small">Manage daily checklists and procedures</p>
                                <button class="btn btn-outline-info btn-sm">Coming Soon</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
