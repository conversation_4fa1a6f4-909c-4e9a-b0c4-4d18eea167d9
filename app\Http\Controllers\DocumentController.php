<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Document::with(['property', 'unit', 'tenant', 'uploadedBy']);
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Document::whereIn('property_id', $propertyIds)->with(['property', 'unit', 'tenant', 'uploadedBy']);
        } else {
            // Tenants see only their documents
            $query = Document::where('tenant_id', $user->id)->with(['property', 'unit', 'uploadedBy']);
        }

        // Apply filters
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        if ($request->filled('property_id')) {
            $query->byProperty($request->property_id);
        }

        if ($request->filled('status')) {
            switch ($request->status) {
                case 'expired':
                    $query->expired();
                    break;
                case 'expiring_soon':
                    $query->expiringSoon(30);
                    break;
                case 'active':
                    $query->where(function ($q) {
                        $q->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>', now());
                    });
                    break;
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('file_name', 'like', "%{$search}%");
            });
        }

        $documents = $query->latest()->paginate(15);

        // Calculate statistics
        $stats = [
            'total' => $query->count(),
            'expired' => $query->expired()->count(),
            'expiring_soon' => $query->expiringSoon(30)->count(),
            'contracts' => $query->byType('contract')->count(),
            'insurance' => $query->byType('insurance')->count(),
        ];

        // Get filter options
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties;
        } else {
            $properties = collect();
        }

        $documentTypes = Document::distinct()->pluck('type');

        return view('documents.index', compact('documents', 'properties', 'documentTypes', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Document::class);
        
        $user = Auth::user();
        
        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            $properties = collect();
        }

        // Get tenants
        $tenants = User::whereHas('roles', function ($q) {
            $q->where('name', 'tenant');
        })->where('is_active', true)->get();

        $selectedProperty = $request->property_id ? Property::find($request->property_id) : null;
        $selectedUnit = $request->unit_id ? Unit::find($request->unit_id) : null;

        return view('documents.create', compact('properties', 'tenants', 'selectedProperty', 'selectedUnit'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Document::class);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:contract,insurance,fire_certificate,lease_agreement,tenant_id,property_deed,permit,inspection_report,other',
            'property_id' => 'nullable|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'tenant_id' => 'nullable|exists:users,id',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:issue_date',
            'alert_days_before' => 'nullable|integer|min:1|max:365',
            'notes' => 'nullable|string',
            'file' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
        ]);

        // Handle file upload
        $file = $request->file('file');
        $fileName = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
        $filePath = $file->storeAs('documents', $fileName, 'public');

        $validated['file_name'] = $file->getClientOriginalName();
        $validated['file_path'] = $filePath;
        $validated['file_type'] = $file->getClientOriginalExtension();
        $validated['file_size'] = $file->getSize();
        $validated['uploaded_by'] = Auth::id();
        $validated['alert_days_before'] = $validated['alert_days_before'] ?? 30;

        $document = Document::create($validated);

        return redirect()->route('documents.show', $document)
            ->with('success', 'Document uploaded successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Document $document)
    {
        $this->authorize('view', $document);

        $document->load(['property', 'unit', 'tenant', 'uploadedBy']);

        return view('documents.show', compact('document'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Document $document)
    {
        $this->authorize('update', $document);
        
        $user = Auth::user();
        
        // Get properties based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            $properties = collect();
        }

        // Get tenants
        $tenants = User::whereHas('roles', function ($q) {
            $q->where('name', 'tenant');
        })->where('is_active', true)->get();

        return view('documents.edit', compact('document', 'properties', 'tenants'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Document $document)
    {
        $this->authorize('update', $document);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:contract,insurance,fire_certificate,lease_agreement,tenant_id,property_deed,permit,inspection_report,other',
            'property_id' => 'nullable|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'tenant_id' => 'nullable|exists:users,id',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:issue_date',
            'alert_days_before' => 'nullable|integer|min:1|max:365',
            'notes' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
        ]);

        // Handle file upload if new file is provided
        if ($request->hasFile('file')) {
            // Delete old file
            $document->deleteFile();

            $file = $request->file('file');
            $fileName = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('documents', $fileName, 'public');

            $validated['file_name'] = $file->getClientOriginalName();
            $validated['file_path'] = $filePath;
            $validated['file_type'] = $file->getClientOriginalExtension();
            $validated['file_size'] = $file->getSize();
        }

        $validated['alert_days_before'] = $validated['alert_days_before'] ?? 30;

        // Reset expiry alert if expiry date changed
        if ($document->expiry_date != $validated['expiry_date']) {
            $validated['expiry_alert_sent'] = false;
        }

        $document->update($validated);

        return redirect()->route('documents.show', $document)
            ->with('success', 'Document updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Document $document)
    {
        $this->authorize('delete', $document);

        // Delete file from storage
        $document->deleteFile();

        $document->delete();

        return redirect()->route('documents.index')
            ->with('success', 'Document deleted successfully!');
    }

    /**
     * Download document file
     */
    public function download(Document $document)
    {
        $this->authorize('view', $document);

        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'File not found');
        }

        return Storage::disk('public')->download($document->file_path, $document->file_name);
    }

    /**
     * View document file in browser
     */
    public function view(Document $document)
    {
        $this->authorize('view', $document);

        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'File not found');
        }

        $filePath = storage_path('app/public/' . $document->file_path);
        $mimeType = mime_content_type($filePath);

        return response()->file($filePath, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $document->file_name . '"'
        ]);
    }

    /**
     * Get expiring documents
     */
    public function expiring(Request $request)
    {
        $user = Auth::user();
        $days = $request->get('days', 30);

        // Build base query based on user role
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('admin', $userRoles)) {
            $baseQuery = Document::query();
        } elseif (in_array('property_owner', $userRoles)) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $baseQuery = Document::whereIn('property_id', $propertyIds);
        } else {
            $baseQuery = Document::where('tenant_id', $user->id);
        }

        // Get expired documents
        $expiredDocuments = $baseQuery->expired()
            ->with(['property', 'unit', 'tenant'])
            ->orderBy('expiry_date', 'asc')
            ->get();

        // Get expiring soon documents
        $expiringSoonDocuments = $baseQuery->expiringSoon($days)
            ->with(['property', 'unit', 'tenant'])
            ->orderBy('expiry_date', 'asc')
            ->get();

        // Calculate statistics
        $expiredCount = $expiredDocuments->count();
        $expiringSoonCount = $expiringSoonDocuments->count();
        $totalCount = $baseQuery->count();

        // Get document type statistics
        $typeStats = [
            'insurance' => $baseQuery->where('type', 'insurance')->count(),
            'fire_certificate' => $baseQuery->where('type', 'fire_certificate')->count(),
            'contract' => $baseQuery->where('type', 'contract')->count(),
            'permit' => $baseQuery->where('type', 'permit')->count(),
        ];

        return view('documents.expiring', compact(
            'expiredDocuments',
            'expiringSoonDocuments',
            'expiredCount',
            'expiringSoonCount',
            'totalCount',
            'typeStats',
            'days'
        ));
    }

    /**
     * Check and update expired documents
     */
    public function checkExpired()
    {
        $expiredDocuments = Document::getExpiredDocuments();
        
        foreach ($expiredDocuments as $document) {
            $document->checkExpiry();
        }

        return response()->json([
            'success' => true,
            'expired_count' => $expiredDocuments->count(),
        ]);
    }

    /**
     * Send expiry alerts
     */
    public function sendExpiryAlerts()
    {
        $documents = Document::getExpiringDocuments(30);
        $alertsSent = 0;

        foreach ($documents as $document) {
            if ($document->shouldSendExpiryAlert()) {
                // Create notification for property owner
                if ($document->property && $document->property->owner) {
                    \App\Models\Notification::createDocumentExpiryAlert($document, $document->property->owner);
                }

                // Create notification for uploader
                if ($document->uploadedBy) {
                    \App\Models\Notification::createDocumentExpiryAlert($document, $document->uploadedBy);
                }

                $document->markExpiryAlertSent();
                $alertsSent++;
            }
        }

        return response()->json([
            'success' => true,
            'alerts_sent' => $alertsSent,
        ]);
    }
}
