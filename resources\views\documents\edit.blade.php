@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-edit-line text-primary me-2"></i>Edit Document
                        </h4>
                        <p class="text-muted mb-0">
                            Update document information and settings
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('documents.show', $document) }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Document
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('documents.update', $document) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Document Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $document->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3">{{ old('description', $document->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Current File -->
                            <div class="mb-3">
                                <label class="form-label">Current File</label>
                                <div class="d-flex align-items-center p-3 bg-light rounded">
                                    <div class="me-3">
                                        @if(in_array($document->file_type, ['jpg', 'jpeg', 'png']))
                                            <i class="ri-image-line fs-4 text-info"></i>
                                        @elseif($document->file_type === 'pdf')
                                            <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                        @elseif(in_array($document->file_type, ['doc', 'docx']))
                                            <i class="ri-file-word-line fs-4 text-primary"></i>
                                        @else
                                            <i class="ri-file-line fs-4 text-muted"></i>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $document->file_name }}</h6>
                                        <small class="text-muted">{{ $document->file_size_formatted }} • {{ strtoupper($document->file_type) }}</small>
                                    </div>
                                    <div>
                                        <a href="{{ route('documents.download', $document) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="ri-download-line me-1"></i>Download
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Replace File -->
                            <div class="mb-3">
                                <label for="file" class="form-label">Replace File (Optional)</label>
                                <input type="file" class="form-control @error('file') is-invalid @enderror" 
                                       id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <div class="form-text">Leave empty to keep current file. Supported formats: PDF, DOC, DOCX, JPG, PNG (Max: 10MB)</div>
                                @error('file')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Property and Unit -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select @error('property_id') is-invalid @enderror" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            @foreach($properties as $property)
                                                <option value="{{ $property->id }}" 
                                                        {{ old('property_id', $document->property_id) == $property->id ? 'selected' : '' }}>
                                                    {{ $property->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('property_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select @error('unit_id') is-invalid @enderror" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                            @if($document->property)
                                                @foreach($document->property->units as $unit)
                                                    <option value="{{ $unit->id }}" 
                                                            {{ old('unit_id', $document->unit_id) == $unit->id ? 'selected' : '' }}>
                                                        Unit {{ $unit->unit_number }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                        @error('unit_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Tenant -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Related Tenant</label>
                                <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id">
                                    <option value="">Select Tenant (Optional)</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" 
                                                {{ old('tenant_id', $document->tenant_id) == $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name }} ({{ $tenant->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes', $document->notes) }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Document Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Document Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="contract" {{ old('type', $document->type) == 'contract' ? 'selected' : '' }}>Contract</option>
                                    <option value="insurance" {{ old('type', $document->type) == 'insurance' ? 'selected' : '' }}>Insurance</option>
                                    <option value="fire_certificate" {{ old('type', $document->type) == 'fire_certificate' ? 'selected' : '' }}>Fire Certificate</option>
                                    <option value="lease_agreement" {{ old('type', $document->type) == 'lease_agreement' ? 'selected' : '' }}>Lease Agreement</option>
                                    <option value="tenant_id" {{ old('type', $document->type) == 'tenant_id' ? 'selected' : '' }}>Tenant ID</option>
                                    <option value="property_deed" {{ old('type', $document->type) == 'property_deed' ? 'selected' : '' }}>Property Deed</option>
                                    <option value="permit" {{ old('type', $document->type) == 'permit' ? 'selected' : '' }}>Permit</option>
                                    <option value="inspection_report" {{ old('type', $document->type) == 'inspection_report' ? 'selected' : '' }}>Inspection Report</option>
                                    <option value="other" {{ old('type', $document->type) == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Issue Date -->
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date</label>
                                <input type="date" class="form-control @error('issue_date') is-invalid @enderror" 
                                       id="issue_date" name="issue_date" 
                                       value="{{ old('issue_date', $document->issue_date ? $document->issue_date->format('Y-m-d') : '') }}">
                                @error('issue_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Expiry Date -->
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" 
                                       id="expiry_date" name="expiry_date" 
                                       value="{{ old('expiry_date', $document->expiry_date ? $document->expiry_date->format('Y-m-d') : '') }}">
                                @error('expiry_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Alert Days Before -->
                            <div class="mb-3">
                                <label for="alert_days_before" class="form-label">Alert Days Before Expiry</label>
                                <input type="number" class="form-control @error('alert_days_before') is-invalid @enderror" 
                                       id="alert_days_before" name="alert_days_before" 
                                       value="{{ old('alert_days_before', $document->alert_days_before) }}" min="1" max="365">
                                <div class="form-text">Number of days before expiry to send alert</div>
                                @error('alert_days_before')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- File Preview -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">New File Preview</h6>
                                </div>
                                <div class="card-body text-center" id="file-preview">
                                    <i class="ri-file-line fs-1 text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No new file selected</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('documents.show', $document) }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Document
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        
        // Clear unit options
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    });

    // File preview
    const fileInput = document.getElementById('file');
    const filePreview = document.getElementById('file-preview');

    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        
        if (file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
            const fileName = file.name;
            const fileType = file.type;
            
            let icon = 'ri-file-line';
            let iconColor = 'text-muted';
            
            if (fileType.includes('pdf')) {
                icon = 'ri-file-pdf-line';
                iconColor = 'text-danger';
            } else if (fileType.includes('image')) {
                icon = 'ri-image-line';
                iconColor = 'text-info';
            } else if (fileType.includes('word') || fileName.includes('.doc')) {
                icon = 'ri-file-word-line';
                iconColor = 'text-primary';
            }
            
            filePreview.innerHTML = `
                <i class="${icon} fs-1 ${iconColor} mb-2"></i>
                <p class="mb-1"><strong>${fileName}</strong></p>
                <p class="text-muted mb-0">${fileSize} MB</p>
            `;
        } else {
            filePreview.innerHTML = `
                <i class="ri-file-line fs-1 text-muted mb-2"></i>
                <p class="text-muted mb-0">No new file selected</p>
            `;
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const file = document.getElementById('file').files[0];
        const issueDate = document.getElementById('issue_date').value;
        const expiryDate = document.getElementById('expiry_date').value;

        // Check file size
        if (file && file.size > 10 * 1024 * 1024) { // 10MB
            e.preventDefault();
            Swal.fire({
                title: 'File Too Large',
                text: 'Please select a file smaller than 10MB.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Check date logic
        if (issueDate && expiryDate && new Date(issueDate) > new Date(expiryDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Dates',
                text: 'Expiry date must be after issue date.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
@endsection
