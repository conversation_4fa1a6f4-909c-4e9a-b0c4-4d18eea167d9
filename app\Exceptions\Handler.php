<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception)
    {
        // Handle 404 errors based on authentication status
        if ($exception instanceof NotFoundHttpException) {
            if (!auth()->check()) {
                // User is not authenticated, redirect to login
                return redirect()->route('login')->with('error', 'Page not found. Please login to access the system.');
            } else {
                // User is authenticated, show custom 404 page
                return response()->view('errors.404', [
                    'exception' => $exception,
                    'user' => auth()->user()
                ], 404);
            }
        }

        return parent::render($request, $exception);
    }

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }
}
