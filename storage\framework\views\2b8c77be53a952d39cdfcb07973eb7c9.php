<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-feedback-line text-primary me-2"></i>Submit New Complaint
                        </h4>
                        <p class="text-muted mb-0">
                            Report an issue or submit a complaint for resolution
                        </p>
                    </div>
                    <div>
                        <a href="<?php echo e(route('complaints.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Complaints
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('complaints.store')); ?>" method="POST" enctype="multipart/form-data" id="createComplaintForm">
                    <?php echo csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Tenant Selection (for admin/property owners) -->
                            <?php if(auth()->user()->roles->pluck('name')->intersect(['admin', 'property_owner'])->isNotEmpty()): ?>
                                <div class="mb-3">
                                    <label for="tenant_id" class="form-label">Tenant <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="tenant_id" name="tenant_id" required>
                                        <option value="">Select Tenant</option>
                                        <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($tenant->id); ?>" <?php echo e(old('tenant_id') == $tenant->id ? 'selected' : ''); ?>>
                                                <?php echo e($tenant->name); ?> (<?php echo e($tenant->email); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['tenant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Property and Unit Selection -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($property->id); ?>" <?php echo e(old('property_id') == $property->id ? 'selected' : ''); ?>>
                                                    <?php echo e($property->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                        </select>
                                        <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Subject -->
                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="subject" name="subject" value="<?php echo e(old('subject')); ?>" 
                                       placeholder="Brief description of the issue" required>
                                <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="maintenance" <?php echo e(old('category') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                                    <option value="plumbing" <?php echo e(old('category') == 'plumbing' ? 'selected' : ''); ?>>Plumbing</option>
                                    <option value="electrical" <?php echo e(old('category') == 'electrical' ? 'selected' : ''); ?>>Electrical</option>
                                    <option value="heating_cooling" <?php echo e(old('category') == 'heating_cooling' ? 'selected' : ''); ?>>Heating/Cooling</option>
                                    <option value="appliances" <?php echo e(old('category') == 'appliances' ? 'selected' : ''); ?>>Appliances</option>
                                    <option value="pest_control" <?php echo e(old('category') == 'pest_control' ? 'selected' : ''); ?>>Pest Control</option>
                                    <option value="noise" <?php echo e(old('category') == 'noise' ? 'selected' : ''); ?>>Noise Complaint</option>
                                    <option value="security" <?php echo e(old('category') == 'security' ? 'selected' : ''); ?>>Security</option>
                                    <option value="parking" <?php echo e(old('category') == 'parking' ? 'selected' : ''); ?>>Parking</option>
                                    <option value="cleaning" <?php echo e(old('category') == 'cleaning' ? 'selected' : ''); ?>>Cleaning</option>
                                    <option value="neighbor" <?php echo e(old('category') == 'neighbor' ? 'selected' : ''); ?>>Neighbor Issue</option>
                                    <option value="billing" <?php echo e(old('category') == 'billing' ? 'selected' : ''); ?>>Billing</option>
                                    <option value="other" <?php echo e(old('category') == 'other' ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="description" name="description" rows="5" 
                                          placeholder="Please provide detailed information about the issue..." required><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Location Details -->
                            <div class="mb-3">
                                <label for="location" class="form-label">Specific Location</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="location" name="location" value="<?php echo e(old('location')); ?>" 
                                       placeholder="e.g., Kitchen sink, Living room window, Bedroom 2">
                                <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Attachments -->
                            <div class="mb-3">
                                <label for="attachments" class="form-label">Attachments</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="attachments" name="attachments[]" multiple 
                                       accept="image/*,application/pdf,.doc,.docx">
                                <div class="form-text">
                                    You can upload images, PDFs, or documents. Maximum 5 files, 10MB each.
                                </div>
                                <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Priority -->
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="priority" name="priority" required>
                                    <option value="low" <?php echo e(old('priority', 'medium') == 'low' ? 'selected' : ''); ?>>Low</option>
                                    <option value="medium" <?php echo e(old('priority', 'medium') == 'medium' ? 'selected' : ''); ?>>Medium</option>
                                    <option value="high" <?php echo e(old('priority') == 'high' ? 'selected' : ''); ?>>High</option>
                                    <option value="urgent" <?php echo e(old('priority') == 'urgent' ? 'selected' : ''); ?>>Urgent</option>
                                </select>
                                <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">
                                    <small>
                                        <strong>Low:</strong> Non-urgent issues<br>
                                        <strong>Medium:</strong> Standard maintenance<br>
                                        <strong>High:</strong> Affects daily living<br>
                                        <strong>Urgent:</strong> Safety/security issues
                                    </small>
                                </div>
                            </div>

                            <!-- Preferred Contact Method -->
                            <div class="mb-3">
                                <label for="preferred_contact" class="form-label">Preferred Contact Method</label>
                                <select class="form-select <?php $__errorArgs = ['preferred_contact'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="preferred_contact" name="preferred_contact">
                                    <option value="email" <?php echo e(old('preferred_contact', 'email') == 'email' ? 'selected' : ''); ?>>Email</option>
                                    <option value="phone" <?php echo e(old('preferred_contact') == 'phone' ? 'selected' : ''); ?>>Phone</option>
                                    <option value="sms" <?php echo e(old('preferred_contact') == 'sms' ? 'selected' : ''); ?>>SMS</option>
                                    <option value="in_person" <?php echo e(old('preferred_contact') == 'in_person' ? 'selected' : ''); ?>>In Person</option>
                                </select>
                                <?php $__errorArgs = ['preferred_contact'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Access Instructions -->
                            <div class="mb-3">
                                <label for="access_instructions" class="form-label">Access Instructions</label>
                                <textarea class="form-control <?php $__errorArgs = ['access_instructions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="access_instructions" name="access_instructions" rows="3" 
                                          placeholder="Special instructions for accessing the unit..."><?php echo e(old('access_instructions')); ?></textarea>
                                <?php $__errorArgs = ['access_instructions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Availability -->
                            <div class="mb-3">
                                <label for="availability" class="form-label">Availability</label>
                                <textarea class="form-control <?php $__errorArgs = ['availability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="availability" name="availability" rows="3" 
                                          placeholder="When are you available for maintenance visits?"><?php echo e(old('availability')); ?></textarea>
                                <?php $__errorArgs = ['availability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Emergency Contact -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_emergency" name="is_emergency" value="1" 
                                           <?php echo e(old('is_emergency') ? 'checked' : ''); ?>>
                                    <label class="form-check-label text-danger" for="is_emergency">
                                        <strong>This is an emergency</strong>
                                    </label>
                                </div>
                                <div class="form-text">
                                    <small class="text-danger">
                                        Check this box only for urgent issues that require immediate attention 
                                        (safety hazards, water leaks, electrical problems, etc.)
                                    </small>
                                </div>
                            </div>

                            <!-- Anonymous Complaint -->
                            <?php if(auth()->user()->roles->pluck('name')->intersect(['admin', 'property_owner'])->isNotEmpty()): ?>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous" value="1" 
                                               <?php echo e(old('is_anonymous') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_anonymous">
                                            Anonymous complaint
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <small>Hide tenant identity from maintenance staff</small>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?php echo e(route('complaints.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-send-plane-line me-1"></i>Submit Complaint
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = <?php echo json_encode($properties, 15, 512) ?>;
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Handle emergency checkbox
    const emergencyCheckbox = document.getElementById('is_emergency');
    const prioritySelect = document.getElementById('priority');

    emergencyCheckbox.addEventListener('change', function() {
        if (this.checked) {
            prioritySelect.value = 'urgent';
            Swal.fire({
                title: 'Emergency Complaint',
                text: 'This complaint will be marked as urgent priority. Emergency contacts will be notified immediately.',
                icon: 'warning',
                confirmButtonText: 'Understood'
            });
        }
    });

    // File upload validation
    const fileInput = document.getElementById('attachments');
    fileInput.addEventListener('change', function() {
        const files = this.files;
        const maxFiles = 5;
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (files.length > maxFiles) {
            Swal.fire({
                title: 'Too Many Files',
                text: `You can only upload up to ${maxFiles} files.`,
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            this.value = '';
            return;
        }

        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSize) {
                Swal.fire({
                    title: 'File Too Large',
                    text: `File "${files[i].name}" is too large. Maximum size is 10MB.`,
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                this.value = '';
                return;
            }
        }
    });

    // Form validation
    const form = document.getElementById('createComplaintForm');
    form.addEventListener('submit', function(e) {
        const subject = document.getElementById('subject').value;
        const category = document.getElementById('category').value;
        const description = document.getElementById('description').value;
        const priority = document.getElementById('priority').value;

        if (!subject || !category || !description || !priority) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (description.length < 10) {
            e.preventDefault();
            Swal.fire({
                title: 'Description Too Short',
                text: 'Please provide a more detailed description of the issue.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/complaints/create.blade.php ENDPATH**/ ?>