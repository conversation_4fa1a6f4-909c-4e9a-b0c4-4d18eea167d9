@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-building-line text-primary me-2"></i>Property Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage and monitor all your properties in one place
                        </p>
                    </div>
                    <div>
                        @can('create', App\Models\Property::class)
                            <a href="{{ route('properties.create') }}" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Add New Property
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('properties.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Property name, address, city...">
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            @foreach($propertyTypes as $type)
                                <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $type)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="city" class="form-label">City</label>
                        <select class="form-select" id="city" name="city">
                            <option value="">All Cities</option>
                            @foreach($cities as $city)
                                <option value="{{ $city }}" {{ request('city') == $city ? 'selected' : '' }}>
                                    {{ $city }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="maintenance" {{ request('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="{{ route('properties.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Properties Grid -->
        @if($properties->count() > 0)
            <div class="row">
                @foreach($properties as $property)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 property-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="ri-building-line text-primary me-1"></i>
                                    {{ $property->name }}
                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="ri-more-line"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ route('properties.show', $property) }}">
                                                <i class="ri-eye-line me-1"></i>View Details
                                            </a>
                                        </li>
                                        @can('update', $property)
                                            <li>
                                                <a class="dropdown-item" href="{{ route('properties.edit', $property) }}">
                                                    <i class="ri-edit-line me-1"></i>Edit
                                                </a>
                                            </li>
                                        @endcan
                                        @can('delete', $property)
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-property" 
                                                        data-property-id="{{ $property->id }}"
                                                        data-property-name="{{ $property->name }}">
                                                    <i class="ri-delete-bin-line me-1"></i>Delete
                                                </button>
                                            </li>
                                        @endcan
                                    </ul>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Property Info -->
                                <div class="mb-3">
                                    <p class="text-muted mb-1">
                                        <i class="ri-map-pin-line me-1"></i>
                                        {{ $property->address }}, {{ $property->city }}, {{ $property->state }}
                                    </p>
                                    <p class="text-muted mb-1">
                                        <i class="ri-home-line me-1"></i>
                                        {{ ucfirst(str_replace('_', ' ', $property->property_type)) }}
                                    </p>
                                    <span class="badge bg-{{ $property->status == 'active' ? 'success' : ($property->status == 'maintenance' ? 'warning' : 'secondary') }}">
                                        {{ ucfirst($property->status) }}
                                    </span>
                                </div>

                                <!-- Quick Stats -->
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h5 class="mb-0 text-primary">{{ $property->total_units_count }}</h5>
                                            <small class="text-muted">Total Units</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h5 class="mb-0 text-success">{{ $property->occupied_units_count }}</h5>
                                            <small class="text-muted">Occupied</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <h5 class="mb-0 text-info">{{ $property->occupancy_rate }}%</h5>
                                        <small class="text-muted">Occupancy</small>
                                    </div>
                                </div>

                                <!-- Financial Info -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="text-muted">Monthly Income:</span>
                                    <span class="fw-bold text-success">${{ number_format($property->monthly_income, 0) }}</span>
                                </div>

                                <!-- Complaints -->
                                @if($property->open_complaints_count > 0)
                                    <div class="alert alert-warning py-2 mb-3">
                                        <i class="ri-alert-line me-1"></i>
                                        {{ $property->open_complaints_count }} open complaint(s)
                                    </div>
                                @endif
                            </div>
                            <div class="card-footer">
                                <div class="d-flex gap-2">
                                    <a href="{{ route('properties.show', $property) }}" class="btn btn-primary btn-sm flex-fill">
                                        <i class="ri-eye-line me-1"></i>View Details
                                    </a>
                                    @can('update', $property)
                                        <a href="{{ route('properties.edit', $property) }}" class="btn btn-outline-warning btn-sm">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-building-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Properties Found</h5>
                    <p class="text-muted mb-4">
                        @if(request()->hasAny(['search', 'type', 'city', 'status']))
                            No properties match your current filters. Try adjusting your search criteria.
                        @else
                            You haven't added any properties yet. Start by adding your first property.
                        @endif
                    </p>
                    @can('create', App\Models\Property::class)
                        <a href="{{ route('properties.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Your First Property
                        </a>
                    @endcan
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete property functionality
    document.querySelectorAll('.delete-property').forEach(button => {
        button.addEventListener('click', function() {
            const propertyId = this.dataset.propertyId;
            const propertyName = this.dataset.propertyName;

            Swal.fire({
                title: 'Delete Property',
                html: `Are you sure you want to delete:<br><br><strong>${propertyName}</strong>?<br><br><small class="text-danger">This action cannot be undone and will delete all associated units, tenants, and data.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/properties/${propertyId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
