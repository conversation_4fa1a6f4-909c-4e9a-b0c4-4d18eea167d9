<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-subtract-line text-primary me-2"></i>Debit Notes
                        </h4>
                        <p class="text-muted mb-0">
                            Manage debit notes for additional charges and adjustments
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('debit-notes.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create Debit Note
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('debit-notes.index')); ?>">All Notes</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('debit-notes.index', ['status' => 'draft'])); ?>">Draft</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('debit-notes.index', ['status' => 'issued'])); ?>">Issued</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('debit-notes.index', ['status' => 'applied'])); ?>">Applied</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('debit-notes.index', ['status' => 'cancelled'])); ?>">Cancelled</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format(0, 2)); ?></h3>
                                <p class="mb-0">Total Debit Amount</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-subtract-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($debitNotes->count()); ?></h3>
                                <p class="mb-0">Pending Notes</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-time-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">Applied Notes</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-check-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-calendar-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debit Notes List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Debit Notes</h5>
            </div>
            <div class="card-body">
                <?php if($debitNotes->isEmpty()): ?>
                    <div class="text-center py-5">
                        <i class="ri-subtract-line fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">No Debit Notes Found</h5>
                        <p class="text-muted mb-4">Create debit notes for additional charges, penalties, or adjustments.</p>
                        <a href="<?php echo e(route('debit-notes.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First Debit Note
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Note Number</th>
                                    <th>Tenant</th>
                                    <th>Property</th>
                                    <th>Amount</th>
                                    <th>Reason</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Placeholder for debit note data -->
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        Debit note data will be displayed here when implemented
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Debit notes management scripts will go here
    console.log('Debit Notes page loaded');
    
    // Apply debit note function
    function applyDebitNote(noteId) {
        if (confirm('Are you sure you want to apply this debit note to the tenant account?')) {
            // Implementation will go here
            console.log('Applying debit note:', noteId);
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/billing/debit-notes/index.blade.php ENDPATH**/ ?>