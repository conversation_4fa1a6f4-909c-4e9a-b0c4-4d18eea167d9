<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="731" height="553" viewBox="0 0 731 553">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1" data-name="Rectangle 1" width="731" height="553" transform="translate(-5967 -7180)" fill="#fff" stroke="#707070" stroke-width="1"/>
    </clipPath>
    <radialGradient id="radial-gradient" cx="0.508" cy="0" r="0.502" gradientTransform="scale(1 0.642)" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e3e7f9"/>
      <stop offset="1" stop-color="#fafafa"/>
    </radialGradient>
    <clipPath id="clip-path-2">
      <path id="Clip_10" data-name="Clip 10" d="M0,0H12V13H0Z" transform="translate(0 0)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="Clip_33" data-name="Clip 33" d="M0,119H89V0H0Z" fill="none"/>
    </clipPath>
  </defs>
  <g id="commercial-property-header" transform="translate(5967 7180)" clip-path="url(#clip-path)">
    <g id="Group_2" data-name="Group 2" transform="translate(-6748 -7399.483)">
      <path id="Rectangle" d="M800,0h731V137.828H800Z" transform="translate(-19 722)" fill="url(#radial-gradient)"/>
      <g id="Group_99" data-name="Group 99" transform="translate(833 219)">
        <path id="Fill_1" data-name="Fill 1" d="M0,38H294V0H0Z" transform="translate(8 260)" fill="#9cd0e2"/>
        <path id="Fill_2" data-name="Fill 2" d="M0,4H294V0H0Z" transform="translate(8 259)" fill="#204152"/>
        <path id="Fill_4" data-name="Fill 4" d="M0,38H4V0H0Z" transform="translate(67 260)" fill="#4298c7"/>
        <path id="Fill_5" data-name="Fill 5" d="M0,38H4V0H0Z" transform="translate(153 260)" fill="#4298c7"/>
        <path id="Fill_6" data-name="Fill 6" d="M0,38H4V0H0Z" transform="translate(239 260)" fill="#4298c7"/>
        <path id="Fill_10" data-name="Fill 10" d="M13.544,38H0L17.459,0H31Z" transform="translate(253 260)" fill="#84c0d1"/>
        <path id="Fill_11" data-name="Fill 11" d="M0,12H310V0H0Z" transform="translate(0 248)" fill="#cdd1e3"/>
        <path id="Fill_12" data-name="Fill 12" d="M0,38H294V0H0Z" transform="translate(8 309)" fill="#9cd0e2"/>
        <path id="Fill_13" data-name="Fill 13" d="M0,4H294V0H0Z" transform="translate(8 308)" fill="#204152"/>
        <path id="Fill_15" data-name="Fill 15" d="M0,38H4V0H0Z" transform="translate(67 309)" fill="#4298c7"/>
        <path id="Fill_16" data-name="Fill 16" d="M0,38H4V0H0Z" transform="translate(153 309)" fill="#4298c7"/>
        <path id="Fill_17" data-name="Fill 17" d="M0,38H4V0H0Z" transform="translate(239 309)" fill="#4298c7"/>
        <path id="Fill_21" data-name="Fill 21" d="M13.544,38H0L17.459,0H31Z" transform="translate(253 309)" fill="#84c0d1"/>
        <path id="Fill_22" data-name="Fill 22" d="M0,12H310V0H0Z" transform="translate(0 297)" fill="#cdd1e3"/>
        <path id="Fill_23" data-name="Fill 23" d="M0,38H294V0H0Z" transform="translate(8 359)" fill="#9cd0e2"/>
        <path id="Fill_24" data-name="Fill 24" d="M0,4H294V0H0Z" transform="translate(8 359)" fill="#204152"/>
        <path id="Fill_26" data-name="Fill 26" d="M0,38H4V0H0Z" transform="translate(67 359)" fill="#4298c7"/>
        <path id="Fill_27" data-name="Fill 27" d="M0,38H4V0H0Z" transform="translate(153 359)" fill="#4298c7"/>
        <path id="Fill_28" data-name="Fill 28" d="M0,38H4V0H0Z" transform="translate(239 359)" fill="#4298c7"/>
        <path id="Fill_32" data-name="Fill 32" d="M13.544,38H0L17.459,0H31Z" transform="translate(253 359)" fill="#84c0d1"/>
        <path id="Fill_33" data-name="Fill 33" d="M0,12H310V0H0Z" transform="translate(0 347)" fill="#cdd1e3"/>
        <path id="Fill_34" data-name="Fill 34" d="M0,106H294V0H0Z" transform="translate(8 397)" fill="#cdd1e3"/>
        <path id="Fill_35" data-name="Fill 35" d="M0,42H152V0H0Z" transform="translate(28 205)" fill="#dee0ed"/>
        <path id="Fill_36" data-name="Fill 36" d="M0,7H159V0H0Z" transform="translate(25 199)" fill="#223752"/>
        <path id="Fill_37" data-name="Fill 37" d="M0,14H140V0H0Z" transform="translate(33 219)" fill="#04486b"/>
        <path id="Fill_38" data-name="Fill 38" d="M0,14H3V0H0Z" transform="translate(160 219)" fill="#05568d"/>
        <path id="Fill_39" data-name="Fill 39" d="M0,14H3V0H0Z" transform="translate(131 219)" fill="#05568d"/>
        <path id="Fill_40" data-name="Fill 40" d="M0,14H3V0H0Z" transform="translate(102 219)" fill="#05568d"/>
        <path id="Fill_41" data-name="Fill 41" d="M0,14H3V0H0Z" transform="translate(73 219)" fill="#05568d"/>
        <path id="Fill_42" data-name="Fill 42" d="M0,14H3V0H0Z" transform="translate(44 219)" fill="#05568d"/>
        <path id="Fill_43" data-name="Fill 43" d="M0,78H233V0H0Z" transform="translate(39 426)" fill="#2b2b2b"/>
        <path id="Fill_44" data-name="Fill 44" d="M0,72H159V0H0Z" transform="translate(76 432)" fill="#9cd0e2"/>
        <path id="Fill_50" data-name="Fill 50" d="M0,4H159V0H0Z" transform="translate(76 447)" fill="#4298c7"/>
        <path id="Fill_51" data-name="Fill 51" d="M0,72H4V0H0Z" transform="translate(123 432)" fill="#4298c7"/>
        <path id="Fill_52" data-name="Fill 52" d="M0,72H4V0H0Z" transform="translate(183 432)" fill="#4298c7"/>
        <path id="Fill_53" data-name="Fill 53" d="M0,72H25V0H0Z" transform="translate(44 432)" fill="#9cd0e2"/>
        <path id="Fill_54" data-name="Fill 54" d="M0,4H25V0H0Z" transform="translate(44 447)" fill="#4298c7"/>
        <path id="Fill_55" data-name="Fill 55" d="M0,72H8V0H0Z" transform="translate(44 432)" fill="#84c1d1"/>
        <path id="Fill_57" data-name="Fill 57" d="M0,72H25V0H0Z" transform="translate(240 432)" fill="#9cd0e2"/>
        <path id="Fill_58" data-name="Fill 58" d="M0,4H25V0H0Z" transform="translate(240 447)" fill="#4298c7"/>
        <path id="Fill_59" data-name="Fill 59" d="M0,72H8V0H0Z" transform="translate(240 432)" fill="#204152"/>
        <path id="Fill_61" data-name="Fill 61" d="M190,0,0,63.793V503H190Z" transform="translate(239)" fill="#383736"/>
        <path id="Fill_62" data-name="Fill 62" d="M0,63.6V484H190V0Z" transform="translate(239 19)" fill="#9cd0e2"/>
        <path id="Fill_63" data-name="Fill 63" d="M69,9H0L22.956,0H69Z" transform="translate(360 34)" fill="#4298c7"/>
        <path id="Fill_64" data-name="Fill 64" d="M0,9H190V0H0Z" transform="translate(239 92)" fill="#4298c7"/>
        <path id="Fill_65" data-name="Fill 65" d="M0,9H190V0H0Z" transform="translate(239 151)" fill="#4298c7"/>
        <path id="Fill_66" data-name="Fill 66" d="M0,9H190V0H0Z" transform="translate(239 210)" fill="#4298c7"/>
        <path id="Fill_67" data-name="Fill 67" d="M0,9H190V0H0Z" transform="translate(239 269)" fill="#4298c7"/>
        <path id="Fill_68" data-name="Fill 68" d="M0,9H190V0H0Z" transform="translate(239 328)" fill="#4298c7"/>
        <path id="Fill_69" data-name="Fill 69" d="M0,9H190V0H0Z" transform="translate(239 386)" fill="#4298c7"/>
        <path id="Fill_70" data-name="Fill 70" d="M0,9H190V0H0Z" transform="translate(239 445)" fill="#4298c7"/>
        <path id="Fill_71" data-name="Fill 71" d="M0,3.871V455H12V0Z" transform="translate(328 48)" fill="#dee0ed"/>
        <path id="Fill_72" data-name="Fill 72" d="M0,382H223V0H0Z" transform="translate(429 122)" fill="#dee0ed"/>
        <path id="Fill_73" data-name="Fill 73" d="M0,21H223V0H0Z" transform="translate(429 101)" fill="#cdd1e3"/>
        <path id="Fill_74" data-name="Fill 74" d="M0,402H17V0H0Z" transform="translate(429 101)" fill="#cdd1e3"/>
        <path id="Fill_76" data-name="Fill 76" d="M0,87H164V0H0Z" transform="translate(456 417)" fill="#cdd1e3"/>
        <path id="Fill_77" data-name="Fill 77" d="M0,77H146V0H0Z" transform="translate(465 426)" fill="#9cd0e2"/>
        <path id="Fill_78" data-name="Fill 78" d="M0,3H146V0H0Z" transform="translate(465 441)" fill="#223752"/>
        <path id="Fill_79" data-name="Fill 79" d="M0,77H4V0H0Z" transform="translate(508 426)" fill="#223752"/>
        <path id="Fill_80" data-name="Fill 80" d="M0,77H4V0H0Z" transform="translate(564 426)" fill="#223752"/>
        <path id="Fill_81" data-name="Fill 81" d="M0,11H61V0H0Z" transform="translate(465 153)" fill="#9cd0e2"/>
        <path id="Fill_82" data-name="Fill 82" d="M0,11H61V0H0Z" transform="translate(465 183)" fill="#9cd0e2"/>
        <path id="Fill_83" data-name="Fill 83" d="M0,11H61V0H0Z" transform="translate(465 211)" fill="#9cd0e2"/>
        <path id="Fill_84" data-name="Fill 84" d="M0,11H61V0H0Z" transform="translate(465 240)" fill="#9cd0e2"/>
        <path id="Fill_85" data-name="Fill 85" d="M0,11H61V0H0Z" transform="translate(465 268)" fill="#9cd0e2"/>
        <path id="Fill_86" data-name="Fill 86" d="M0,11H61V0H0Z" transform="translate(465 297)" fill="#9cd0e2"/>
        <path id="Fill_87" data-name="Fill 87" d="M0,11H61V0H0Z" transform="translate(465 326)" fill="#9cd0e2"/>
        <path id="Fill_88" data-name="Fill 88" d="M0,11H61V0H0Z" transform="translate(465 355)" fill="#9cd0e2"/>
        <path id="Fill_89" data-name="Fill 89" d="M0,11H61V0H0Z" transform="translate(465 383)" fill="#9cd0e2"/>
        <path id="Fill_90" data-name="Fill 90" d="M0,11H61V0H0Z" transform="translate(550 153)" fill="#9cd0e2"/>
        <path id="Fill_91" data-name="Fill 91" d="M0,11H61V0H0Z" transform="translate(550 183)" fill="#9cd0e2"/>
        <path id="Fill_92" data-name="Fill 92" d="M0,11H61V0H0Z" transform="translate(550 211)" fill="#9cd0e2"/>
        <path id="Fill_93" data-name="Fill 93" d="M0,11H61V0H0Z" transform="translate(550 240)" fill="#9cd0e2"/>
        <path id="Fill_94" data-name="Fill 94" d="M0,11H61V0H0Z" transform="translate(550 268)" fill="#9cd0e2"/>
        <path id="Fill_95" data-name="Fill 95" d="M0,11H61V0H0Z" transform="translate(550 297)" fill="#9cd0e2"/>
        <path id="Fill_96" data-name="Fill 96" d="M0,11H61V0H0Z" transform="translate(550 326)" fill="#9cd0e2"/>
        <path id="Fill_97" data-name="Fill 97" d="M0,11H61V0H0Z" transform="translate(550 355)" fill="#9cd0e2"/>
        <path id="Fill_98" data-name="Fill 98" d="M0,11H61V0H0Z" transform="translate(550 383)" fill="#9cd0e2"/>
      </g>
      <path id="Fill_86-2" data-name="Fill 86" d="M0,47.12H2.537V0H0Z" transform="translate(1430.598 675.173)" fill="#573a2e"/>
      <path id="Fill_87-2" data-name="Fill 87" d="M19.239,0s-43.288,90.717,0,90.717S19.239,0,19.239,0" transform="translate(1412.627 591.969)" fill="#44d7b6"/>
      <path id="Fill_92-2" data-name="Fill 92" d="M0,50.746H2.734V0H0Z" transform="translate(1456.136 671.547)" fill="#573a2e"/>
      <path id="Fill_93-2" data-name="Fill 93" d="M0,49.329C0,22.085,7.385,0,16.5,0s16.5,22.085,16.5,49.329S25.608,98.659,16.5,98.659,0,76.574,0,49.329" transform="translate(1441.005 588.757)" fill="#44d7b6"/>
      <g id="Group_33" data-name="Group 33" transform="translate(792 588.757)">
        <path id="Fill_82-2" data-name="Fill 82" d="M0,50.746H2.734V0H0Z" transform="translate(18.808 82.79)" fill="#573a2e"/>
        <path id="Fill_84-2" data-name="Fill 84" d="M0,49.329C0,22.085,9.033,0,20.178,0S40.353,22.085,40.353,49.329,31.32,98.659,20.178,98.659,0,76.574,0,49.329" transform="translate(0 0)" fill="#44d7b6"/>
        <path id="Fill_85-2" data-name="Fill 85" d="M7.289,52.973c0-27.245,9.033-49.329,20.176-49.329.118,0,.235.038.351.044C25.459,1.325,22.881,0,20.176,0,9.033,0,0,22.087,0,49.329,0,76.284,8.842,98.151,19.824,98.615,12.472,91.249,7.289,73.6,7.289,52.973" transform="translate(0.001 0)" fill="#32c6a5"/>
        <path id="Fill_89-2" data-name="Fill 89" d="M0,39.352H2.118V0H0Z" transform="translate(43.834 94.185)" fill="#573a2e"/>
        <path id="Fill_90-2" data-name="Fill 90" d="M16.068,0s-36.153,75.762,0,75.762S16.068,0,16.068,0" transform="translate(28.824 24.696)" fill="#44d7b6"/>
        <path id="Fill_91-2" data-name="Fill 91" d="M19.458,7.78C17.462,3.006,16.031,0,16.031,0s-32.537,68.237-4.9,75.153c-13.876-12.128,2.147-53.1,8.33-67.373" transform="translate(28.861 24.695)" fill="#32c6a5"/>
      </g>
      <g id="Group_68" data-name="Group 68" transform="translate(1215 601)">
        <path id="Fill_1-2" data-name="Fill 1" d="M7,.533V6H0S.225,5,.517,4.872A25.636,25.636,0,0,0,2.868,2.4C3.035,2.178,3.912,0,3.912,0Z" transform="translate(75 113)" fill="#283d50"/>
        <path id="Fill_3" data-name="Fill 3" d="M9,.533V6H7.349L6.936,4.978,6.44,6H0S.223,5,.511,4.872s4.236-2.25,4.4-2.472S5.945,0,5.945,0Z" transform="translate(65 113)" fill="#283d50"/>
        <path id="Fill_5-2" data-name="Fill 5" d="M9.946,4.355a8.445,8.445,0,0,0,.69,3.573C10.959,8.817,14,9.852,14,9.852S4.805,20.064,1.256,20s1.551-9.63,1.551-9.63,1.488-.9,1.812-1.875a19.96,19.96,0,0,0,.518-3.17L9.3,0Z" transform="translate(66 9)" fill="#ebb397"/>
        <path id="Fill_7" data-name="Fill 7" d="M1.831,0C1.241.406.231,1.262.188,1.893A28.56,28.56,0,0,0,.02,5.455C.1,6.537.146,9.332.315,10.595s.317,2.976.6,3.2a8.483,8.483,0,0,0,3.7,0c.59-.315,3.5-3.426,3.37-6.131,0,0,1.116.563,2-1.871.231-.635-1.753-3.067-2-3.81C7.391.18,3.979.676,3.052.541A1.837,1.837,0,0,1,1.831,0" transform="translate(67 3)" fill="#ebb397"/>
        <g id="Group_11" data-name="Group 11" transform="translate(67 0)">
          <path id="Clip_10-2" data-name="Clip 10" d="M0,0H12V13H0Z" transform="translate(0 0)" fill="none"/>
          <g id="Group_11-2" data-name="Group 11" clip-path="url(#clip-path-2)">
            <path id="Fill_9" data-name="Fill 9" d="M.67,4.609s.338-.826,1.015-.478A7.193,7.193,0,0,0,4.9,4.609a7.435,7.435,0,0,0,2.156-.564,6.023,6.023,0,0,0,.3,2.042c.3.435.972.956,1.015,1.478a7.373,7.373,0,0,1,0,1.043S9.041,7.7,9.591,7.87s.549,1.13,0,1.565a9.149,9.149,0,0,1-1.1.739,7.153,7.153,0,0,0,.127,1.3C8.745,12.044,9,13,9,13a26.255,26.255,0,0,1,2.156-4.652c.972-1.348,1.437-4.13-.465-5.174A3.184,3.184,0,0,0,8.026.088C5.363-.39-.514,1.175.036,2.479A8.285,8.285,0,0,1,.67,4.609" transform="translate(0 0)" fill="#2a2d3a"/>
          </g>
        </g>
        <path id="Fill_12-2" data-name="Fill 12" d="M14.182,1.385s-.014,10.593.025,20.8c.009,2.5.865,8.842.781,13.168-.03,1.579.038,4.35,0,6.029-.28,12.515-.082,23.075-.082,23.075L9.957,65S8.172,53.563,6.418,42.164C5.682,37.377,3.686,28.785,3.2,26.845,1.549,20.285,0,0,0,0Z" transform="translate(67 51)" fill="#b6bfd7"/>
        <path id="Fill_14" data-name="Fill 14" d="M9.869,11.961s.255,16.756.1,19.838c-.05.982.858,4.7.715,9.317-.023.726-.046,1.474-.07,2.237-.049,1.588.416,3.242.366,4.895C10.728,56.6,11,64.953,11,64.953s-3.877.105-3.877,0S1.407,33.339.933,25.745.737,8.044.108,6.994,1.805,0,1.805,0L7.293,1.468Z" transform="translate(62 51)" fill="#a7b1c9"/>
        <path id="Fill_16-2" data-name="Fill 16" d="M15.79,0l5.817,3.121S23.079,9.006,23,11.4s-3.412,26.154-3.412,26.154-8.325,1-12.51,0S0,35.631,0,35.631,1.445,12.444,1.775,10.05,2.929,2.867,2.929,2.867l3.277-1.68,1.817,1.39Z" transform="translate(62 18)" fill="#5b61eb"/>
        <path id="Fill_18" data-name="Fill 18" d="M0,.474.744,2.91l1.3.09L4,.113,1.148,0Z" transform="translate(69 20)" fill="#424dd2"/>
        <path id="Fill_20" data-name="Fill 20" d="M7.219,0,0,3.7,2.923,6,8,1.171Z" transform="translate(70 16)" fill="#424dd2"/>
        <path id="Fill_22-2" data-name="Fill 22" d="M2,2.229,0,4,.412.886,1.063,0Z" transform="translate(68 17)" fill="#424dd2"/>
        <path id="Fill_24-2" data-name="Fill 24" d="M6,7.53,3.028,9,0,6.944V0L6,.587Z" transform="translate(73 30)" fill="#424dd2"/>
        <path id="Fill_26-2" data-name="Fill 26" d="M12,16.835c-3.909.85-8.779-1.736-12-4.4L1.322,7.848,6.935,0a4.168,4.168,0,0,1,4.079,3.783c.635,3.861-.531,6.888.856,12.37a6.623,6.623,0,0,1,.13.683" transform="translate(12 9)" fill="#2a2d3a"/>
        <path id="Fill_28-2" data-name="Fill 28" d="M1.714,5.6c.048.077.149.219.279.4C2.951,6,4.185,4.4,4.3,4.37a2.783,2.783,0,0,1,1.161.214c.24.161.452.187.527-.085s-.353-.624-.647-.9A5.464,5.464,0,0,0,3.96,2.824c-.355-.121-.63-.69-.847-.886A4.955,4.955,0,0,1,1.8,0,4.758,4.758,0,0,0,.549.567C-.066.969,0,1.159,0,1.159.652,2.289,1.47,5.219,1.714,5.6" transform="translate(28 58)" fill="#eebd9e"/>
        <path id="Fill_30" data-name="Fill 30" d="M4.469.427A2.91,2.91,0,0,0,.1,2.236C-.541,5.209,1.969,15.767,2.922,20.719c.09.467-.375-1.8-.063-.726A61.569,61.569,0,0,0,9.35,34,5.914,5.914,0,0,1,12,31.849S8.211,20.574,7.642,19.158c0,0-.707-5.812-.947-11.624C6.521,3.324,5.527,1.1,4.469.427" transform="translate(18 27)" fill="#424dd2"/>
        <path id="Clip_33-2" data-name="Clip 33" d="M0,119H89V0H0Z" fill="none"/>
        <g id="Mask_Group_6" data-name="Mask Group 6" clip-path="url(#clip-path-3)">
          <path id="Fill_32-2" data-name="Fill 32" d="M0,12H21V0H0Z" transform="translate(14 51)" fill="#435c82"/>
          <path id="Fill_34-2" data-name="Fill 34" d="M0,12H21V0H0Z" transform="translate(14 50)" fill="#fff"/>
          <path id="Fill_35-2" data-name="Fill 35" d="M0,12H21V0H0Z" transform="translate(13 51)" fill="#435c82"/>
          <path id="Fill_36-2" data-name="Fill 36" d="M4,1.72C3.979,1.439,3.716,0,2.948.482,2.776.164,2.083-.265,1.779.214,1.218-.014,1.22.194.81.353.471.484.163.362.023.843a.918.918,0,0,0,.45.984,5.4,5.4,0,0,0,1.817.165c.45-.019,1.454.086,1.71-.272" transform="translate(29 62)" fill="#eebd9e"/>
          <path id="Fill_37-2" data-name="Fill 37" d="M6.539,4.038c-.6,0-1.6-.832-.723-2.788L2.088,0A17.413,17.413,0,0,1,.163,4C-.928,5.47,3.708,14.521,6.387,14.961S10,12.006,10,12.006L8.1,5.343Z" transform="translate(13 22)" fill="#eebd9e"/>
          <path id="Fill_38-2" data-name="Fill 38" d="M7,4.8c-.842.761-1.653,3.492-1.623,4.957L0,11S1.2,7.111,1.73,4.175,1.708,0,1.708,0Z" transform="translate(13 17)" fill="#eebd9e"/>
          <path id="Fill_39-2" data-name="Fill 39" d="M8.328,1.388A5.617,5.617,0,0,1,9.218,4c.051.569-.193,1.138.147,1.644S10.867,7,10.988,7.3s-.686.92-.768,1.132-.392,1.924-.53,2.791-2.024.914-3.264.6A17.376,17.376,0,0,1,2.65,9.986C1.944,9.557.919,7.2.919,7.2L0,2.559A3.7,3.7,0,0,1,3.525,0C6.313-.016,7.6.415,8.328,1.388" transform="translate(14 11)" fill="#eebd9e"/>
          <path id="Fill_40-2" data-name="Fill 40" d="M7.175,9.427c-.609.136-1.057,3.778-.1,6.2-2.854.925-4.233-.018-6.341-1.132C0,13.378-1,5.07,2.322,2.117,7.861-2.81,11,2.407,11,2.407S9.884,8.224,7.175,9.427" transform="translate(10 9)" fill="#2a2d3a"/>
          <path id="Fill_41-2" data-name="Fill 41" d="M4,0A10.236,10.236,0,0,1,7.4,2.6c1.6,1.847,3.811,4.03,5.094,5.66,0,0-.792-2.476-1.166-3.784C10.9,2.982,10.41.7,9.86.27l1.77.6a58.755,58.755,0,0,0,2.356,5.819c.822,1.459,2.071,3.476,2.012,5.422s-1.219,4.711-1.049,6.378-.042,10.229-.042,10.229a23.365,23.365,0,0,1-7.687,1.095C3.24,29.631,3.654,29.16,2.375,30a106.359,106.359,0,0,0,2.3-11.1c.343-3-1.259-8.131-1.955-11.452S-.836,4.2.238,2.486,4,0,4,0" transform="translate(10 26)" fill="#b1bddb"/>
          <path id="Fill_42-2" data-name="Fill 42" d="M3.584,9.289A34.631,34.631,0,0,1,4.789.916c.07-.3.143-.6.218-.916C3.647.041,2.286.09.925.132.768,4.54.984,9,0,11.152a8.927,8.927,0,0,0,2.6,2.429c.578.3,2.2,1.586,2.969,1.4A2.024,2.024,0,0,0,7,13.782s-3.152-2.7-3.416-4.492" transform="translate(14 103)" fill="#eebd9e"/>
          <path id="Fill_43-2" data-name="Fill 43" d="M.191.054a1.123,1.123,0,0,1,1.236.4A12.458,12.458,0,0,0,4.542,3,2.817,2.817,0,0,0,7.6,2.719,11.872,11.872,0,0,1,9.63,3.869c1.392.953-1.337,1.444-4.924.914C3.92,4.667,1.759,3.437,1.478,3.422S.907,4.36.667,4.36c-.424,0-.5.088-.5.088L0,1.992Z" transform="translate(14 115)" fill="#283d50"/>
          <path id="Fill_44-2" data-name="Fill 44" d="M10.595,0c.874,7.02.08,23.047-.035,24.479s.1,3.436-.592,5.058A204.367,204.367,0,0,0,5.28,51a27.808,27.808,0,0,1-4.821-.544s.689-10.422.689-12.712A49.277,49.277,0,0,1,3.214,27.628c.459-.954-1.033-8.762-1.148-10.479S0,.926,0,.926Z" transform="translate(14 57)" fill="#a7b1c9"/>
          <path id="Fill_45" data-name="Fill 45" d="M3.766,7.511A29.762,29.762,0,0,1,7,.967Q5.214.472,3.423,0C2.274,3.247,1.239,6.251,0,7.489a11.9,11.9,0,0,0,1.526,3.617c.416.574,1.437,2.612,2.178,2.821A1.562,1.562,0,0,0,5.35,13.5S3.427,9.348,3.766,7.511" transform="translate(2 103)" fill="#eebd9e"/>
          <path id="Fill_46" data-name="Fill 46" d="M1.535,0s.645.035.989.973A15.652,15.652,0,0,0,4.48,4.909,2.775,2.775,0,0,0,7.362,6.115,14.63,14.63,0,0,1,8.808,8.187c.938,1.58-1.713.735-4.792-1.5C3.341,6.2,1.8,3.987,1.549,3.837s-.843.621-1.061.506C.1,4.139,0,4.185,0,4.185L.7,1.759Z" transform="translate(0 110)" fill="#283d50"/>
          <path id="Fill_47" data-name="Fill 47" d="M5.116,0c-.244,1.453-1.462,4.307,0,11.676S7.308,25.707,7.308,25.707s-2.68,5.879-4.141,9.976A124.5,124.5,0,0,0,0,48.831L3.654,51s7.065-14.957,8.283-17.212,2.9-5.756,2.987-8.081C15.082,21.625,16,12.871,16.641,9.335,17.043,7.121,18,.435,18,.435Z" transform="translate(4 57)" fill="#b6bfd7"/>
          <path id="Fill_48" data-name="Fill 48" d="M17,9.354a22.913,22.913,0,0,0-.8-5C15.409,2.387,14.505,0,14.505,0L3.22,4.82A9.961,9.961,0,0,0,0,9.085a46.889,46.889,0,0,0,17,.268" transform="translate(9 48)" fill="#f0ede7"/>
          <path id="Fill_49" data-name="Fill 49" d="M6,11.954c.057-1.964-1.163-4-1.966-5.473A60.465,60.465,0,0,1,1.73.607L0,0C.538.433,1.017,2.738,1.433,4.241c.365,1.32,1.139,3.82,1.139,3.82-.433-.568-.305,18.514-.02,21.294C3.276,32.883,6,31.863,6,31.863S5.139,20.074,4.973,18.392,5.94,13.918,6,11.954" transform="translate(20 26)" fill="#5b61eb"/>
          <path id="Fill_50-2" data-name="Fill 50" d="M3.315.879C2.2.617,1.093.333,0,0-.026,5.488.1,12.8.281,14.425,1.062,17.86,4,16.867,4,16.867S3.072,5.39,2.893,3.753A8.942,8.942,0,0,1,3.315.879" transform="translate(22 41)" fill="#424dd2"/>
          <path id="Fill_51-2" data-name="Fill 51" d="M1.5,11.631A100.645,100.645,0,0,0,.314.012C-.17-.557-.028,18.57.292,21.357.774,23.462,2.047,23.948,3,24c-.562-5.355-1.4-7-1.5-12.369" transform="translate(21 34)" fill="#3545ba"/>
          <path id="Fill_52-2" data-name="Fill 52" d="M1.289,4.214C1.688,2.958,4.782,0,4.782,0s6.692,4.413,8.712,8.428c0,0,.768,13.4.411,17.46s-2.826,8.533-6.891,8.08A36.188,36.188,0,0,1,0,32.5s3.9-9.154,3.478-11.424C2.762,17.23.66,6.189,1.289,4.214" transform="translate(9 26)" fill="#5b61eb"/>
          <path id="Fill_53-2" data-name="Fill 53" d="M1.234,0c.306,4.021,1.69,11.242,2.244,14.193C3.9,16.444,0,25.515,0,25.515a36.462,36.462,0,0,0,7.015,1.453c4.065.449,6.534-3.982,6.891-8.008.167-1.889.087-5.814-.045-9.449C9.2,6.963,4.569,4.076,1.234,0" transform="translate(9 32)" fill="#424dd2"/>
          <path id="Stroke_54" data-name="Stroke 54" d="M3.342,0a3.258,3.258,0,0,1,.09,3.807A5.549,5.549,0,0,1,0,6" transform="translate(18 11)" fill="none" stroke="#2a2d3a" stroke-miterlimit="10" stroke-width="0.224"/>
          <path id="Fill_55-2" data-name="Fill 55" d="M.122,2.449a5.375,5.375,0,0,0,2.511-.518c.3-.14.978-.175,1.275-.491A6.8,6.8,0,0,1,5.351.341C5.768.158,6.322-.131,6.54.066s.084.405-.192.584S6.44,1.895,7.174,1.7,8.768.9,9.187.789s.475.5.475.5.363.329.337.6a3.157,3.157,0,0,1-.165.832A5.565,5.565,0,0,0,9.481,3.8c-.026.273-.976.938-1.358,1.086s-2.255.02-2.745.1S1.7,4.638.292,4.818c0,0-.215-.012-.278-.827A5.692,5.692,0,0,1,.122,2.449" transform="translate(36 39)" fill="#eebd9e"/>
          <path id="Fill_56" data-name="Fill 56" d="M4,.078C2.17-.362.211,1.06,0,4.2s7.546,8.833,11.373,12.426a2.557,2.557,0,0,0,1.746.731A131.613,131.613,0,0,0,27.714,19s.106-1.564.286-3.493c0,0-10.594-1.4-13.614-3.266A13.555,13.555,0,0,1,10.359,7.4C8.293,3.578,6.564.694,4,.078" transform="translate(9 27)" fill="#5b61eb"/>
          <path id="Fill_57-2" data-name="Fill 57" d="M3.169,0A7.62,7.62,0,0,1,3,1.562,12.578,12.578,0,0,1,.893,5.776,6.336,6.336,0,0,0,0,7.809l.923.081s.346-.4.606-.64.722-.467.716-.29A10.881,10.881,0,0,1,1.76,9.682,2.946,2.946,0,0,1,.365,10.838L1.907,11l1.4-.411.559-.2.591-1.091.663-.36s.512-1.943.764-3.346.027-1.9-.014-4.075A13.539,13.539,0,0,1,6,.043C5.056.034,4.112.017,3.169,0" transform="translate(81 57)" fill="#ebb397"/>
          <path id="Fill_58-2" data-name="Fill 58" d="M3.994,38.644,7.724,39S9.023,23.531,9,22.852,9.183,6.692,7.283,3.109C5.743.207,3.357-.431,1.345.249S.175,5.432.175,5.432L3.24,22.968Z" transform="translate(80 20)" fill="#5b61eb"/>
          <path id="Fill_59-2" data-name="Fill 59" d="M1.868.411A2.2,2.2,0,0,1,0,.006L.153,3.561,3.7,4s.127-1.685.3-4C3.288.12,2.577.252,1.868.411" transform="translate(84 56)" fill="#424dd2"/>
          <path id="Fill_60" data-name="Fill 60" d="M5.086.332,2.808.587S1.586.175,1.177.045A1.058,1.058,0,0,0,0,.587l.734.642L2.548,3.381s.83.775,1.361.59S6,3.28,6,3.28Z" transform="translate(41 40)" fill="#ebb397"/>
          <path id="Fill_61-2" data-name="Fill 61" d="M0,20.662,1.39,24s16.082-3.629,16.689-4.114c.369-.294,3.95-4.6,6.705-7.931C26.564,9.8,28,8.051,28,8.051s-.709-5.529-2.733-6.791A7.794,7.794,0,0,0,21.521,0L13.578,14.551Z" transform="translate(44 20)" fill="#5b61eb"/>
          <path id="Fill_62-2" data-name="Fill 62" d="M2.911,0,0,1.229,1.231,4S2.363,3.76,4,3.408Q3.427,1.712,2.911,0" transform="translate(45 40)" fill="#424dd2"/>
          <path id="Fill_63-2" data-name="Fill 63" d="M2.012,0s-.4,5.152-.844,11.228C.7,17.639.18,25.015.027,26c-.3,1.914,1.985,4,1.985,4S3.9,27.477,4,26.693c.064-.505-.828-11.437-1.025-19.128A43.87,43.87,0,0,1,3.4.087Z" transform="translate(69 22)" fill="#424dd2"/>
          <path id="Fill_64-2" data-name="Fill 64" d="M.923,0A21.424,21.424,0,0,1,5.6,2.761C7.5,4.361,10,9,10,9S8.354,7.264,7.633,6.679C7.3,6.412,5.8,5.591,5.8,5.591l.538-.823L4.5,4.672A27.111,27.111,0,0,0,2.776,2.221,4.991,4.991,0,0,0,0,.347Z" transform="translate(12 26)" fill="#424dd2"/>
          <path id="Fill_65-2" data-name="Fill 65" d="M0,0C.142.323.881,1.93,1.557,3.4S4,9,4,9s-.29-2.192-.354-3.009-.1-1.611-.1-1.611H2.585a1.708,1.708,0,0,1,.4-.95A15.259,15.259,0,0,0,2,1.44,13.927,13.927,0,0,0,.762,0Z" transform="translate(18 26)" fill="#424dd2"/>
          <path id="Fill_66-2" data-name="Fill 66" d="M0,.869C.475.323.588.033,1.057,0s.453.167.453.167.524-.153.728-.044l.2.109a5.167,5.167,0,0,1,.531.018A.649.649,0,0,1,3.39.434S4,.2,4,.434,3.491,1,3.22,1,0,.869,0,.869" transform="translate(40 43)" fill="#ebb397"/>
          <path id="Fill_67-2" data-name="Fill 67" d="M1.523,0C.888.21.007.571,0,.788s.355.276.762.135,1.694-.4,1.694-.4L3,.394Z" transform="translate(40 41)" fill="#ebb397"/>
        </g>
      </g>
    </g>
  </g>
</svg>
