<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['task_reminder', 'lease_expiry', 'document_expiry', 'payment_due', 'maintenance_request', 'system_alert', 'general'])->default('general');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // recipient
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null'); // sender
            $table->foreignId('related_task_id')->nullable()->constrained('tasks')->onDelete('cascade');
            $table->foreignId('related_property_id')->nullable()->constrained('properties')->onDelete('cascade');
            $table->foreignId('related_unit_id')->nullable()->constrained('units')->onDelete('cascade');
            $table->json('data')->nullable(); // additional data for the notification
            $table->boolean('is_read')->default(false);
            $table->boolean('is_email_sent')->default(false);
            $table->datetime('read_at')->nullable();
            $table->datetime('email_sent_at')->nullable();
            $table->datetime('scheduled_for')->nullable(); // for scheduled notifications
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
