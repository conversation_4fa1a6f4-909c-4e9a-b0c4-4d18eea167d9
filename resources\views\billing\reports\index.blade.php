@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-bar-chart-box-line text-primary me-2"></i>Billing Reports
                        </h4>
                        <p class="text-muted mb-0">
                            Comprehensive billing and payment reports and analytics
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success" onclick="exportAllReports()">
                            <i class="ri-download-line me-1"></i>Export All
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-calendar-line me-1"></i>Date Range
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="setDateRange('today')">Today</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setDateRange('week')">This Week</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setDateRange('month')">This Month</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setDateRange('quarter')">This Quarter</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setDateRange('year')">This Year</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">${{ number_format(0, 2) }}</h3>
                                <p class="mb-0">Total Revenue</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-money-dollar-circle-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">${{ number_format(0, 2) }}</h3>
                                <p class="mb-0">Outstanding</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-time-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">Payments</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-check-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">Properties</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-building-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Types -->
        <div class="row">
            @foreach($reportTypes as $key => $title)
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="card-title">{{ $title }}</h5>
                                <p class="text-muted mb-0">
                                    @switch($key)
                                        @case('revenue')
                                            Detailed revenue analysis and trends
                                            @break
                                        @case('outstanding')
                                            Outstanding payments and overdue bills
                                            @break
                                        @case('payment-history')
                                            Complete payment transaction history
                                            @break
                                        @case('tenant-statements')
                                            Individual tenant account statements
                                            @break
                                    @endswitch
                                </p>
                            </div>
                            <div class="icon-shape bg-light">
                                @switch($key)
                                    @case('revenue')
                                        <i class="ri-line-chart-line text-success"></i>
                                        @break
                                    @case('outstanding')
                                        <i class="ri-alarm-warning-line text-warning"></i>
                                        @break
                                    @case('payment-history')
                                        <i class="ri-history-line text-info"></i>
                                        @break
                                    @case('tenant-statements')
                                        <i class="ri-user-line text-primary"></i>
                                        @break
                                @endswitch
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ route('billing-reports.' . $key) }}" class="btn btn-primary btn-sm">
                                <i class="ri-eye-line me-1"></i>View Report
                            </a>
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportReport('{{ $key }}')">
                                <i class="ri-download-line me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Billing Activity</h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="ri-bar-chart-box-line fs-1 text-muted mb-3"></i>
                    <h6 class="text-muted">Recent Activity</h6>
                    <p class="text-muted mb-0">Recent billing activity will be displayed here when implemented</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Billing reports scripts
    console.log('Billing Reports page loaded');
    
    function setDateRange(range) {
        console.log('Setting date range:', range);
        // Implementation will go here
    }
    
    function exportReport(reportType) {
        console.log('Exporting report:', reportType);
        // Implementation will go here
    }
    
    function exportAllReports() {
        console.log('Exporting all reports');
        // Implementation will go here
    }
</script>
@endpush
