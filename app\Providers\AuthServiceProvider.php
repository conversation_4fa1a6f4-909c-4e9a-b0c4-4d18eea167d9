<?php

namespace App\Providers;

use App\Models\Property;
use App\Models\Unit;
use App\Models\Complaint;
use App\Models\Bill;
use App\Models\Payment;
use App\Policies\PropertyPolicy;
use App\Policies\UnitPolicy;
use App\Policies\ComplaintPolicy;
use App\Policies\BillPolicy;
use App\Policies\PaymentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Property::class => PropertyPolicy::class,
        Unit::class => UnitPolicy::class,
        Complaint::class => ComplaintPolicy::class,
        Bill::class => BillPolicy::class,
        Payment::class => PaymentPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define additional gates if needed
        Gate::define('manage-system', function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            return in_array('admin', $userRoles);
        });

        Gate::define('access-admin-panel', function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            return !empty(array_intersect($userRoles, ['admin', 'property_owner', 'receptionist']));
        });

        Gate::define('view-financial-data', function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            return !empty(array_intersect($userRoles, ['admin', 'property_owner']));
        });

        Gate::define('manage-visitors', function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            return !empty(array_intersect($userRoles, ['admin', 'receptionist']));
        });
    }
}
