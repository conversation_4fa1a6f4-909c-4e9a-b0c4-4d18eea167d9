<?php

namespace App\Policies;

use App\Models\Property;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PropertyPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_properties') || $user->can('manage_own_properties');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Property $property): bool
    {
        // Admin can view all properties
        if ($user->can('view_properties')) {
            return true;
        }

        // Property owners can view their own properties
        if ($user->can('manage_own_properties') && $property->owner_id === $user->id) {
            return true;
        }

        // Property managers can view properties they manage
        if ($property->manager_id === $user->id) {
            return true;
        }

        // Tenants can view properties where they have units
        if ($user->isTenant() && $user->rentedUnits()->whereHas('property', function ($query) use ($property) {
            $query->where('id', $property->id);
        })->exists()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_properties') || $user->can('manage_own_properties');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Property $property): bool
    {
        // Admin can edit all properties
        if ($user->can('edit_properties')) {
            return true;
        }

        // Property owners can edit their own properties
        if ($user->can('manage_own_properties') && $property->owner_id === $user->id) {
            return true;
        }

        // Property managers can edit properties they manage
        if ($property->manager_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Property $property): bool
    {
        // Admin can delete all properties
        if ($user->can('delete_properties')) {
            return true;
        }

        // Property owners can delete their own properties
        if ($user->can('manage_own_properties') && $property->owner_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Property $property): bool
    {
        return $this->delete($user, $property);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Property $property): bool
    {
        return $user->can('delete_properties');
    }

    /**
     * Determine whether the user can view financial reports for the property.
     */
    public function viewFinancials(User $user, Property $property): bool
    {
        // Admin can view all financial reports
        if ($user->can('view_financial_reports')) {
            return true;
        }

        // Property owners can view financials for their own properties
        if ($user->can('manage_own_properties') && $property->owner_id === $user->id) {
            return true;
        }

        return false;
    }
}
