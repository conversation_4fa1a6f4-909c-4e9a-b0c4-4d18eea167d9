@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-task-line text-primary me-2"></i>Create New Task
                        </h4>
                        <p class="text-muted mb-0">
                            Create a new task with reminders and assignments
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Tasks
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('tasks.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Task Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title') }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Property and Unit -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select @error('property_id') is-invalid @enderror" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            @foreach($properties as $property)
                                                <option value="{{ $property->id }}" 
                                                        {{ old('property_id', $selectedProperty?->id) == $property->id ? 'selected' : '' }}>
                                                    {{ $property->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('property_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select @error('unit_id') is-invalid @enderror" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                            @if($selectedProperty)
                                                @foreach($selectedProperty->units as $unit)
                                                    <option value="{{ $unit->id }}" 
                                                            {{ old('unit_id', $selectedUnit?->id) == $unit->id ? 'selected' : '' }}>
                                                        Unit {{ $unit->unit_number }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                        @error('unit_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Task Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="inspection" {{ old('type') == 'inspection' ? 'selected' : '' }}>Inspection</option>
                                    <option value="contract_renewal" {{ old('type') == 'contract_renewal' ? 'selected' : '' }}>Contract Renewal</option>
                                    <option value="maintenance" {{ old('type') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                    <option value="lease_expiry" {{ old('type') == 'lease_expiry' ? 'selected' : '' }}>Lease Expiry</option>
                                    <option value="document_expiry" {{ old('type') == 'document_expiry' ? 'selected' : '' }}>Document Expiry</option>
                                    <option value="other" {{ old('type') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Priority -->
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="medium" {{ old('priority', 'medium') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Assigned To -->
                            <div class="mb-3">
                                <label for="assigned_to" class="form-label">Assign To</label>
                                <select class="form-select @error('assigned_to') is-invalid @enderror" 
                                        id="assigned_to" name="assigned_to">
                                    <option value="">Select Staff Member (Optional)</option>
                                    @foreach($staff as $member)
                                        <option value="{{ $member->id }}" 
                                                {{ old('assigned_to') == $member->id ? 'selected' : '' }}>
                                            {{ $member->name }}
                                            @if($member->roles->isNotEmpty())
                                                ({{ $member->roles->pluck('name')->join(', ') }})
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                @error('assigned_to')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Due Date -->
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="datetime-local" class="form-control @error('due_date') is-invalid @enderror" 
                                       id="due_date" name="due_date" value="{{ old('due_date') }}">
                                @error('due_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Recurring Task -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" value="1" 
                                           {{ old('is_recurring') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_recurring">
                                        Recurring Task
                                    </label>
                                </div>
                            </div>

                            <!-- Recurrence Pattern -->
                            <div class="mb-3" id="recurrence_pattern_group" style="display: none;">
                                <label for="recurrence_pattern" class="form-label">Recurrence Pattern</label>
                                <select class="form-select @error('recurrence_pattern') is-invalid @enderror" 
                                        id="recurrence_pattern" name="recurrence_pattern">
                                    <option value="">Select Pattern</option>
                                    <option value="daily" {{ old('recurrence_pattern') == 'daily' ? 'selected' : '' }}>Daily</option>
                                    <option value="weekly" {{ old('recurrence_pattern') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                    <option value="monthly" {{ old('recurrence_pattern') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="yearly" {{ old('recurrence_pattern') == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                </select>
                                @error('recurrence_pattern')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Reminder Settings -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Reminder Settings</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="reminder_days" class="form-label">Remind Before (Days)</label>
                                        <input type="number" class="form-control @error('reminder_days') is-invalid @enderror" 
                                               id="reminder_days" name="reminder_days" value="{{ old('reminder_days', 1) }}" min="1" max="365">
                                        @error('reminder_days')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="reminder_email" name="reminder_email" value="1" 
                                               {{ old('reminder_email') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="reminder_email">
                                            Send Email Reminder
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum datetime to current time
    const dueDateInput = document.getElementById('due_date');
    if (dueDateInput) {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        dueDateInput.min = now.toISOString().slice(0, 16);
    }

    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        
        // Clear unit options
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            // You would typically make an AJAX call here to get units
            // For now, we'll use the existing data
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);

            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    });

    // Handle recurring task checkbox
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurrencePatternGroup = document.getElementById('recurrence_pattern_group');

    isRecurringCheckbox.addEventListener('change', function() {
        if (this.checked) {
            recurrencePatternGroup.style.display = 'block';
        } else {
            recurrencePatternGroup.style.display = 'none';
            document.getElementById('recurrence_pattern').value = '';
        }
    });

    // Show recurrence pattern if already checked
    if (isRecurringCheckbox.checked) {
        recurrencePatternGroup.style.display = 'block';
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const isRecurring = document.getElementById('is_recurring').checked;
        const recurrencePattern = document.getElementById('recurrence_pattern').value;
        const dueDate = document.getElementById('due_date').value;

        if (isRecurring && !recurrencePattern) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Recurrence Pattern',
                text: 'Please select a recurrence pattern for recurring tasks.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (isRecurring && !dueDate) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Due Date',
                text: 'Due date is required for recurring tasks.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
@endsection
