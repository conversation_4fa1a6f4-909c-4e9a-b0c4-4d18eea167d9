@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-task-line text-primary me-2"></i>Task Details
                        </h4>
                        <p class="text-muted mb-0">
                            View and manage task information
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        @can('update', $task)
                            <a href="{{ route('tasks.edit', $task) }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Task
                            </a>
                        @endcan
                        <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Tasks
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Task Details -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ $task->title }}</h5>
                            <div class="d-flex gap-2">
                                <span class="badge bg-{{ $task->priority == 'urgent' ? 'danger' : ($task->priority == 'high' ? 'warning' : ($task->priority == 'medium' ? 'info' : 'secondary')) }}">
                                    {{ ucfirst($task->priority) }} Priority
                                </span>
                                <span class="badge bg-{{ $task->status == 'completed' ? 'success' : ($task->status == 'in_progress' ? 'primary' : ($task->status == 'cancelled' ? 'danger' : 'warning')) }}">
                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                </span>
                                @if($task->is_recurring)
                                    <span class="badge bg-info">
                                        <i class="ri-repeat-line me-1"></i>Recurring
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Description -->
                        @if($task->description)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Description</h6>
                                <p class="mb-0">{{ $task->description }}</p>
                            </div>
                        @endif

                        <!-- Property and Unit Information -->
                        @if($task->property || $task->unit)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Location</h6>
                                <div class="row">
                                    @if($task->property)
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-building-line text-primary me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Property</small>
                                                    <strong>{{ $task->property->name }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($task->unit)
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-home-line text-info me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Unit</small>
                                                    <strong>Unit {{ $task->unit->unit_number }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Notes -->
                        @if($task->notes)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Notes</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-0">{{ $task->notes }}</p>
                                </div>
                            </div>
                        @endif

                        <!-- Reminder Settings -->
                        @if($task->reminder_settings)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Reminder Settings</h6>
                                <div class="bg-light p-3 rounded">
                                    @if(isset($task->reminder_settings['days_before']))
                                        <p class="mb-1">
                                            <i class="ri-alarm-line text-warning me-2"></i>
                                            Remind {{ $task->reminder_settings['days_before'] }} day(s) before due date
                                        </p>
                                    @endif
                                    @if(isset($task->reminder_settings['email_enabled']) && $task->reminder_settings['email_enabled'])
                                        <p class="mb-0">
                                            <i class="ri-mail-line text-info me-2"></i>
                                            Email reminders enabled
                                        </p>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Recurring Information -->
                        @if($task->is_recurring)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Recurring Information</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-1">
                                        <i class="ri-repeat-line text-info me-2"></i>
                                        Recurrence: {{ ucfirst($task->recurrence_pattern) }}
                                    </p>
                                    @if($task->next_occurrence)
                                        <p class="mb-0">
                                            <i class="ri-calendar-line text-success me-2"></i>
                                            Next occurrence: {{ $task->next_occurrence->format('M d, Y H:i') }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Actions -->
                        <div class="d-flex gap-2 flex-wrap">
                            @can('update', $task)
                                @if($task->status !== 'completed')
                                    <form action="{{ route('tasks.complete', $task) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-success">
                                            <i class="ri-check-line me-1"></i>Mark as Completed
                                        </button>
                                    </form>
                                @endif
                                
                                <a href="{{ route('tasks.edit', $task) }}" class="btn btn-warning">
                                    <i class="ri-edit-line me-1"></i>Edit Task
                                </a>
                            @endcan
                            
                            @if($task->property)
                                <a href="{{ route('properties.show', $task->property) }}" class="btn btn-outline-primary">
                                    <i class="ri-building-line me-1"></i>View Property
                                </a>
                            @endif
                            
                            @if($task->unit)
                                <a href="{{ route('units.show', $task->unit) }}" class="btn btn-outline-info">
                                    <i class="ri-home-line me-1"></i>View Unit
                                </a>
                            @endif

                            @can('delete', $task)
                                <button type="button" class="btn btn-outline-danger delete-task" 
                                        data-task-id="{{ $task->id }}"
                                        data-task-title="{{ $task->title }}">
                                    <i class="ri-delete-bin-line me-1"></i>Delete Task
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>

                <!-- Related Notifications -->
                @if($task->notifications->count() > 0)
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Related Notifications</h5>
                        </div>
                        <div class="card-body">
                            @foreach($task->notifications as $notification)
                                <div class="d-flex align-items-center justify-content-between border-bottom pb-2 mb-2">
                                    <div>
                                        <h6 class="mb-1">{{ $notification->title }}</h6>
                                        <small class="text-muted">{{ $notification->message }}</small>
                                        <br><small class="text-info">{{ $notification->created_at->format('M d, Y H:i') }}</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ $notification->priority == 'urgent' ? 'danger' : ($notification->priority == 'high' ? 'warning' : 'info') }}">
                                            {{ ucfirst($notification->priority) }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Task Metadata -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Task Information</h5>
                    </div>
                    <div class="card-body">
                        <!-- Type -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Type</small>
                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $task->type)) }}</span>
                        </div>

                        <!-- Status -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Status</small>
                            <span class="badge bg-{{ $task->status == 'completed' ? 'success' : ($task->status == 'in_progress' ? 'primary' : ($task->status == 'cancelled' ? 'danger' : 'warning')) }}">
                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                            </span>
                            @if($task->is_overdue)
                                <br><small class="text-danger mt-1"><i class="ri-alarm-warning-line"></i> Overdue</small>
                            @elseif($task->is_due_today)
                                <br><small class="text-warning mt-1"><i class="ri-calendar-todo-line"></i> Due Today</small>
                            @elseif($task->is_upcoming)
                                <br><small class="text-info mt-1"><i class="ri-calendar-line"></i> Due Soon</small>
                            @endif
                        </div>

                        <!-- Priority -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Priority</small>
                            <span class="badge bg-{{ $task->priority == 'urgent' ? 'danger' : ($task->priority == 'high' ? 'warning' : ($task->priority == 'medium' ? 'info' : 'secondary')) }}">
                                {{ ucfirst($task->priority) }}
                            </span>
                        </div>

                        <!-- Due Date -->
                        @if($task->due_date)
                            <div class="mb-3">
                                <small class="text-muted d-block">Due Date</small>
                                <strong>{{ $task->due_date->format('M d, Y H:i') }}</strong>
                                @if($task->days_until_due !== null)
                                    <br><small class="text-muted">
                                        @if($task->days_until_due < 0)
                                            {{ abs($task->days_until_due) }} day(s) overdue
                                        @elseif($task->days_until_due == 0)
                                            Due today
                                        @else
                                            {{ $task->days_until_due }} day(s) remaining
                                        @endif
                                    </small>
                                @endif
                            </div>
                        @endif

                        <!-- Assigned To -->
                        @if($task->assignedTo)
                            <div class="mb-3">
                                <small class="text-muted d-block">Assigned To</small>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        @if($task->assignedTo->profile_photo)
                                            <img src="{{ asset('storage/' . $task->assignedTo->profile_photo) }}" 
                                                 alt="{{ $task->assignedTo->name }}" class="rounded-circle">
                                        @else
                                            <div class="avatar-initial rounded-circle bg-primary">
                                                {{ substr($task->assignedTo->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </div>
                                    <strong>{{ $task->assignedTo->name }}</strong>
                                </div>
                            </div>
                        @endif

                        <!-- Created By -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Created By</small>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2">
                                    @if($task->createdBy->profile_photo)
                                        <img src="{{ asset('storage/' . $task->createdBy->profile_photo) }}" 
                                             alt="{{ $task->createdBy->name }}" class="rounded-circle">
                                    @else
                                        <div class="avatar-initial rounded-circle bg-secondary">
                                            {{ substr($task->createdBy->name, 0, 1) }}
                                        </div>
                                    @endif
                                </div>
                                <strong>{{ $task->createdBy->name }}</strong>
                            </div>
                        </div>

                        <!-- Created Date -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Created</small>
                            <strong>{{ $task->created_at->format('M d, Y H:i') }}</strong>
                            <br><small class="text-muted">{{ $task->created_at->diffForHumans() }}</small>
                        </div>

                        <!-- Completed Date -->
                        @if($task->completed_at)
                            <div class="mb-3">
                                <small class="text-muted d-block">Completed</small>
                                <strong>{{ $task->completed_at->format('M d, Y H:i') }}</strong>
                                <br><small class="text-muted">{{ $task->completed_at->diffForHumans() }}</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete task functionality
    document.querySelectorAll('.delete-task').forEach(button => {
        button.addEventListener('click', function() {
            const taskId = this.dataset.taskId;
            const taskTitle = this.dataset.taskTitle;

            Swal.fire({
                title: 'Delete Task',
                html: `Are you sure you want to delete:<br><br><strong>${taskTitle}</strong>?<br><br><small class="text-danger">This action cannot be undone.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/tasks/${taskId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
