<?php

namespace App\Http\Controllers;

use App\Models\Visitor;
use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use App\Models\BlacklistedVisitor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class VisitorController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of visitors
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Build query based on user role
        if (in_array('admin', $userRoles)) {
            $query = Visitor::with(['visitingUnit.property', 'visitingTenant', 'property', 'checkedInBy', 'checkedOutBy']);
        } elseif (in_array('property_owner', $userRoles)) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Visitor::whereIn('property_id', $propertyIds)
                           ->with(['visitingUnit.property', 'visitingTenant', 'property', 'checkedInBy', 'checkedOutBy']);
        } else {
            // Receptionists and staff can see all visitors
            $query = Visitor::with(['visitingUnit.property', 'visitingTenant', 'property', 'checkedInBy', 'checkedOutBy']);
        }

        // Apply filters
        if ($request->filled('status')) {
            if ($request->status === 'checked_in') {
                $query->checkedIn();
            } elseif ($request->status === 'checked_out') {
                $query->checkedOut();
            }
        }

        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        if ($request->filled('date')) {
            $query->whereDate('check_in_time', $request->date);
        } else {
            // Default to today's visitors
            $query->today();
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('identification_number', 'like', "%{$search}%");
            });
        }

        $visitors = $query->orderBy('check_in_time', 'desc')->paginate(15);

        // Get statistics
        $stats = $this->getVisitorStats($user);

        // Get properties for filter
        if (in_array('admin', $userRoles)) {
            $properties = Property::all();
        } elseif (in_array('property_owner', $userRoles)) {
            $properties = $user->ownedProperties;
        } else {
            $properties = Property::all();
        }

        return view('visitors.index', compact('visitors', 'stats', 'properties'));
    }

    /**
     * Show the form for creating a new visitor
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Get properties based on user role
        if (in_array('admin', $userRoles)) {
            $properties = Property::with('units')->get();
        } elseif (in_array('property_owner', $userRoles)) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            $properties = Property::with('units')->get();
        }

        // Get tenants
        $tenants = User::whereHas('roles', function($q) {
            $q->where('name', 'tenant');
        })->get();

        return view('visitors.create', compact('properties', 'tenants'));
    }

    /**
     * Store a newly created visitor
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'identification_type' => 'required|in:national_id,passport,driver_license,other',
            'identification_number' => 'required|string|max:50',
            'purpose_of_visit' => 'required|string|max:255',
            'property_id' => 'required|exists:properties,id',
            'visiting_unit_id' => 'nullable|exists:units,id',
            'visiting_tenant_id' => 'nullable|exists:users,id',
            'vehicle_number' => 'nullable|string|max:20',
            'emergency_contact' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
        ]);

        // Check if visitor is blacklisted
        $blacklisted = BlacklistedVisitor::where('identification_number', $validated['identification_number'])
                                       ->orWhere('phone', $validated['phone'])
                                       ->first();

        if ($blacklisted) {
            return back()->withErrors(['identification_number' => 'This visitor is blacklisted and cannot be checked in.']);
        }

        // Set check-in time and user
        $validated['check_in_time'] = now();
        $validated['checked_in_by'] = Auth::id();
        $validated['status'] = 'checked_in';

        $visitor = Visitor::create($validated);

        return redirect()->route('visitors.show', $visitor)
                        ->with('success', 'Visitor checked in successfully!');
    }

    /**
     * Display the specified visitor
     */
    public function show(Visitor $visitor)
    {
        $visitor->load(['visitingUnit.property', 'visitingTenant', 'property', 'checkedInBy', 'checkedOutBy']);
        
        return view('visitors.show', compact('visitor'));
    }

    /**
     * Show the form for editing the visitor
     */
    public function edit(Visitor $visitor)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Get properties based on user role
        if (in_array('admin', $userRoles)) {
            $properties = Property::with('units')->get();
        } elseif (in_array('property_owner', $userRoles)) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            $properties = Property::with('units')->get();
        }

        // Get tenants
        $tenants = User::whereHas('roles', function($q) {
            $q->where('name', 'tenant');
        })->get();

        return view('visitors.edit', compact('visitor', 'properties', 'tenants'));
    }

    /**
     * Update the specified visitor
     */
    public function update(Request $request, Visitor $visitor)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'identification_type' => 'required|in:national_id,passport,driver_license,other',
            'identification_number' => 'required|string|max:50',
            'purpose_of_visit' => 'required|string|max:255',
            'property_id' => 'required|exists:properties,id',
            'visiting_unit_id' => 'nullable|exists:units,id',
            'visiting_tenant_id' => 'nullable|exists:users,id',
            'vehicle_number' => 'nullable|string|max:20',
            'emergency_contact' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
        ]);

        $visitor->update($validated);

        return redirect()->route('visitors.show', $visitor)
                        ->with('success', 'Visitor information updated successfully!');
    }

    /**
     * Check out a visitor
     */
    public function checkOut(Visitor $visitor)
    {
        if ($visitor->check_out_time) {
            return back()->withErrors(['error' => 'Visitor is already checked out.']);
        }

        $visitor->update([
            'check_out_time' => now(),
            'checked_out_by' => Auth::id(),
            'status' => 'checked_out'
        ]);

        return back()->with('success', 'Visitor checked out successfully!');
    }

    /**
     * Get visitor statistics
     */
    private function getVisitorStats($user)
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('admin', $userRoles)) {
            $query = Visitor::query();
        } elseif (in_array('property_owner', $userRoles)) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Visitor::whereIn('property_id', $propertyIds);
        } else {
            $query = Visitor::query();
        }

        return [
            'today_visitors' => $query->clone()->today()->count(),
            'checked_in' => $query->clone()->checkedIn()->count(),
            'total_today' => $query->clone()->today()->count(),
            'this_week' => $query->clone()->whereBetween('check_in_time', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count(),
            'this_month' => $query->clone()->whereBetween('check_in_time', [
                Carbon::now()->startOfMonth(),
                Carbon::now()->endOfMonth()
            ])->count(),
        ];
    }

    /**
     * Visitor dashboard for receptionists
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get today's visitors
        $todayVisitors = Visitor::today()
                               ->with(['visitingUnit.property', 'visitingTenant', 'property'])
                               ->orderBy('check_in_time', 'desc')
                               ->get();

        // Get currently checked in visitors
        $checkedInVisitors = Visitor::checkedIn()
                                   ->with(['visitingUnit.property', 'visitingTenant', 'property'])
                                   ->orderBy('check_in_time', 'desc')
                                   ->get();

        // Get statistics
        $stats = $this->getVisitorStats($user);

        return view('visitors.dashboard', compact('todayVisitors', 'checkedInVisitors', 'stats'));
    }

    /**
     * Quick check-in form
     */
    public function quickCheckIn(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'identification_number' => 'required|string|max:50',
            'purpose_of_visit' => 'required|string|max:255',
            'property_id' => 'required|exists:properties,id',
            'visiting_unit_id' => 'nullable|exists:units,id',
        ]);

        // Check if visitor is blacklisted
        $blacklisted = BlacklistedVisitor::where('identification_number', $validated['identification_number'])
                                       ->orWhere('phone', $validated['phone'])
                                       ->first();

        if ($blacklisted) {
            return response()->json(['error' => 'This visitor is blacklisted and cannot be checked in.'], 422);
        }

        $validated['check_in_time'] = now();
        $validated['checked_in_by'] = Auth::id();
        $validated['status'] = 'checked_in';
        $validated['identification_type'] = 'national_id'; // Default

        $visitor = Visitor::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Visitor checked in successfully!',
            'visitor' => $visitor->load(['visitingUnit.property', 'property'])
        ]);
    }
}
