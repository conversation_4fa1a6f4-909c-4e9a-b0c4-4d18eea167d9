<?php

use App\Helpers\UrlObfuscator;

if (!function_exists('obfuscated_route')) {
    /**
     * Generate an obfuscated route URL with extremely long and complex parameters
     */
    function obfuscated_route($name, $parameters = [], $absolute = true)
    {
        // Generate the base route
        $url = route($name, $parameters, $absolute);
        
        // Add multiple layers of obfuscation parameters
        $obfuscationParams = [
            // Session-like parameters
            'session_id' => hash('sha256', time() . mt_rand()),
            'csrf_token' => bin2hex(random_bytes(32)),
            'request_id' => uniqid('req_', true) . '_' . bin2hex(random_bytes(16)),
            
            // Tracking parameters
            'utm_source' => 'internal_' . bin2hex(random_bytes(8)),
            'utm_medium' => 'web_' . bin2hex(random_bytes(8)),
            'utm_campaign' => 'app_' . bin2hex(random_bytes(12)),
            'utm_content' => bin2hex(random_bytes(20)),
            'utm_term' => bin2hex(random_bytes(16)),
            
            // Cache and version parameters
            'cache_buster' => time() . '_' . mt_rand(100000, 999999),
            'version' => 'v' . date('YmdHis') . '_' . bin2hex(random_bytes(8)),
            'build' => 'b' . hash('crc32', time()) . '_' . bin2hex(random_bytes(6)),
            
            // Security-like parameters
            'signature' => hash('sha512', time() . bin2hex(random_bytes(32))),
            'nonce' => base64_encode(random_bytes(32)),
            'state' => bin2hex(random_bytes(24)),
            'challenge' => base64_encode(hash('sha256', time() . bin2hex(random_bytes(16)), true)),
            
            // Timestamp parameters
            'timestamp' => time(),
            'microtime' => microtime(true),
            'timezone' => date('T'),
            'offset' => date('Z'),
            
            // Random data parameters
            'data1' => base64_encode(random_bytes(40)),
            'data2' => bin2hex(random_bytes(32)),
            'data3' => base64_encode(random_bytes(28)),
            'data4' => bin2hex(random_bytes(24)),
            'data5' => base64_encode(random_bytes(36)),
            
            // Additional complexity
            'ref' => hash('md5', time() . bin2hex(random_bytes(16))),
            'src' => 'app_' . bin2hex(random_bytes(12)),
            'medium' => 'internal_' . bin2hex(random_bytes(10)),
            'context' => base64_encode(json_encode([
                'time' => time(),
                'rand' => bin2hex(random_bytes(16)),
                'hash' => hash('sha256', time() . bin2hex(random_bytes(8)))
            ])),
            
            // Even more parameters to make it extremely long
            'meta1' => bin2hex(random_bytes(20)),
            'meta2' => base64_encode(random_bytes(30)),
            'meta3' => hash('sha384', time() . bin2hex(random_bytes(20))),
            'meta4' => bin2hex(random_bytes(25)),
            'meta5' => base64_encode(random_bytes(35)),
            
            // Final layer of complexity
            'checksum' => hash('sha256', $url . time()),
            'validation' => base64_encode(hash('sha512', time() . $url, true)),
            'verification' => bin2hex(hash('sha256', time() . mt_rand(), true)),
        ];
        
        // Build the query string
        $separator = strpos($url, '?') !== false ? '&' : '?';
        $queryString = http_build_query($obfuscationParams);
        
        return $url . $separator . $queryString;
    }
}

if (!function_exists('ultra_obfuscated_url')) {
    /**
     * Generate an ultra-obfuscated URL that's extremely long and complex
     */
    function ultra_obfuscated_url($model, $action = 'show', $additionalParams = [])
    {
        // Get the obfuscated route key
        $routeKey = $model->getRouteKey();
        
        // Determine the route name
        $modelName = strtolower(class_basename($model));
        $routeName = $modelName . 's.' . $action;
        
        // Merge parameters
        $parameters = array_merge([$routeKey], $additionalParams);
        
        // Generate the ultra-obfuscated URL
        return obfuscated_route($routeName, $parameters);
    }
}

if (!function_exists('generate_complex_id')) {
    /**
     * Generate an extremely complex ID-like string
     */
    function generate_complex_id($length = 128)
    {
        $segments = [
            bin2hex(random_bytes(16)),
            base64_encode(random_bytes(24)),
            hash('sha256', time() . mt_rand()),
            bin2hex(random_bytes(12)),
            base64_encode(random_bytes(18)),
            hash('md5', microtime(true) . mt_rand()),
            bin2hex(random_bytes(20)),
        ];
        
        $combined = implode('', $segments);
        
        // Ensure it's at least the requested length
        while (strlen($combined) < $length) {
            $combined .= bin2hex(random_bytes(16));
        }
        
        return substr($combined, 0, $length);
    }
}

if (!function_exists('obfuscate_all_urls')) {
    /**
     * Obfuscate all URLs in a given HTML content
     */
    function obfuscate_all_urls($content)
    {
        // Pattern to match URLs in href and action attributes
        $patterns = [
            '/href="([^"]*)"/',
            '/action="([^"]*)"/',
            '/src="([^"]*)"/',
        ];
        
        foreach ($patterns as $pattern) {
            $content = preg_replace_callback($pattern, function ($matches) {
                $url = $matches[1];
                
                // Skip external URLs and assets
                if (strpos($url, 'http') === 0 || strpos($url, 'assets/') !== false) {
                    return $matches[0];
                }
                
                // Add obfuscation parameters
                $separator = strpos($url, '?') !== false ? '&' : '?';
                $obfuscationParams = [
                    'ref' => bin2hex(random_bytes(8)),
                    'token' => bin2hex(random_bytes(16)),
                    'session' => bin2hex(random_bytes(12)),
                    'cache' => time() . '_' . mt_rand(10000, 99999),
                    'sig' => hash('md5', $url . time()),
                ];
                
                $queryString = http_build_query($obfuscationParams);
                $obfuscatedUrl = $url . $separator . $queryString;
                
                return str_replace($url, $obfuscatedUrl, $matches[0]);
            }, $content);
        }
        
        return $content;
    }
}

if (!function_exists('get_company_logo')) {
    /**
     * Get company logo URL for header or login
     */
    function get_company_logo($type = 'header', $fallback = null)
    {
        $setting = \App\Models\Setting::where('category', 'company')
                                     ->where('key', $type . '_logo')
                                     ->first();

        if ($setting && $setting->value && \Storage::disk('public')->exists($setting->value)) {
            return asset('storage/' . $setting->value);
        }

        return $fallback ?: asset('assets/images/logo.svg');
    }
}

if (!function_exists('get_company_setting')) {
    /**
     * Get company setting value
     */
    function get_company_setting($key, $default = null)
    {
        $setting = \App\Models\Setting::where('category', 'company')
                                     ->where('key', $key)
                                     ->first();

        return $setting ? $setting->value : $default;
    }
}
