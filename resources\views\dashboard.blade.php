@extends('layouts.base')
@section('content')

            <!-- Welcome Section -->
            <div class="row gx-4 mb-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                      <div>
                        <h4 class="mb-1">Welcome back, {{ auth()->user()->name }}!</h4>
                        <p class="text-muted mb-0">
                          You are logged in as:
                          <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', auth()->user()->getRoleNames()->first() ?? 'User')) }}</span>
                        </p>
                      </div>
                      <div>
                        <form action="{{ route('logout') }}" method="POST" class="d-inline">
                          @csrf
                          <button type="submit" class="btn btn-outline-danger">
                            <i class="ri-logout-box-line me-1"></i>Logout
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Row starts -->
            <div class="row gx-4">
              <div class="col-xxl-9 col-sm-12">

                <!-- Row starts -->
                <div class="row gx-4">
                  <div class="col-lg-4 col-sm-6 col-12">
                    <div class="card mb-4">
                      <div class="card-body">
                        <div class="d-flex align-items-center">
                          <div class="p-2 border border-primary rounded-circle me-3">
                            <div class="icon-box md bg-primary-lighten rounded-5">
                              <i class="ri-building-line fs-4 text-primary"></i>
                            </div>
                          </div>
                          <div class="d-flex flex-column">
                            <h2 class="lh-1">{{ \App\Models\Property::count() }}</h2>
                            <p class="m-0">Properties</p>
                          </div>
                        </div>
                        <div class="d-flex gap-2 flex-wrap align-items-center justify-content-between mt-2">
                          <div class="text-start">
                            <p class="mb-0 text-primary">{{ \App\Models\Property::where('status', 'active')->count() }}</p>
                            <span class="badge bg-primary-light text-primary small">active</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-sm-6 col-12">
                    <div class="card mb-4">
                      <div class="card-body">
                        <div class="d-flex align-items-center">
                          <div class="p-2 border border-primary rounded-circle me-3">
                            <div class="icon-box md bg-primary-lighten rounded-5">
                              <i class="ri-home-line fs-4 text-primary"></i>
                            </div>
                          </div>
                          <div class="d-flex flex-column">
                            <h2 class="lh-1">{{ \App\Models\Unit::count() }}</h2>
                            <p class="m-0">Units</p>
                          </div>
                        </div>
                        <div class="d-flex gap-2 flex-wrap align-items-center justify-content-between mt-2">
                          <div class="text-start">
                            <p class="mb-0 text-primary">{{ \App\Models\Unit::where('status', 'available')->count() }}</p>
                            <span class="badge bg-success-light text-success small">available</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-4 col-sm-12 col-12">
                    <div class="card mb-4">
                      <div class="card-body">
                        <div class="d-flex align-items-center">
                          <div class="p-2 border border-primary rounded-circle me-3">
                            <div class="icon-box md bg-primary-lighten rounded-5">
                              <i class="ri-customer-service-line fs-4 text-primary"></i>
                            </div>
                          </div>
                          <div class="d-flex flex-column">
                            <h2 class="lh-1">{{ \App\Models\Complaint::count() }}</h2>
                            <p class="m-0">Complaints</p>
                          </div>
                        </div>
                        <div class="d-flex gap-2 flex-wrap align-items-center justify-content-between mt-2">
                          <div class="text-start">
                            <p class="mb-0 text-primary">{{ \App\Models\Complaint::where('status', 'open')->count() }}</p>
                            <span class="badge bg-warning-light text-warning small">open</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Row ends -->

                <!-- Row starts -->
                <div class="row gx-4">
                  <div class="col-sm-12">
                    <div class="card mb-4">
                      <div class="card-header">
                        <h5 class="card-title">Available Doctors</h5>
                      </div>
                      <div class="card-body pt-0">

                        <!-- Row starts -->
                        <div class="row g-4">
                          <div class="col-xl-4 col-sm-6 col-12">
                            <a href="doctors-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                              <img src="assets/images/doctor1.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                              <div class="d-flex gap-1 flex-column flex-fill">
                                <div class="fw-semibold">Dr. Lillie Kennedy</div>
                                <div class="text-muted small">Periodontist - 9 yrs</div>
                                <div class="small">
                                  <i class="ri-star-fill text-primary me-1"></i>5.0
                                </div>
                              </div>
                              <i class="ri-arrow-right-s-line text-primary fs-1 opacity-25"></i>
                            </a>
                          </div>
                          <div class="col-xl-4 col-sm-6 col-12">
                            <a href="doctors-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                              <img src="assets/images/doctor3.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                              <div class="d-flex gap-1 flex-column flex-fill">
                                <div class="fw-semibold">Dr. Kerri Myers</div>
                                <div class="text-muted small">Endodontist - 6 yrs</div>
                                <div class="small">
                                  <i class="ri-star-fill text-primary me-1"></i>4.9
                                </div>
                              </div>
                              <i class="ri-arrow-right-s-line text-primary fs-1 opacity-25"></i>
                            </a>
                          </div>
                          <div class="col-xl-4 col-sm-6 col-12">
                            <a href="doctors-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                              <img src="assets/images/doctor4.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                              <div class="d-flex gap-1 flex-column flex-fill">
                                <div class="fw-semibold">Dr. Tobias Wong</div>
                                <div class="text-muted small">Orthodontist - 8 yrs</div>
                                <div class="small">
                                  <i class="ri-star-fill text-primary me-1"></i>4.8
                                </div>
                              </div>
                              <i class="ri-arrow-right-s-line text-primary fs-1 opacity-25"></i>
                            </a>
                          </div>
                        </div>
                        <!-- Row ends -->

                      </div>
                    </div>
                  </div>
                  <div class="col-sm-12">
                    <div class="card mb-4">
                      <div class="card-header pb-0 d-flex align-items-center justify-content-between">
                        <h5 class="card-title">Revenue</h5>
                        <div class="btn-group btn-group-sm" role="group">
                          <button type="button" class="btn btn-primary">2024</button>
                          <button type="button" class="btn btn-outline-primary">2023</button>
                          <button type="button" class="btn btn-outline-primary">2022</button>
                        </div>
                      </div>
                      <div class="card-body pt-0">
                        <div class="overflow-hidden">
                          <div id="income"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Row ends -->

              </div>
              <div class="col-xxl-3 col-sm-12">
                <div class="card mb-4">
                  <div class="card-header pb-0">
                    <h5 class="card-title">Appointments</h5>
                  </div>
                  <div class="card-body">

                    <!-- Date calendar starts -->
                    <div class="datepicker-bg d-flex justify-content-center align-items-center mb-3">
                      <!-- Loader starts -->
                      <div id="datepicker-loader" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                          <span class="visually-hidden">Loading...</span>
                        </div>
                      </div>
                      <!-- Loader ends -->
                      <div id="datepicker" class="d-none w-100"></div>
                    </div>
                    <!-- Date calendar ends -->

                    <!-- Appointments starts -->
                    <div class="mb-4">
                      <div class="scroll300">

                        <!-- Grid starts -->
                        <div class="d-grid gap-2">
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient1.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Kitty Miller</div>
                              <div class="text-muted small">Consultation</div>
                            </div>
                            <span class="badge bg-danger">8:00</span>
                          </a>
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient2.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Anne Wallace</div>
                              <div class="text-muted small">Medication</div>
                            </div>
                            <span class="badge bg-success">9:00</span>
                          </a>
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient3.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Lesley Chaney</div>
                              <div class="text-muted small">Laboratory</div>
                            </div>
                            <span class="badge bg-warning">9:00</span>
                          </a>
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient5.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Darcy May</div>
                              <div class="text-muted small">Emergency</div>
                            </div>
                            <span class="badge bg-primary">9:30</span>
                          </a>
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient4.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Monroe Barron</div>
                              <div class="text-muted small">Emergency</div>
                            </div>
                            <span class="badge bg-warning">9:30</span>
                          </a>
                          <a href="patient-profile.html" class="d-flex align-items-center gap-3 appointment-card">
                            <img src="assets/images/patient.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                            <div class="d-flex flex-column flex-fill">
                              <div class="fw-semibold text-truncate">Allen Roth</div>
                              <div class="text-muted small">Appointment</div>
                            </div>
                            <span class="badge bg-danger">9:00</span>
                          </a>
                        </div>
                        <!-- Grid ends -->

                      </div>
                    </div>
                    <!-- Appointments ends -->

                    <!-- Available doctor starts -->
                    <div class="available-doc">
                      <a href="doctors-profile.html">
                        <div class="d-flex align-items-center gap-3 text-white">
                          <img src="assets/images/doctor4.png" class="img-3x rounded-5" alt="Doctor Dashboard">
                          <div class="d-flex flex-column flex-fill">
                            <div class="fw-semibold">Dr. Tobias Wong</div>
                            <div class="small">Dentist</div>
                          </div>
                        </div>
                        <div class="timing mt-2 text-white small">
                          <span class="day"></span> <span class="today-date"></span>, 9AM - 2PM
                        </div>
                      </a>
                    </div>
                    <!-- Available doctor ends -->

                  </div>
                </div>
              </div>
            </div>
            <!-- Row ends -->

            <!-- Row starts -->
            <div class="row gx-4">
              <div class="col-xxl-9 col-sm-12">
                <div class="card mb-4">
                  <div class="card-header">
                    <h5 class="card-title">Patients</h5>
                  </div>
                  <div class="card-body pt-0">
                    <div class="d-flex gap-4 flex-wrap">
                      <div class="div">
                        <h6 class="mb-0">New Patients</h6>
                        <div class="d-flex align-items-center gap-1">
                          <div class="fs-2 fw-semibold">2000</div>
                          <div class="d-flex">
                            <div>20.2%</div>
                            <i class="ri-arrow-right-up-line text-success"></i>
                          </div>
                        </div>
                        <span
                          class="monthDisplay badge bg-primary-subtle text-primary border border-primary small"></span>
                      </div>
                      <div class="div">
                        <h6 class="mb-0">Return Patients</h6>
                        <div class="d-flex align-items-center gap-1">
                          <div class="fs-2 fw-semibold">6000</div>
                          <div class="d-flex">
                            <div>22.8%</div>
                            <i class="ri-arrow-right-up-line text-success"></i>
                          </div>
                        </div>
                        <span
                          class="monthDisplay badge bg-primary-subtle text-primary border border-primary small"></span>
                      </div>
                      <div class="div">
                        <h6 class="mb-0">Male Patients</h6>
                        <div class="d-flex align-items-center gap-1">
                          <div class="fs-2 fw-semibold">3000</div>
                          <div class="d-flex">
                            <div>38.9%</div>
                            <i class="ri-arrow-right-up-line text-success"></i>
                          </div>
                        </div>
                        <span
                          class="monthDisplay badge bg-primary-subtle text-primary border border-primary small"></span>
                      </div>
                      <div class="div">
                        <h6 class="mb-0">Female Patients</h6>
                        <div class="d-flex align-items-center gap-1">
                          <div class="fs-2 fw-semibold">4000</div>
                          <div class="d-flex">
                            <div>49.3%</div>
                            <i class="ri-arrow-right-up-line text-success"></i>
                          </div>
                        </div>
                        <span
                          class="monthDisplay badge bg-primary-subtle text-primary border border-primary small"></span>
                      </div>
                    </div>
                    <div class="overflow-hidden">
                      <div id="patients"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-xxl-3 col-sm-12">
                <div class="card mb-4">
                  <div class="card-header">
                    <h5 class="card-title">Overview</h5>
                  </div>
                  <div class="card-body">
                    <div class="overflow-hidden">
                      <div class="auto-align-graph">
                        <div id="overview"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Row ends -->

            <!-- Row starts -->
            <div class="row gx-4">
              <div class="col-sm-12">
                <div class="card mb-4">
                  <div class="card-header">
                    <h5 class="card-title">Income By Department</h5>
                  </div>
                  <div class="card-body">
                    <div class="overflow-hidden">
                      <div id="departments"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Row ends -->

            <!-- Row starts -->
            <div class="row gx-4">
              <div class="col-sm-12">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title">Patient Visits</h5>
                  </div>
                  <div class="card-body pt-0">

                    <!-- Table starts -->
                    <div class="table-responsive">
                      <table id="hideSearchExample" class="table m-0 align-middle">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>Patient Name</th>
                            <th>Age</th>
                            <th>Date of Birth</th>
                            <th>Diagnosis</th>
                            <th>Type</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>001</td>
                            <td>
                              <img src="assets/images/patient.png" class="img-2x rounded-5 me-1"
                                alt="Doctors Admin Template">
                              Willian Mathews
                            </td>
                            <td>21</td>
                            <td>
                              20/06/2010
                            </td>
                            <td>Implant</td>
                            <td>
                              <span class="badge bg-danger-subtle text-danger fs-6">Emergency</span>
                            </td>
                            <td>
                              <div class="d-inline-flex gap-1">
                                <button type="button" class="btn btn-hover btn-sm rounded-5" data-bs-toggle="modal"
                                  data-bs-target="#delRow">
                                  <span data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete Patient Details">
                                    <i class="ri-delete-bin-line"></i>
                                  </span>
                                </button>
                                <a href="edit-patient.html" class="btn btn-hover btn-sm rounded-5"
                                  data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Edit Patient Details">
                                  <i class="ri-edit-box-line"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>002</td>
                            <td>
                              <img src="assets/images/patient1.png" class="img-2x rounded-5 me-1"
                                alt="Doctors Admin Template">
                              Adam Bradley
                            </td>
                            <td>36</td>
                            <td>
                              24/09/2002
                            </td>
                            <td>Periodontics</td>
                            <td>
                              <span class="badge bg-primary-subtle text-primary fs-6">Non Urgent</span>
                            </td>
                            <td>
                              <div class="d-inline-flex gap-1">
                                <button type="button" class="btn btn-hover btn-sm rounded-5" data-bs-toggle="modal"
                                  data-bs-target="#delRow">
                                  <span data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete Patient Details">
                                    <i class="ri-delete-bin-line"></i>
                                  </span>
                                </button>
                                <a href="edit-patient.html" class="btn btn-hover btn-sm rounded-5"
                                  data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Edit Patient Details">
                                  <i class="ri-edit-box-line"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>003</td>
                            <td>
                              <img src="assets/images/patient2.png" class="img-2x rounded-5 me-1"
                                alt="Doctors Admin Template">
                              Mayra Hatfield
                            </td>
                            <td>82</td>
                            <td>
                              22/02/2007
                            </td>
                            <td>Root Canal</td>
                            <td>
                              <span class="badge bg-warning-subtle text-warning fs-6">Out Patient</span>
                            </td>
                            <td>
                              <div class="d-inline-flex gap-1">
                                <button type="button" class="btn btn-hover btn-sm rounded-5" data-bs-toggle="modal"
                                  data-bs-target="#delRow">
                                  <span data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete Patient Details">
                                    <i class="ri-delete-bin-line"></i>
                                  </span>
                                </button>
                                <a href="edit-patient.html" class="btn btn-hover btn-sm rounded-5"
                                  data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Edit Patient Details">
                                  <i class="ri-edit-box-line"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>004</td>
                            <td>
                              <img src="assets/images/patient3.png" class="img-2x rounded-5 me-1"
                                alt="Doctors Admin Template">
                              Nicole Sellers
                            </td>
                            <td>29</td>
                            <td>
                              28/09/1996
                            </td>
                            <td>Dentures</td>
                            <td>
                              <span class="badge bg-info-subtle text-info fs-6">Discharge</span>
                            </td>
                            <td>
                              <div class="d-inline-flex gap-1">
                                <button type="button" class="btn btn-hover btn-sm rounded-5" data-bs-toggle="modal"
                                  data-bs-target="#delRow">
                                  <span data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete Patient Details">
                                    <i class="ri-delete-bin-line"></i>
                                  </span>
                                </button>
                                <a href="edit-patient.html" class="btn btn-hover btn-sm rounded-5"
                                  data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Edit Patient Details">
                                  <i class="ri-edit-box-line"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>005</td>
                            <td>
                              <img src="assets/images/patient4.png" class="img-2x rounded-5 me-1"
                                alt="Doctors Admin Template">
                              Roseann Kane
                            </td>
                            <td>58</td>
                            <td>
                              30/03/1989
                            </td>
                            <td>Implant</td>
                            <td>
                              <span class="badge bg-danger-subtle text-danger fs-6">Urgent</span>
                            </td>
                            <td>
                              <div class="d-inline-flex gap-1">
                                <button type="button" class="btn btn-hover btn-sm rounded-5" data-bs-toggle="modal"
                                  data-bs-target="#delRow">
                                  <span data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete Patient Details">
                                    <i class="ri-delete-bin-line"></i>
                                  </span>
                                </button>
                                <a href="edit-patient.html" class="btn btn-hover btn-sm rounded-5"
                                  data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Edit Patient Details">
                                  <i class="ri-edit-box-line"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <!-- Table ends -->

                    <!-- Modal Delete Row -->
                    <div class="modal fade" id="delRow" tabindex="-1" aria-labelledby="delRowLabel" aria-hidden="true">
                      <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="delRowLabel">
                              Confirm
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            Are you sure you want to delete the patient details?
                          </div>
                          <div class="modal-footer">
                            <div class="d-flex justify-content-end gap-2">
                              <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"
                                aria-label="Close">No</button>
                              <button type="button" class="btn btn-danger" data-bs-dismiss="modal"
                                aria-label="Close">Yes</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
            <!-- Row ends -->

@endsection