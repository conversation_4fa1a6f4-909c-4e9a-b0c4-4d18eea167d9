<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ComplaintController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Complaint::with(['complainant', 'property', 'unit', 'assignedTo']);
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Complaint::whereIn('property_id', $propertyIds)->with(['complainant', 'property', 'unit', 'assignedTo']);
        } elseif ($user->hasRole('maintenance_staff')) {
            $query = Complaint::where('assigned_to', $user->id)->with(['complainant', 'property', 'unit']);
        } else {
            // Tenants see only their complaints
            $query = Complaint::where('complainant_id', $user->id)->with(['property', 'unit', 'assignedTo']);
        }

        // Apply filters
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('complainant', function ($tq) use ($search) {
                      $tq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $complaints = $query->latest()->paginate(15);

        // Calculate statistics
        $stats = [
            'total' => $query->count(),
            'open' => $query->where('status', 'open')->count(),
            'in_progress' => $query->where('status', 'in_progress')->count(),
            'resolved' => $query->where('status', 'resolved')->count(),
            'high_priority' => $query->where('priority', 'high')->count(),
        ];

        // Get filter options
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties;
        } else {
            $properties = collect();
        }

        return view('complaints.index', compact('complaints', 'properties', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Complaint::class);
        
        $user = Auth::user();
        
        // Get properties and units based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            // Tenants can only create complaints for their units
            $properties = Property::whereHas('units', function ($q) use ($user) {
                $q->where('tenant_id', $user->id);
            })->with(['units' => function ($q) use ($user) {
                $q->where('tenant_id', $user->id);
            }])->get();
        }

        // Get maintenance staff for assignment
        $maintenanceStaff = User::whereHas('roles', function ($q) {
            $q->where('name', 'maintenance_staff');
        })->where('is_active', true)->get();

        $selectedProperty = $request->property_id ? Property::find($request->property_id) : null;
        $selectedUnit = $request->unit_id ? Unit::find($request->unit_id) : null;

        return view('complaints.create', compact('properties', 'maintenanceStaff', 'selectedProperty', 'selectedUnit'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Complaint::class);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|in:maintenance,plumbing,electrical,hvac,cleaning,security,noise,other',
            'priority' => 'required|in:low,medium,high,urgent',
            'property_id' => 'required|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'assigned_to' => 'nullable|exists:users,id',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120',
        ]);

        $validated['complainant_id'] = Auth::id();
        $validated['status'] = 'open';

        // Handle file uploads
        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('complaint_attachments', 'public');
                $attachments[] = [
                    'filename' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                ];
            }
        }
        $validated['attachments'] = $attachments;

        $complaint = Complaint::create($validated);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint submitted successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Complaint $complaint)
    {
        $this->authorize('view', $complaint);

        $complaint->load(['complainant', 'property', 'unit', 'assignedTo']);

        return view('complaints.show', compact('complaint'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Complaint $complaint)
    {
        $this->authorize('update', $complaint);
        
        $user = Auth::user();
        
        // Get properties and units based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units')->get();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties()->with('units')->get();
        } else {
            $properties = Property::whereHas('units', function ($q) use ($user) {
                $q->where('tenant_id', $user->id);
            })->with(['units' => function ($q) use ($user) {
                $q->where('tenant_id', $user->id);
            }])->get();
        }

        // Get maintenance staff for assignment
        $maintenanceStaff = User::whereHas('roles', function ($q) {
            $q->where('name', 'maintenance_staff');
        })->where('is_active', true)->get();

        return view('complaints.edit', compact('complaint', 'properties', 'maintenanceStaff'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Complaint $complaint)
    {
        $this->authorize('update', $complaint);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|in:maintenance,plumbing,electrical,hvac,cleaning,security,noise,other',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:open,in_progress,resolved,closed',
            'property_id' => 'required|exists:properties,id',
            'unit_id' => 'nullable|exists:units,id',
            'assigned_to' => 'nullable|exists:users,id',
            'resolution_notes' => 'nullable|string',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120',
        ]);

        // Handle file uploads
        $existingAttachments = $complaint->attachments ?? [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('complaint_attachments', 'public');
                $existingAttachments[] = [
                    'filename' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                ];
            }
        }
        $validated['attachments'] = $existingAttachments;

        // Set resolved_at timestamp if status is resolved
        if ($validated['status'] === 'resolved' && $complaint->status !== 'resolved') {
            $validated['resolved_at'] = now();
        } elseif ($validated['status'] !== 'resolved') {
            $validated['resolved_at'] = null;
        }

        $complaint->update($validated);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Complaint $complaint)
    {
        $this->authorize('delete', $complaint);

        // Delete associated files
        if ($complaint->attachments) {
            foreach ($complaint->attachments as $attachment) {
                if (isset($attachment['path'])) {
                    Storage::disk('public')->delete($attachment['path']);
                }
            }
        }

        $complaint->delete();

        return redirect()->route('complaints.index')
            ->with('success', 'Complaint deleted successfully!');
    }

    /**
     * Assign complaint to maintenance staff
     */
    public function assign(Request $request, Complaint $complaint)
    {
        $this->authorize('update', $complaint);

        $validated = $request->validate([
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string',
        ]);

        $complaint->update([
            'assigned_to' => $validated['assigned_to'],
            'status' => 'in_progress',
        ]);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint assigned successfully!');
    }

    /**
     * Update complaint status
     */
    public function updateStatus(Request $request, Complaint $complaint)
    {
        $this->authorize('update', $complaint);

        $validated = $request->validate([
            'status' => 'required|in:open,in_progress,resolved,closed',
            'resolution_notes' => 'nullable|string',
        ]);

        // Set resolved_at timestamp if status is resolved
        if ($validated['status'] === 'resolved' && $complaint->status !== 'resolved') {
            $validated['resolved_at'] = now();
        } elseif ($validated['status'] !== 'resolved') {
            $validated['resolved_at'] = null;
        }

        $complaint->update($validated);

        return redirect()->route('complaints.show', $complaint)
            ->with('success', 'Complaint status updated successfully!');
    }

    /**
     * Download attachment
     */
    public function downloadAttachment(Complaint $complaint, $index)
    {
        $this->authorize('view', $complaint);

        if (!isset($complaint->attachments[$index])) {
            abort(404);
        }

        $attachment = $complaint->attachments[$index];
        $filePath = storage_path('app/public/' . $attachment['path']);

        if (!file_exists($filePath)) {
            abort(404);
        }

        return response()->download($filePath, $attachment['filename']);
    }
}
