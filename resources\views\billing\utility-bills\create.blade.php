@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-flashlight-line text-primary me-2"></i>Add Utility Bill
                        </h4>
                        <p class="text-muted mb-0">
                            Record a new utility bill for property management
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('utility-bills.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Utility Bills
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form action="{{ route('utility-bills.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <div class="row">
                <!-- Bill Information -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Bill Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="utility_type" class="form-label">Utility Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('utility_type') is-invalid @enderror" 
                                            id="utility_type" name="utility_type" required>
                                        <option value="">Select Utility Type</option>
                                        <option value="electricity" {{ old('utility_type') == 'electricity' ? 'selected' : '' }}>Electricity</option>
                                        <option value="water" {{ old('utility_type') == 'water' ? 'selected' : '' }}>Water</option>
                                        <option value="gas" {{ old('utility_type') == 'gas' ? 'selected' : '' }}>Gas</option>
                                        <option value="internet" {{ old('utility_type') == 'internet' ? 'selected' : '' }}>Internet</option>
                                        <option value="cable_tv" {{ old('utility_type') == 'cable_tv' ? 'selected' : '' }}>Cable TV</option>
                                        <option value="waste_management" {{ old('utility_type') == 'waste_management' ? 'selected' : '' }}>Waste Management</option>
                                        <option value="other" {{ old('utility_type') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('utility_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                    <select class="form-select @error('property_id') is-invalid @enderror" 
                                            id="property_id" name="property_id" required>
                                        <option value="">Select Property</option>
                                        <!-- Properties will be populated here -->
                                        <option value="1" {{ old('property_id') == '1' ? 'selected' : '' }}>Sample Property 1</option>
                                        <option value="2" {{ old('property_id') == '2' ? 'selected' : '' }}>Sample Property 2</option>
                                    </select>
                                    @error('property_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="bill_number" class="form-label">Bill Number</label>
                                    <input type="text" class="form-control @error('bill_number') is-invalid @enderror" 
                                           id="bill_number" name="bill_number" value="{{ old('bill_number') }}" 
                                           placeholder="e.g., ELC-2024-001">
                                    @error('bill_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="provider_name" class="form-label">Service Provider <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('provider_name') is-invalid @enderror" 
                                           id="provider_name" name="provider_name" value="{{ old('provider_name') }}" 
                                           placeholder="e.g., City Electric Company" required>
                                    @error('provider_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="billing_period_start" class="form-label">Billing Period Start <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('billing_period_start') is-invalid @enderror" 
                                           id="billing_period_start" name="billing_period_start" 
                                           value="{{ old('billing_period_start') }}" required>
                                    @error('billing_period_start')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="billing_period_end" class="form-label">Billing Period End <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('billing_period_end') is-invalid @enderror" 
                                           id="billing_period_end" name="billing_period_end" 
                                           value="{{ old('billing_period_end') }}" required>
                                    @error('billing_period_end')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                           id="due_date" name="due_date" value="{{ old('due_date') }}" required>
                                    @error('due_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Usage & Amount -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Usage & Amount Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="previous_reading" class="form-label">Previous Reading</label>
                                    <input type="number" step="0.01" class="form-control @error('previous_reading') is-invalid @enderror" 
                                           id="previous_reading" name="previous_reading" value="{{ old('previous_reading') }}" 
                                           placeholder="0.00">
                                    @error('previous_reading')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="current_reading" class="form-label">Current Reading</label>
                                    <input type="number" step="0.01" class="form-control @error('current_reading') is-invalid @enderror" 
                                           id="current_reading" name="current_reading" value="{{ old('current_reading') }}" 
                                           placeholder="0.00">
                                    @error('current_reading')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="usage_amount" class="form-label">Usage Amount</label>
                                    <input type="number" step="0.01" class="form-control @error('usage_amount') is-invalid @enderror" 
                                           id="usage_amount" name="usage_amount" value="{{ old('usage_amount') }}" 
                                           placeholder="0.00" readonly>
                                    @error('usage_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="rate_per_unit" class="form-label">Rate per Unit</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.0001" class="form-control @error('rate_per_unit') is-invalid @enderror" 
                                               id="rate_per_unit" name="rate_per_unit" value="{{ old('rate_per_unit') }}" 
                                               placeholder="0.0000">
                                        @error('rate_per_unit')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="total_amount" class="form-label">Total Amount <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" class="form-control @error('total_amount') is-invalid @enderror" 
                                               id="total_amount" name="total_amount" value="{{ old('total_amount') }}" 
                                               placeholder="0.00" required>
                                        @error('total_amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3" 
                                          placeholder="Additional notes about this utility bill">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="bill_attachment" class="form-label">Bill Attachment</label>
                                <input type="file" class="form-control @error('bill_attachment') is-invalid @enderror" 
                                       id="bill_attachment" name="bill_attachment" accept=".pdf,.jpg,.jpeg,.png">
                                @error('bill_attachment')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Upload the original bill (PDF, JPG, PNG). Max size: 5MB</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions & Summary -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Bill Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Utility Type</small>
                                <div id="summary-utility-type" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Property</small>
                                <div id="summary-property" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Billing Period</small>
                                <div id="summary-period" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Total Amount</small>
                                <div id="summary-amount" class="fw-bold text-primary fs-4">$0.00</div>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i>Save Utility Bill
                                </button>
                                <button type="button" class="btn btn-success" onclick="saveAndDistribute()">
                                    <i class="ri-share-line me-1"></i>Save & Distribute
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Utility bill creation scripts
    console.log('Utility Bill Create page loaded');
    
    // Calculate usage amount
    function calculateUsage() {
        const previous = parseFloat(document.getElementById('previous_reading').value) || 0;
        const current = parseFloat(document.getElementById('current_reading').value) || 0;
        const usage = current - previous;
        document.getElementById('usage_amount').value = usage >= 0 ? usage.toFixed(2) : '';
        
        // Calculate total if rate is available
        const rate = parseFloat(document.getElementById('rate_per_unit').value) || 0;
        if (rate > 0 && usage > 0) {
            document.getElementById('total_amount').value = (usage * rate).toFixed(2);
        }
        updateSummary();
    }
    
    // Update summary
    function updateSummary() {
        const utilityType = document.getElementById('utility_type').selectedOptions[0]?.text || '-';
        const property = document.getElementById('property_id').selectedOptions[0]?.text || '-';
        const startDate = document.getElementById('billing_period_start').value;
        const endDate = document.getElementById('billing_period_end').value;
        const amount = document.getElementById('total_amount').value || '0.00';
        
        document.getElementById('summary-utility-type').textContent = utilityType;
        document.getElementById('summary-property').textContent = property;
        document.getElementById('summary-period').textContent = startDate && endDate ? `${startDate} to ${endDate}` : '-';
        document.getElementById('summary-amount').textContent = `$${parseFloat(amount).toFixed(2)}`;
    }
    
    function saveAndDistribute() {
        // Implementation for save and distribute
        console.log('Saving and distributing utility bill...');
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('previous_reading').addEventListener('input', calculateUsage);
        document.getElementById('current_reading').addEventListener('input', calculateUsage);
        document.getElementById('rate_per_unit').addEventListener('input', calculateUsage);
        document.getElementById('total_amount').addEventListener('input', updateSummary);
        document.getElementById('utility_type').addEventListener('change', updateSummary);
        document.getElementById('property_id').addEventListener('change', updateSummary);
        document.getElementById('billing_period_start').addEventListener('change', updateSummary);
        document.getElementById('billing_period_end').addEventListener('change', updateSummary);
    });
</script>
@endpush
