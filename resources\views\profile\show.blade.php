@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">My Profile</h5>
                <div>
                    <a href="{{ route('profile.edit') }}" class="btn btn-outline-warning me-2">
                        <i class="ri-edit-line me-1"></i>Edit Profile
                    </a>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Profile Photo Section -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <div class="profile-photo-display position-relative" style="width: 150px; height: 150px; margin: 0 auto;">
                            <img src="{{ $user->profile_photo_url }}" 
                                 alt="{{ $user->name }}" 
                                 class="rounded-circle border border-primary shadow" 
                                 style="width: 100%; height: 100%; object-fit: cover;">
                            
                            <!-- Photo Update Overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center"
                                 style="background: rgba(0,123,255,0.8); opacity: 0; transition: opacity 0.3s; cursor: pointer;"
                                 onmouseover="this.style.opacity='1'" 
                                 onmouseout="this.style.opacity='0'"
                                 onclick="showProfilePhotoModal()">
                                <i class="ri-camera-line text-white fs-2"></i>
                            </div>
                        </div>
                        
                        <h4 class="mt-3 mb-1">{{ $user->name }}</h4>
                        <p class="text-muted mb-2">{{ $user->email }}</p>
                        
                        <div class="d-flex justify-content-center gap-2 mb-3">
                            <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                {{ $user->is_active ? 'Active Account' : 'Inactive Account' }}
                            </span>
                            <span class="badge bg-primary">
                                {{ ucfirst(str_replace('_', ' ', $user->getRoleNames()->first() ?? 'User')) }}
                            </span>
                        </div>
                        
                        @if($user->hasProfilePhoto())
                            <small class="text-success">
                                <i class="ri-camera-line me-1"></i>Custom Profile Photo
                            </small>
                        @else
                            <small class="text-muted">
                                <i class="ri-user-line me-1"></i>Default Avatar
                            </small>
                        @endif
                    </div>
                </div>

                <!-- Profile Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-user-line text-primary me-2"></i>Personal Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ $user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $user->phone ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $user->address ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>City:</strong></td>
                                        <td>{{ $user->city ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>State:</strong></td>
                                        <td>{{ $user->state ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Zip Code:</strong></td>
                                        <td>{{ $user->zip_code ?? 'Not provided' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-shield-user-line text-success me-2"></i>Account Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>User Type:</strong></td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Role:</strong></td>
                                        <td>
                                            @foreach($user->roles as $role)
                                                <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                            @endforeach
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Account Status:</strong></td>
                                        <td>
                                            @if($user->is_active)
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>Active
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="ri-close-line me-1"></i>Inactive
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Member Since:</strong></td>
                                        <td>{{ $user->created_at->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $user->updated_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                    @if($user->email_verified_at)
                                        <tr>
                                            <td><strong>Email Verified:</strong></td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>Verified
                                                </span>
                                            </td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-center gap-2">
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="ri-arrow-left-line me-1"></i>Back to Dashboard
                            </a>
                            <a href="{{ route('profile.edit') }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Profile
                            </a>
                            <button type="button" class="btn btn-info" onclick="showProfilePhotoModal()">
                                <i class="ri-camera-line me-1"></i>Update Photo
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
