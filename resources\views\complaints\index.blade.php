@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-feedback-line text-primary me-2"></i>Complaints Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage and track all tenant complaints and issues
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('complaints.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>New Complaint
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('complaints.index') }}">All Complaints</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['status' => 'open']) }}">Open</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['status' => 'in_progress']) }}">In Progress</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['status' => 'resolved']) }}">Resolved</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['status' => 'closed']) }}">Closed</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['priority' => 'high']) }}">High Priority</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['priority' => 'medium']) }}">Medium Priority</a></li>
                                <li><a class="dropdown-item" href="{{ route('complaints.index', ['priority' => 'low']) }}">Low Priority</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['open_count'] ?? 0 }}</h3>
                                <p class="mb-0">Open Complaints</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-error-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['in_progress_count'] ?? 0 }}</h3>
                                <p class="mb-0">In Progress</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['resolved_count'] ?? 0 }}</h3>
                                <p class="mb-0">Resolved</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-check-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['high_priority_count'] ?? 0 }}</h3>
                                <p class="mb-0">High Priority</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Complaints Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Complaints List</h5>
                    <div class="d-flex gap-2">
                        <form method="GET" action="{{ route('complaints.index') }}" class="d-flex gap-2">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search complaints..." value="{{ request('search') }}">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if(isset($complaints) && $complaints->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tenant</th>
                                    <th>Property/Unit</th>
                                    <th>Subject</th>
                                    <th>Category</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($complaints as $complaint)
                                    <tr>
                                        <td>
                                            <strong>#{{ $complaint->id }}</strong>
                                        </td>
                                        <td>
                                            @if($complaint->tenant)
                                                <div>
                                                    <h6 class="mb-0">{{ $complaint->tenant->name }}</h6>
                                                    <small class="text-muted">{{ $complaint->tenant->email }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">No tenant</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($complaint->unit)
                                                <div>
                                                    <strong>{{ $complaint->unit->property->name ?? 'Unknown Property' }}</strong>
                                                    <br><small class="text-muted">Unit {{ $complaint->unit->unit_number }}</small>
                                                </div>
                                            @elseif($complaint->property)
                                                <strong>{{ $complaint->property->name }}</strong>
                                            @else
                                                <span class="text-muted">No property</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ Str::limit($complaint->subject ?? 'No Subject', 30) }}</strong>
                                                @if($complaint->description)
                                                    <br><small class="text-muted">{{ Str::limit($complaint->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ ucfirst(str_replace('_', ' ', $complaint->category ?? 'general')) }}
                                            </span>
                                        </td>
                                        <td>
                                            @php
                                                $priorityColors = [
                                                    'low' => 'success',
                                                    'medium' => 'warning',
                                                    'high' => 'danger',
                                                    'urgent' => 'dark'
                                                ];
                                                $priorityColor = $priorityColors[$complaint->priority ?? 'medium'] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $priorityColor }}">
                                                {{ ucfirst($complaint->priority ?? 'medium') }}
                                            </span>
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'open' => 'warning',
                                                    'in_progress' => 'info',
                                                    'resolved' => 'success',
                                                    'closed' => 'secondary',
                                                    'cancelled' => 'dark'
                                                ];
                                                $statusColor = $statusColors[$complaint->status ?? 'open'] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $statusColor }}">
                                                {{ ucfirst(str_replace('_', ' ', $complaint->status ?? 'open')) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($complaint->created_at)
                                                {{ $complaint->created_at->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $complaint->created_at->format('H:i') }}</small>
                                            @else
                                                <span class="text-muted">No date</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('complaints.show', $complaint) }}" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ route('complaints.edit', $complaint) }}" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                @if($complaint->status !== 'resolved' && $complaint->status !== 'closed')
                                                    <button type="button" class="btn btn-outline-success resolve-complaint" 
                                                            data-complaint-id="{{ $complaint->id }}" title="Mark as Resolved">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if(method_exists($complaints, 'links'))
                        <div class="d-flex justify-content-center mt-4">
                            {{ $complaints->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-5">
                        <i class="ri-feedback-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Complaints Found</h5>
                        <p class="text-muted mb-4">
                            @if(request('search'))
                                No complaints match your search criteria.
                            @else
                                No complaints have been submitted yet.
                            @endif
                        </p>
                        <a href="{{ route('complaints.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First Complaint
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Resolve complaint
    document.querySelectorAll('.resolve-complaint').forEach(button => {
        button.addEventListener('click', function() {
            const complaintId = this.dataset.complaintId;

            Swal.fire({
                title: 'Mark as Resolved',
                text: 'Are you sure you want to mark this complaint as resolved?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Mark as Resolved',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/complaints/${complaintId}/resolve`;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
