@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-task-line text-primary me-2"></i>Task Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage tasks, reminders, and notifications
                        </p>
                    </div>
                    <div>
                        @can('create', App\Models\Task::class)
                            <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Create Task
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-task-line fs-1 text-primary mb-2"></i>
                        <h4 class="mb-1">{{ $stats['total'] }}</h4>
                        <p class="text-muted mb-0">Total Tasks</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-time-line fs-1 text-warning mb-2"></i>
                        <h4 class="mb-1">{{ $stats['pending'] }}</h4>
                        <p class="text-muted mb-0">Pending</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-play-circle-line fs-1 text-info mb-2"></i>
                        <h4 class="mb-1">{{ $stats['in_progress'] }}</h4>
                        <p class="text-muted mb-0">In Progress</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-check-double-line fs-1 text-success mb-2"></i>
                        <h4 class="mb-1">{{ $stats['completed'] }}</h4>
                        <p class="text-muted mb-0">Completed</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-alarm-warning-line fs-1 text-danger mb-2"></i>
                        <h4 class="mb-1">{{ $stats['overdue'] }}</h4>
                        <p class="text-muted mb-0">Overdue</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-calendar-todo-line fs-1 text-purple mb-2"></i>
                        <h4 class="mb-1">{{ $stats['due_today'] }}</h4>
                        <p class="text-muted mb-0">Due Today</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('tasks.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Task title, description...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="inspection" {{ request('type') == 'inspection' ? 'selected' : '' }}>Inspection</option>
                            <option value="contract_renewal" {{ request('type') == 'contract_renewal' ? 'selected' : '' }}>Contract Renewal</option>
                            <option value="maintenance" {{ request('type') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                            <option value="lease_expiry" {{ request('type') == 'lease_expiry' ? 'selected' : '' }}>Lease Expiry</option>
                            <option value="document_expiry" {{ request('type') == 'document_expiry' ? 'selected' : '' }}>Document Expiry</option>
                            <option value="other" {{ request('type') == 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <option value="low" {{ request('priority') == 'low' ? 'selected' : '' }}>Low</option>
                            <option value="medium" {{ request('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                            <option value="high" {{ request('priority') == 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ request('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tasks List -->
        @if($tasks->count() > 0)
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Type</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Assigned To</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tasks as $task)
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                                                @endif
                                                @if($task->property)
                                                    <br><small class="text-info">{{ $task->property->name }}</small>
                                                @endif
                                                @if($task->unit)
                                                    <small class="text-info"> - Unit {{ $task->unit->unit_number }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $task->type)) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $task->priority == 'urgent' ? 'danger' : ($task->priority == 'high' ? 'warning' : ($task->priority == 'medium' ? 'info' : 'secondary')) }}">
                                                {{ ucfirst($task->priority) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $task->status == 'completed' ? 'success' : ($task->status == 'in_progress' ? 'primary' : ($task->status == 'cancelled' ? 'danger' : 'warning')) }}">
                                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                            </span>
                                            @if($task->is_overdue)
                                                <br><small class="text-danger"><i class="ri-alarm-warning-line"></i> Overdue</small>
                                            @elseif($task->is_due_today)
                                                <br><small class="text-warning"><i class="ri-calendar-todo-line"></i> Due Today</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($task->assignedTo)
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        @if($task->assignedTo->profile_photo)
                                                            <img src="{{ asset('storage/' . $task->assignedTo->profile_photo) }}" alt="{{ $task->assignedTo->name }}" class="rounded-circle">
                                                        @else
                                                            <div class="avatar-initial rounded-circle bg-primary">
                                                                {{ substr($task->assignedTo->name, 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <span>{{ $task->assignedTo->name }}</span>
                                                </div>
                                            @else
                                                <span class="text-muted">Unassigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($task->due_date)
                                                {{ $task->due_date->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $task->due_date->format('H:i') }}</small>
                                            @else
                                                <span class="text-muted">No due date</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="ri-more-line"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('tasks.show', $task) }}">
                                                            <i class="ri-eye-line me-1"></i>View Details
                                                        </a>
                                                    </li>
                                                    @can('update', $task)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('tasks.edit', $task) }}">
                                                                <i class="ri-edit-line me-1"></i>Edit Task
                                                            </a>
                                                        </li>
                                                        @if($task->status !== 'completed')
                                                            <li>
                                                                <form action="{{ route('tasks.complete', $task) }}" method="POST" class="d-inline">
                                                                    @csrf
                                                                    @method('PATCH')
                                                                    <button type="submit" class="dropdown-item text-success">
                                                                        <i class="ri-check-line me-1"></i>Mark Complete
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif
                                                    @endcan
                                                    @can('delete', $task)
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item text-danger delete-task" 
                                                                    data-task-id="{{ $task->id }}"
                                                                    data-task-title="{{ $task->title }}">
                                                                <i class="ri-delete-bin-line me-1"></i>Delete
                                                            </button>
                                                        </li>
                                                    @endcan
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $tasks->links() }}
                    </div>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-task-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Tasks Found</h5>
                    <p class="text-muted mb-4">
                        @if(request()->hasAny(['search', 'status', 'type', 'priority']))
                            No tasks match your current filters. Try adjusting your search criteria.
                        @else
                            You haven't created any tasks yet. Start by creating your first task.
                        @endif
                    </p>
                    @can('create', App\Models\Task::class)
                        <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create Your First Task
                        </a>
                    @endcan
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete task functionality
    document.querySelectorAll('.delete-task').forEach(button => {
        button.addEventListener('click', function() {
            const taskId = this.dataset.taskId;
            const taskTitle = this.dataset.taskTitle;

            Swal.fire({
                title: 'Delete Task',
                html: `Are you sure you want to delete:<br><br><strong>${taskTitle}</strong>?<br><br><small class="text-danger">This action cannot be undone.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/tasks/${taskId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
