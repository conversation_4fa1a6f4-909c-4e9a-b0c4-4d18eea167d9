@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">User Details: {{ $user->name }}</h5>
                <div>
                    @if($user->canBeManagedBy(auth()->user()))
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-outline-warning me-2">
                            <i class="ri-edit-line me-1"></i>Edit User
                        </a>
                    @endif
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Users
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- User Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-user-line text-primary me-2"></i>Personal Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ $user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $user->phone ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $user->address ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>City:</strong></td>
                                        <td>{{ $user->city ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>State:</strong></td>
                                        <td>{{ $user->state ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Zip Code:</strong></td>
                                        <td>{{ $user->zip_code ?? 'Not provided' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-shield-user-line text-success me-2"></i>Account Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>User Type:</strong></td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Role:</strong></td>
                                        <td>
                                            @foreach($user->roles as $role)
                                                <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                            @endforeach
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            @if($user->is_active)
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>Active
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="ri-close-line me-1"></i>Inactive
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                    @if($user->creator)
                                        <tr>
                                            <td><strong>Created By:</strong></td>
                                            <td>
                                                <a href="{{ route('admin.users.show', $user->creator) }}" class="text-decoration-none">
                                                    {{ $user->creator->name }}
                                                </a>
                                                <br><small class="text-muted">Property Owner</small>
                                            </td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td><strong>Joined:</strong></td>
                                        <td>{{ $user->created_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $user->updated_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                <i class="ri-arrow-left-line me-1"></i>Back to Users
                            </a>
                            @if($user->canBeManagedBy(auth()->user()))
                                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                                    <i class="ri-edit-line me-1"></i>Edit User
                                </a>
                                @if($user->id !== auth()->id())
                                    @if($user->is_active)
                                        <button type="button" class="btn btn-outline-secondary deactivate-user" 
                                                data-user-name="{{ $user->name }}"
                                                data-url="{{ route('admin.users.deactivate', $user) }}">
                                            <i class="ri-pause-circle-line me-1"></i>Deactivate
                                        </button>
                                    @else
                                        <button type="button" class="btn btn-success activate-user" 
                                                data-user-name="{{ $user->name }}"
                                                data-url="{{ route('admin.users.activate', $user) }}">
                                            <i class="ri-play-circle-line me-1"></i>Activate
                                        </button>
                                    @endif
                                    
                                    <button type="button" class="btn btn-danger delete-user" 
                                            data-user-name="{{ $user->name }}"
                                            data-url="{{ route('admin.users.destroy', $user) }}">
                                        <i class="ri-delete-bin-line me-1"></i>Delete
                                    </button>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        @if($user->hasRole('property_owner') && isset($employees) && $employees->count() > 0)
            <!-- Employees Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-group-line text-primary me-2"></i>Employees ({{ $employees->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if(isset($employeesByRole))
                        @foreach($employeesByRole as $roleName => $roleEmployees)
                            <div class="mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="ri-user-line me-1"></i>{{ ucfirst(str_replace('_', ' ', $roleName)) }} 
                                    <span class="badge bg-primary ms-2">{{ $roleEmployees->count() }}</span>
                                </h6>
                                <div class="row">
                                    @foreach($roleEmployees as $employee)
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card border h-100">
                                                <div class="card-body p-3">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <span class="text-white">{{ substr($employee->name, 0, 1) }}</span>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-0">{{ $employee->name }}</h6>
                                                            <small class="text-muted">{{ $employee->email }}</small>
                                                        </div>
                                                        @if($employee->is_active)
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </div>
                                                    
                                                    @if($employee->phone)
                                                        <p class="mb-2 small">
                                                            <i class="ri-phone-line me-1"></i>{{ $employee->phone }}
                                                        </p>
                                                    @endif
                                                    
                                                    <p class="mb-2 small">
                                                        <i class="ri-calendar-line me-1"></i>Joined {{ $employee->created_at->format('M d, Y') }}
                                                    </p>
                                                    
                                                    <div class="d-flex gap-1">
                                                        <a href="{{ route('admin.users.show', $employee) }}" class="btn btn-outline-info btn-sm">
                                                            <i class="ri-eye-line"></i>
                                                        </a>
                                                        @if($employee->canBeManagedBy(auth()->user()))
                                                            <a href="{{ route('admin.users.edit', $employee) }}" class="btn btn-outline-warning btn-sm">
                                                                <i class="ri-edit-line"></i>
                                                            </a>
                                                            @if($employee->is_active)
                                                                <button type="button" class="btn btn-outline-secondary btn-sm deactivate-user" 
                                                                        data-user-name="{{ $employee->name }}"
                                                                        data-url="{{ route('admin.users.deactivate', $employee) }}">
                                                                    <i class="ri-pause-circle-line"></i>
                                                                </button>
                                                            @else
                                                                <button type="button" class="btn btn-outline-success btn-sm activate-user" 
                                                                        data-user-name="{{ $employee->name }}"
                                                                        data-url="{{ route('admin.users.activate', $employee) }}">
                                                                    <i class="ri-play-circle-line"></i>
                                                                </button>
                                                            @endif
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        @elseif($user->hasRole('property_owner'))
            <!-- No Employees Section -->
            <div class="card mt-4">
                <div class="card-body text-center py-5">
                    <i class="ri-group-line fs-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">No Employees Yet</h5>
                    <p class="text-muted">This property owner hasn't created any employee accounts yet.</p>
                    @if(auth()->user()->hasRole('admin'))
                        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add First Employee
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activate User
    document.querySelectorAll('.activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Activate Employee Account',
                html: `Are you sure you want to <strong>activate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-play-circle-line me-1"></i>Yes, Activate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Deactivate User
    document.querySelectorAll('.deactivate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Deactivate Employee Account',
                html: `Are you sure you want to <strong>deactivate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-pause-circle-line me-1"></i>Yes, Deactivate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Delete User
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Delete Employee Account',
                html: `Are you sure you want to <strong>permanently delete</strong> the account for:<br><br><strong>${userName}</strong>?<br><br><span class="text-danger"><i class="ri-alert-line"></i> This action cannot be undone!</span>`,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
