@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-money-dollar-circle-line text-primary me-2"></i>Record New Payment
                        </h4>
                        <p class="text-muted mb-0">
                            Record a payment received from a tenant
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Payments
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('payments.store') }}" method="POST" id="createPaymentForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Bill Selection -->
                            <div class="mb-3">
                                <label for="bill_id" class="form-label">Related Bill (Optional)</label>
                                <select class="form-select @error('bill_id') is-invalid @enderror" 
                                        id="bill_id" name="bill_id">
                                    <option value="">Select Bill (Optional)</option>
                                    @foreach($bills as $bill)
                                        <option value="{{ $bill->id }}" 
                                                data-amount="{{ $bill->amount }}"
                                                data-tenant="{{ $bill->tenant_id }}"
                                                data-property="{{ $bill->property_id }}"
                                                data-unit="{{ $bill->unit_id }}"
                                                {{ old('bill_id') == $bill->id ? 'selected' : '' }}>
                                            #{{ $bill->bill_number ?? 'B-' . str_pad($bill->id, 6, '0', STR_PAD_LEFT) }} - 
                                            {{ $bill->tenant ? $bill->tenant->name : 'No Tenant' }} - 
                                            ${{ number_format($bill->amount, 2) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('bill_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Tenant Selection -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Tenant <span class="text-danger">*</span></label>
                                <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id" required>
                                    <option value="">Select Tenant</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name }} ({{ $tenant->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Property and Unit Selection -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select @error('property_id') is-invalid @enderror" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            @foreach($properties as $property)
                                                <option value="{{ $property->id }}" {{ old('property_id') == $property->id ? 'selected' : '' }}>
                                                    {{ $property->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('property_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select @error('unit_id') is-invalid @enderror" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                        </select>
                                        @error('unit_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Amount -->
                            <div class="mb-3">
                                <label for="amount" class="form-label">Payment Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                           id="amount" name="amount" value="{{ old('amount') }}" 
                                           placeholder="0.00" min="0.01" step="0.01" required>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Payment Date -->
                            <div class="mb-3">
                                <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control @error('payment_date') is-invalid @enderror" 
                                       id="payment_date" name="payment_date" 
                                       value="{{ old('payment_date', now()->format('Y-m-d\TH:i')) }}" required>
                                @error('payment_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3" 
                                          placeholder="Additional details about the payment...">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">Internal Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="2" 
                                          placeholder="Internal notes (not visible to tenant)...">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Payment Method -->
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" name="payment_method" required>
                                    <option value="">Select Method</option>
                                    <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                    <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                    <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                    <option value="debit_card" {{ old('payment_method') == 'debit_card' ? 'selected' : '' }}>Debit Card</option>
                                    <option value="online" {{ old('payment_method') == 'online' ? 'selected' : '' }}>Online Payment</option>
                                    <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>Mobile Payment</option>
                                    <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Payment Status -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Payment Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="completed" {{ old('status', 'completed') == 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="failed" {{ old('status') == 'failed' ? 'selected' : '' }}>Failed</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Reference Number -->
                            <div class="mb-3">
                                <label for="reference_number" class="form-label">Reference Number</label>
                                <input type="text" class="form-control @error('reference_number') is-invalid @enderror" 
                                       id="reference_number" name="reference_number" value="{{ old('reference_number') }}" 
                                       placeholder="e.g., Check #, Transaction ID">
                                @error('reference_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Late Fee -->
                            <div class="mb-3">
                                <label for="late_fee" class="form-label">Late Fee</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('late_fee') is-invalid @enderror" 
                                           id="late_fee" name="late_fee" value="{{ old('late_fee', 0) }}" 
                                           placeholder="0.00" min="0" step="0.01">
                                    @error('late_fee')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Payment Summary -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Payment Summary</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Payment Amount:</span>
                                        <span id="summary-amount">$0.00</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Late Fee:</span>
                                        <span id="summary-late-fee">$0.00</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total:</span>
                                        <span id="summary-total">$0.00</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Receipt Options -->
                            <div class="mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_receipt" name="send_receipt" value="1" 
                                           {{ old('send_receipt') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="send_receipt">
                                        Send receipt to tenant
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_apply_to_bill" name="auto_apply_to_bill" value="1" 
                                           {{ old('auto_apply_to_bill', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="auto_apply_to_bill">
                                        Automatically apply to selected bill
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Record Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle bill selection
    const billSelect = document.getElementById('bill_id');
    const tenantSelect = document.getElementById('tenant_id');
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');
    const amountInput = document.getElementById('amount');

    billSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (selectedOption.value) {
            // Auto-fill related fields
            const amount = selectedOption.dataset.amount;
            const tenantId = selectedOption.dataset.tenant;
            const propertyId = selectedOption.dataset.property;
            const unitId = selectedOption.dataset.unit;

            if (amount) amountInput.value = amount;
            if (tenantId) tenantSelect.value = tenantId;
            if (propertyId) {
                propertySelect.value = propertyId;
                loadUnits(propertyId, unitId);
            }
        }
    });

    // Handle property selection to load units
    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId, selectedUnitId = null) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    if (selectedUnitId && unit.id == selectedUnitId) {
                        option.selected = true;
                    }
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Update payment summary
    const lateFeeInput = document.getElementById('late_fee');
    
    function updateSummary() {
        const amount = parseFloat(amountInput.value) || 0;
        const lateFee = parseFloat(lateFeeInput.value) || 0;
        const total = amount + lateFee;

        document.getElementById('summary-amount').textContent = '$' + amount.toFixed(2);
        document.getElementById('summary-late-fee').textContent = '$' + lateFee.toFixed(2);
        document.getElementById('summary-total').textContent = '$' + total.toFixed(2);
    }

    amountInput.addEventListener('input', updateSummary);
    lateFeeInput.addEventListener('input', updateSummary);

    // Initialize summary
    updateSummary();

    // Form validation
    const form = document.getElementById('createPaymentForm');
    form.addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value);
        const tenantId = tenantSelect.value;
        const paymentMethod = document.getElementById('payment_method').value;

        if (!amount || amount <= 0) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Amount',
                text: 'Please enter a valid payment amount.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (!tenantId) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Tenant',
                text: 'Please select a tenant for this payment.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (!paymentMethod) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Payment Method',
                text: 'Please select a payment method.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
@endsection
