table.dataTable {
  color: #272727;
  margin-bottom: 5px !important;
  border: 1px solid #dee1e9;
  border-radius: 6px;
  overflow: hidden;
}

table.dataTable td {
  color: #4c545e;
}

table.dataTable td.highlight {
  background: #eaf1ff;
  color: #272727;
}

.page-link {
  color: #272727;
  border-color: #dee1e9;
}

.page-link:hover {
  background-color: #ffffff;
  color: #566fe2;
  border-color: #dee1e9;
}

.page-link.active,
.active>.page-link {
  background-color: #566fe2;
  border: 1px solid #566fe2;
  color: #ffffff;
}

.page-link.disabled,
.disabled>.page-link {
  border-color: #d8dce6;
  background-color: #ffffff;
}

div.dataTables_wrapper div.dataTables_info {
  display: inline-block;
}

div.dataTables_wrapper div.dataTables_paginate {
  float: right;
}

table.dataTable>thead .sorting,
table.dataTable>thead .sorting_asc {
  color: #fff;
  background: #566fe2;
}