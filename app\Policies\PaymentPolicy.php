<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PaymentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // All authenticated users can view payments
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Payment $payment): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin can view all payments
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can view payments for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($payment->property_id && $user->ownedProperties->contains($payment->property_id)) {
                return true;
            }
            if ($payment->unit && $user->ownedProperties->contains($payment->unit->property_id)) {
                return true;
            }
        }

        // Tenants can view their own payments
        if (in_array('tenant', $userRoles)) {
            return $payment->tenant_id === $user->id;
        }

        // Property managers and staff can view payments
        if (in_array('property_manager', $userRoles) || in_array('maintenance_staff', $userRoles) || in_array('receptionist', $userRoles)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin, property owners, property managers, and tenants can create payments
        return in_array('admin', $userRoles) || 
               in_array('property_owner', $userRoles) || 
               in_array('property_manager', $userRoles) ||
               in_array('tenant', $userRoles);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Payment $payment): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin can update all payments
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can update payments for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($payment->property_id && $user->ownedProperties->contains($payment->property_id)) {
                return true;
            }
            if ($payment->unit && $user->ownedProperties->contains($payment->unit->property_id)) {
                return true;
            }
        }

        // Property managers can update payments
        if (in_array('property_manager', $userRoles)) {
            return true;
        }

        // Tenants can update their own payments (if pending)
        if (in_array('tenant', $userRoles)) {
            return $payment->tenant_id === $user->id && $payment->status === 'pending';
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Payment $payment): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Only admin and property owners can delete payments
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can delete payments for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($payment->property_id && $user->ownedProperties->contains($payment->property_id)) {
                return true;
            }
            if ($payment->unit && $user->ownedProperties->contains($payment->unit->property_id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Payment $payment): bool
    {
        return $this->delete($user, $payment);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Payment $payment): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Only admin can force delete payments
        return in_array('admin', $userRoles);
    }

    /**
     * Determine whether the user can confirm the payment.
     */
    public function confirm(User $user, Payment $payment): bool
    {
        return $this->update($user, $payment);
    }

    /**
     * Determine whether the user can refund the payment.
     */
    public function refund(User $user, Payment $payment): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Only admin and property owners can refund payments
        return in_array('admin', $userRoles) || 
               (in_array('property_owner', $userRoles) && $this->view($user, $payment));
    }
}
