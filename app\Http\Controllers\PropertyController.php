<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\Unit;
use App\Models\Complaint;
use App\Models\Bill;
use App\Models\Visitor;
use App\Models\BlacklistedVisitor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PropertyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Property::with(['owner', 'manager', 'units']);
        } else {
            $query = $user->ownedProperties()->with(['manager', 'units']);
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        if ($request->filled('type')) {
            $query->where('property_type', $request->type);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Get properties with statistics
        $properties = $query->get()->map(function ($property) {
            $property->total_units_count = $property->units->count();
            $property->occupied_units_count = $property->units->where('status', 'occupied')->count();
            $property->available_units_count = $property->units->where('status', 'available')->count();
            $property->occupancy_rate = $property->total_units_count > 0
                ? round(($property->occupied_units_count / $property->total_units_count) * 100, 1)
                : 0;

            // Calculate monthly income
            $property->monthly_income = $property->units->where('status', 'occupied')->sum('rent_amount');

            // Get complaints count
            $property->open_complaints_count = Complaint::where('property_id', $property->id)
                ->where('status', '!=', 'resolved')->count();

            return $property;
        });

        // Get filter options
        $propertyTypes = Property::distinct()->pluck('property_type');
        $cities = Property::distinct()->pluck('city');

        return view('properties.index', compact('properties', 'propertyTypes', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Property::class);

        return view('properties.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Property::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'zip_code' => 'required|string|max:10',
            'country' => 'required|string|max:100',
            'property_type' => 'required|string|max:100',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'purchase_price' => 'nullable|numeric|min:0',
            'amenities' => 'nullable|array',
        ]);

        $validated['owner_id'] = Auth::id();
        $validated['status'] = 'active';

        $property = Property::create($validated);

        return redirect()->route('properties.show', $property)
            ->with('success', 'Property created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Property $property)
    {
        $this->authorize('view', $property);

        // Load relationships
        $property->load(['owner', 'manager', 'units.tenant', 'complaints']);

        // Calculate statistics
        $stats = [
            'total_units' => $property->units->count(),
            'occupied_units' => $property->units->where('status', 'occupied')->count(),
            'available_units' => $property->units->where('status', 'available')->count(),
            'maintenance_units' => $property->units->where('status', 'maintenance')->count(),
            'monthly_income' => $property->units->where('status', 'occupied')->sum('rent_amount'),
            'occupancy_rate' => $property->units->count() > 0
                ? round(($property->units->where('status', 'occupied')->count() / $property->units->count()) * 100, 1)
                : 0,
        ];

        // Get complaints statistics
        $complaints = [
            'total' => $property->complaints->count(),
            'open' => $property->complaints->where('status', 'open')->count(),
            'in_progress' => $property->complaints->where('status', 'in_progress')->count(),
            'resolved' => $property->complaints->where('status', 'resolved')->count(),
        ];

        // Get recent visitors
        $recentVisitors = Visitor::where('property_id', $property->id)
            ->with(['visitingUnit', 'visitingTenant'])
            ->latest('check_in_time')
            ->take(5)
            ->get();

        // Get billing information
        $billing = [
            'total_billed' => Bill::where('property_id', $property->id)->sum('total_amount'),
            'amount_paid' => Bill::where('property_id', $property->id)->where('status', 'paid')->sum('total_amount'),
            'amount_pending' => Bill::where('property_id', $property->id)->where('status', 'unpaid')->sum('total_amount'),
        ];

        return view('properties.show', compact('property', 'stats', 'complaints', 'recentVisitors', 'billing'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Property $property)
    {
        $this->authorize('update', $property);

        return view('properties.edit', compact('property'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Property $property)
    {
        $this->authorize('update', $property);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'zip_code' => 'required|string|max:10',
            'country' => 'required|string|max:100',
            'property_type' => 'required|string|max:100',
            'total_area' => 'nullable|numeric|min:0',
            'total_units' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'purchase_price' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,maintenance',
            'amenities' => 'nullable|array',
        ]);

        $property->update($validated);

        return redirect()->route('properties.show', $property)
            ->with('success', 'Property updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Property $property)
    {
        $this->authorize('delete', $property);

        $property->delete();

        return redirect()->route('properties.index')
            ->with('success', 'Property deleted successfully!');
    }
}
