<?php

namespace App\Console\Commands;

use App\Models\Task;
use App\Models\Document;
use App\Models\Unit;
use App\Models\Notification;
use App\Http\Controllers\NotificationController;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ProcessScheduledTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tasks:process-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process scheduled tasks, reminders, and notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing scheduled tasks and notifications...');

        // Process task reminders
        $this->processTaskReminders();

        // Process lease expiry notifications
        $this->processLeaseExpiryNotifications();

        // Process document expiry notifications
        $this->processDocumentExpiryNotifications();

        // Create recurring tasks
        $this->createRecurringTasks();

        // Update expired documents
        $this->updateExpiredDocuments();

        $this->info('Scheduled tasks processing completed!');
    }

    /**
     * Process task reminders
     */
    private function processTaskReminders()
    {
        $this->info('Processing task reminders...');

        $tasks = Task::whereNotNull('due_date')
            ->whereNotNull('reminder_settings')
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->get();

        $remindersSent = 0;

        foreach ($tasks as $task) {
            $reminderSettings = $task->reminder_settings;

            if (!isset($reminderSettings['days_before']) || !$reminderSettings['email_enabled']) {
                continue;
            }

            $daysBefore = $reminderSettings['days_before'];
            $reminderDate = $task->due_date->subDays($daysBefore);

            // Check if reminder should be sent today
            if ($reminderDate->isToday()) {
                // Check if reminder already sent
                $existingNotification = Notification::where('related_task_id', $task->id)
                    ->where('type', 'task_reminder')
                    ->whereDate('created_at', today())
                    ->first();

                if (!$existingNotification && $task->assignedTo) {
                    Notification::createTaskReminder(
                        $task,
                        $task->assignedTo,
                        "Reminder: Task '{$task->title}' is due in {$daysBefore} day(s)"
                    );
                    $remindersSent++;
                }
            }
        }

        $this->info("Sent {$remindersSent} task reminders");
    }

    /**
     * Process lease expiry notifications
     */
    private function processLeaseExpiryNotifications()
    {
        $this->info('Processing lease expiry notifications...');

        NotificationController::createLeaseExpiryNotifications();

        $this->info('Lease expiry notifications processed');
    }

    /**
     * Process document expiry notifications
     */
    private function processDocumentExpiryNotifications()
    {
        $this->info('Processing document expiry notifications...');

        NotificationController::createDocumentExpiryNotifications();

        $this->info('Document expiry notifications processed');
    }

    /**
     * Create recurring tasks
     */
    private function createRecurringTasks()
    {
        $this->info('Creating recurring tasks...');

        $completedRecurringTasks = Task::where('is_recurring', true)
            ->where('status', 'completed')
            ->whereNotNull('next_occurrence')
            ->whereDate('next_occurrence', '<=', today())
            ->get();

        $tasksCreated = 0;

        foreach ($completedRecurringTasks as $task) {
            $newTask = $task->createNextOccurrence();
            if ($newTask) {
                $tasksCreated++;

                // Create notification for assigned user
                if ($newTask->assignedTo) {
                    Notification::createTaskReminder(
                        $newTask,
                        $newTask->assignedTo,
                        "New recurring task created: {$newTask->title}"
                    );
                }
            }
        }

        $this->info("Created {$tasksCreated} recurring tasks");
    }

    /**
     * Update expired documents
     */
    private function updateExpiredDocuments()
    {
        $this->info('Updating expired documents...');

        $expiredDocuments = Document::getExpiredDocuments();
        $updatedCount = 0;

        foreach ($expiredDocuments as $document) {
            if ($document->checkExpiry()) {
                $updatedCount++;
            }
        }

        $this->info("Updated {$updatedCount} expired documents");
    }
}
