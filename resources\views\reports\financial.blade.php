@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-line-chart-line text-primary me-2"></i>Financial Reports
                        </h4>
                        <p class="text-muted mb-0">
                            Income, expenses, and profit/loss analysis
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <form method="GET" action="{{ route('reports.financial') }}" class="d-flex gap-2">
                            <input type="date" name="start_date" class="form-control" 
                                   value="{{ $startDate->format('Y-m-d') }}" title="Start Date">
                            <input type="date" name="end_date" class="form-control" 
                                   value="{{ $endDate->format('Y-m-d') }}" title="End Date">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-search-line me-1"></i>Filter
                            </button>
                        </form>
                        <button class="btn btn-outline-success" onclick="exportFinancialReport()">
                            <i class="ri-file-excel-line me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit/Loss Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ri-money-dollar-circle-line me-2"></i>Profit & Loss Summary
                            <small class="text-muted">({{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }})</small>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">${{ number_format($profitLoss['total_income'], 2) }}</h3>
                                                <p class="mb-0">Total Income</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-arrow-up-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">${{ number_format($profitLoss['total_expenses'], 2) }}</h3>
                                                <p class="mb-0">Total Expenses</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-arrow-down-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-{{ $profitLoss['net_profit'] >= 0 ? 'primary' : 'warning' }} text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">${{ number_format($profitLoss['net_profit'], 2) }}</h3>
                                                <p class="mb-0">Net {{ $profitLoss['net_profit'] >= 0 ? 'Profit' : 'Loss' }}</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-{{ $profitLoss['net_profit'] >= 0 ? 'trending-up' : 'trending-down' }}-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h3 class="mb-1">{{ number_format($profitLoss['profit_margin'], 1) }}%</h3>
                                                <p class="mb-0">Profit Margin</p>
                                            </div>
                                            <div class="fs-1 opacity-50">
                                                <i class="ri-percent-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Income and Expenses Details -->
        <div class="row mb-4">
            <!-- Income Details -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0 text-success">
                            <i class="ri-arrow-up-circle-line me-2"></i>Income Details
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($incomeData->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Tenant</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($incomeData->take(10) as $income)
                                            <tr>
                                                <td>{{ $income->payment_date->format('M d') }}</td>
                                                <td>
                                                    <small>{{ $income->tenant->name ?? 'N/A' }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        {{ ucfirst($income->payment_method ?? 'Payment') }}
                                                    </span>
                                                </td>
                                                <td><strong>${{ number_format($income->amount, 2) }}</strong></td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($incomeData->count() > 10)
                                <div class="text-center mt-3">
                                    <small class="text-muted">Showing 10 of {{ $incomeData->count() }} income records</small>
                                </div>
                            @endif
                        @else
                            <div class="text-center py-4">
                                <i class="ri-money-dollar-circle-line fs-1 text-muted mb-2"></i>
                                <p class="text-muted">No income recorded for this period</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Expenses Details -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0 text-danger">
                            <i class="ri-arrow-down-circle-line me-2"></i>Expense Details
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($expenseData->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Property</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($expenseData->take(10) as $expense)
                                            <tr>
                                                <td>{{ $expense->created_at->format('M d') }}</td>
                                                <td>
                                                    <small>{{ $expense->property->name ?? 'N/A' }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger">
                                                        {{ ucfirst($expense->type ?? 'Maintenance') }}
                                                    </span>
                                                </td>
                                                <td><strong>${{ number_format($expense->cost ?? 0, 2) }}</strong></td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($expenseData->count() > 10)
                                <div class="text-center mt-3">
                                    <small class="text-muted">Showing 10 of {{ $expenseData->count() }} expense records</small>
                                </div>
                            @endif
                        @else
                            <div class="text-center py-4">
                                <i class="ri-arrow-down-circle-line fs-1 text-muted mb-2"></i>
                                <p class="text-muted">No expenses recorded for this period</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Income vs Expenses Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ri-bar-chart-line me-2"></i>Income vs Expenses Comparison
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <canvas id="incomeExpenseChart" width="400" height="200"></canvas>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex flex-column justify-content-center h-100">
                                    <div class="mb-3">
                                        <h6>Financial Summary</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="ri-arrow-up-line text-success me-2"></i>Income: ${{ number_format($profitLoss['total_income'], 2) }}</li>
                                            <li><i class="ri-arrow-down-line text-danger me-2"></i>Expenses: ${{ number_format($profitLoss['total_expenses'], 2) }}</li>
                                            <li><i class="ri-subtract-line text-primary me-2"></i>Net: ${{ number_format($profitLoss['net_profit'], 2) }}</li>
                                            <li><i class="ri-percent-line text-info me-2"></i>Margin: {{ number_format($profitLoss['profit_margin'], 1) }}%</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h6>Performance Indicators</h6>
                                        <div class="progress mb-2">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ $profitLoss['total_income'] > 0 ? 100 : 0 }}%">
                                                Income
                                            </div>
                                        </div>
                                        <div class="progress mb-2">
                                            <div class="progress-bar bg-danger" role="progressbar" 
                                                 style="width: {{ $profitLoss['total_income'] > 0 ? ($profitLoss['total_expenses'] / $profitLoss['total_income']) * 100 : 0 }}%">
                                                Expenses
                                            </div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-{{ $profitLoss['profit_margin'] >= 0 ? 'primary' : 'warning' }}" role="progressbar" 
                                                 style="width: {{ abs($profitLoss['profit_margin']) }}%">
                                                {{ $profitLoss['profit_margin'] >= 0 ? 'Profit' : 'Loss' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Breakdown -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ri-calendar-line me-2"></i>Monthly Financial Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Income</th>
                                <th>Expenses</th>
                                <th>Net Profit/Loss</th>
                                <th>Profit Margin</th>
                                <th>Growth</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                // Group income and expenses by month
                                $monthlyData = [];
                                $currentDate = $startDate->copy();
                                
                                while ($currentDate <= $endDate) {
                                    $monthKey = $currentDate->format('Y-m');
                                    $monthName = $currentDate->format('M Y');
                                    
                                    $monthIncome = $incomeData->filter(function($item) use ($monthKey) {
                                        return $item->payment_date->format('Y-m') === $monthKey;
                                    })->sum('amount');
                                    
                                    $monthExpenses = $expenseData->filter(function($item) use ($monthKey) {
                                        return $item->created_at->format('Y-m') === $monthKey;
                                    })->sum('cost');
                                    
                                    $monthlyData[] = [
                                        'month' => $monthName,
                                        'income' => $monthIncome,
                                        'expenses' => $monthExpenses,
                                        'net' => $monthIncome - $monthExpenses,
                                        'margin' => $monthIncome > 0 ? (($monthIncome - $monthExpenses) / $monthIncome) * 100 : 0
                                    ];
                                    
                                    $currentDate->addMonth();
                                }
                            @endphp
                            
                            @foreach($monthlyData as $index => $data)
                                <tr>
                                    <td><strong>{{ $data['month'] }}</strong></td>
                                    <td class="text-success">${{ number_format($data['income'], 2) }}</td>
                                    <td class="text-danger">${{ number_format($data['expenses'], 2) }}</td>
                                    <td class="text-{{ $data['net'] >= 0 ? 'primary' : 'warning' }}">
                                        <strong>${{ number_format($data['net'], 2) }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $data['margin'] >= 0 ? 'success' : 'danger' }}">
                                            {{ number_format($data['margin'], 1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        @if($index > 0)
                                            @php
                                                $prevNet = $monthlyData[$index - 1]['net'];
                                                $growth = $prevNet != 0 ? (($data['net'] - $prevNet) / abs($prevNet)) * 100 : 0;
                                            @endphp
                                            <span class="badge bg-{{ $growth >= 0 ? 'success' : 'danger' }}">
                                                {{ $growth >= 0 ? '+' : '' }}{{ number_format($growth, 1) }}%
                                            </span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Income vs Expenses Chart
    const ctx = document.getElementById('incomeExpenseChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Income', 'Expenses'],
            datasets: [{
                data: [{{ $profitLoss['total_income'] }}, {{ $profitLoss['total_expenses'] }}],
                backgroundColor: ['#28a745', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function exportFinancialReport() {
    Swal.fire({
        title: 'Export Financial Report',
        text: 'Export financial report as Excel file?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Export',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implement export functionality
            Swal.fire('Coming Soon', 'Export functionality will be available soon.', 'info');
        }
    });
}
</script>
@endsection
