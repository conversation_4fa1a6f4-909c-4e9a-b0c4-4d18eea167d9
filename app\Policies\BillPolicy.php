<?php

namespace App\Policies;

use App\Models\Bill;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class BillPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // All authenticated users can view bills
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Bill $bill): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin can view all bills
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can view bills for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($bill->property_id && $user->ownedProperties->contains($bill->property_id)) {
                return true;
            }
            if ($bill->unit && $user->ownedProperties->contains($bill->unit->property_id)) {
                return true;
            }
        }

        // Tenants can view their own bills
        if (in_array('tenant', $userRoles)) {
            return $bill->tenant_id === $user->id;
        }

        // Property managers and staff can view bills for properties they manage
        if (in_array('property_manager', $userRoles) || in_array('maintenance_staff', $userRoles) || in_array('receptionist', $userRoles)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin, property owners, and property managers can create bills
        return in_array('admin', $userRoles) || 
               in_array('property_owner', $userRoles) || 
               in_array('property_manager', $userRoles);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Bill $bill): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Admin can update all bills
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can update bills for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($bill->property_id && $user->ownedProperties->contains($bill->property_id)) {
                return true;
            }
            if ($bill->unit && $user->ownedProperties->contains($bill->unit->property_id)) {
                return true;
            }
        }

        // Property managers can update bills
        if (in_array('property_manager', $userRoles)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Bill $bill): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Only admin and property owners can delete bills
        if (in_array('admin', $userRoles)) {
            return true;
        }

        // Property owners can delete bills for their properties
        if (in_array('property_owner', $userRoles)) {
            if ($bill->property_id && $user->ownedProperties->contains($bill->property_id)) {
                return true;
            }
            if ($bill->unit && $user->ownedProperties->contains($bill->unit->property_id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Bill $bill): bool
    {
        return $this->delete($user, $bill);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Bill $bill): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();

        // Only admin can force delete bills
        return in_array('admin', $userRoles);
    }

    /**
     * Determine whether the user can mark the bill as paid.
     */
    public function markAsPaid(User $user, Bill $bill): bool
    {
        return $this->update($user, $bill);
    }

    /**
     * Determine whether the user can send reminders for the bill.
     */
    public function sendReminder(User $user, Bill $bill): bool
    {
        return $this->update($user, $bill);
    }
}
