@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ $pageTitle ?? 'User Management' }}</h5>
                <div>
                    @if(auth()->user()->hasRole('admin') && isset($propertyOwners))
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="ri-building-line me-1"></i>View by Owner
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                    <i class="ri-group-line me-1"></i>All Users
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                @foreach($propertyOwners as $owner)
                                    <li><a class="dropdown-item" href="{{ route('admin.users.index', ['owner_id' => $owner->id]) }}">
                                        <i class="ri-user-line me-1"></i>{{ $owner->name }}
                                        <span class="badge bg-primary ms-1">{{ $owner->employees_count }}</span>
                                    </a></li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Add New User
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Access Level Info -->
                <div class="alert alert-info border-0 mb-4">
                    <div class="d-flex align-items-center">
                        @if(auth()->user()->hasRole('admin'))
                            <div class="flex-shrink-0">
                                <i class="ri-shield-check-line fs-2 text-success"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="alert-heading mb-1">Administrator Access</h6>
                                <p class="mb-0">You have full system access. You can create, edit, delete, activate, and deactivate all user types including other admins and property owners.</p>
                            </div>
                        @elseif(auth()->user()->hasRole('property_owner'))
                            <div class="flex-shrink-0">
                                <i class="ri-building-line fs-2 text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="alert-heading mb-1">Property Owner Access</h6>
                                <p class="mb-0">You can manage your condominium staff. Create, edit, delete, activate, and deactivate accounts for tenants, receptionists, and maintenance staff.</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('admin.users.index') }}" class="d-flex">
                            <input type="text" name="search" class="form-control me-2"
                                   placeholder="Search by name or email..."
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                            @if(request('search') || request('role'))
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary ms-2">
                                    <i class="ri-close-line"></i>
                                </a>
                            @endif
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('admin.users.index') }}" class="d-flex justify-content-end">
                            <select name="role" class="form-select me-2" style="width: auto;" onchange="this.form.submit()">
                                <option value="">All Roles</option>
                                @foreach(\Spatie\Permission\Models\Role::all() as $role)
                                    <option value="{{ $role->name }}" {{ request('role') == $role->name ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $role->name)) }}
                                    </option>
                                @endforeach
                            </select>
                            @if(request('search'))
                                <input type="hidden" name="search" value="{{ request('search') }}">
                            @endif
                        </form>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>User Type</th>
                                <th>Role</th>
                                @if(auth()->user()->hasRole('admin'))
                                    <th>Created By</th>
                                @endif
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($users as $user)
                                <tr>
                                    <td>{{ $user->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2" style="width: 40px; height: 40px;">
                                                <img src="{{ $user->profile_photo_url }}"
                                                     alt="{{ $user->name }}"
                                                     class="rounded-circle border"
                                                     style="width: 100%; height: 100%; object-fit: cover;">
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->name }}</h6>
                                                <small class="text-muted">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>{{ $user->phone ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</span>
                                    </td>
                                    <td>
                                        @foreach($user->roles as $role)
                                            <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                        @endforeach
                                    </td>
                                    @if(auth()->user()->hasRole('admin'))
                                        <td>
                                            @if($user->creator)
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-info rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="ri-building-line text-white"></i>
                                                    </div>
                                                    <div>
                                                        <a href="{{ route('admin.users.show', $user->creator) }}" class="text-decoration-none">
                                                            {{ $user->creator->name }}
                                                        </a>
                                                        <br><small class="text-muted">Property Owner</small>
                                                    </div>
                                                </div>
                                            @elseif($user->hasRole('admin'))
                                                <span class="badge bg-warning">
                                                    <i class="ri-shield-check-line me-1"></i>System Admin
                                                </span>
                                            @elseif($user->hasRole('property_owner'))
                                                <span class="badge bg-info">
                                                    <i class="ri-building-line me-1"></i>Independent Owner
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    @endif
                                    <td>
                                        @if($user->is_active)
                                            <span class="badge bg-success">
                                                <i class="ri-check-line me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="ri-close-line me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                    <td>{{ $user->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-info" title="View">
                                                <i class="ri-eye-line"></i>
                                            </a>

                                            @php
                                                $canManage = $user->canBeManagedBy(auth()->user());
                                            @endphp

                                            @if($canManage)
                                                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-outline-warning" title="Edit User">
                                                    <i class="ri-edit-line"></i>
                                                </a>

                                                @if($user->id !== auth()->id())
                                                    @if($user->is_active)
                                                        <button type="button" class="btn btn-outline-secondary deactivate-user"
                                                                title="Deactivate User"
                                                                data-user-id="{{ $user->id }}"
                                                                data-user-name="{{ $user->name }}"
                                                                data-url="{{ route('admin.users.deactivate', $user) }}">
                                                            <i class="ri-pause-circle-line"></i>
                                                        </button>
                                                    @else
                                                        <button type="button" class="btn btn-outline-success activate-user"
                                                                title="Activate User"
                                                                data-user-id="{{ $user->id }}"
                                                                data-user-name="{{ $user->name }}"
                                                                data-url="{{ route('admin.users.activate', $user) }}">
                                                            <i class="ri-play-circle-line"></i>
                                                        </button>
                                                    @endif

                                                    <button type="button" class="btn btn-outline-danger delete-user"
                                                            title="Delete User"
                                                            data-user-id="{{ $user->id }}"
                                                            data-user-name="{{ $user->name }}"
                                                            data-url="{{ route('admin.users.destroy', $user) }}">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ri-user-line fs-1"></i>
                                            <p class="mt-2">No users found</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $users->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activate User
    document.querySelectorAll('.activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Activate User Account',
                html: `Are you sure you want to <strong>activate</strong> the account for:<br><br><strong>${userName}</strong>?<br><br>The user will be able to login and access the system.`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-play-circle-line me-1"></i>Yes, Activate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true,
                customClass: {
                    confirmButton: 'btn btn-success',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Activating...',
                        text: 'Please wait while we activate the user account.',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Deactivate User
    document.querySelectorAll('.deactivate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Deactivate User Account',
                html: `Are you sure you want to <strong>deactivate</strong> the account for:<br><br><strong>${userName}</strong>?<br><br>The user will be logged out and unable to access the system until reactivated.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-pause-circle-line me-1"></i>Yes, Deactivate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true,
                customClass: {
                    confirmButton: 'btn btn-warning',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Deactivating...',
                        text: 'Please wait while we deactivate the user account.',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Delete User
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;

            Swal.fire({
                title: 'Delete User Account',
                html: `Are you sure you want to <strong>permanently delete</strong> the account for:<br><br><strong>${userName}</strong>?<br><br><span class="text-danger"><i class="ri-alert-line"></i> This action cannot be undone!</span>`,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true,
                customClass: {
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Deleting...',
                        text: 'Please wait while we delete the user account.',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
