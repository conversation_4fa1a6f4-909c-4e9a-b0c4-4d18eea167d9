<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ObfuscateUrls
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only process HTML responses
        if ($response->headers->get('Content-Type') && 
            strpos($response->headers->get('Content-Type'), 'text/html') !== false) {
            
            $content = $response->getContent();
            
            // Add additional obfuscation to URLs in the response
            $content = $this->obfuscateUrlsInContent($content);
            
            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Obfuscate URLs in HTML content
     */
    private function obfuscateUrlsInContent($content)
    {
        // Add random query parameters to make URLs longer and more complex
        $patterns = [
            // Match href attributes
            '/href="([^"]*\/(?:properties|units|users|tasks|notifications|documents)\/[^"]*)"/',
            // Match action attributes in forms
            '/action="([^"]*\/(?:properties|units|users|tasks|notifications|documents)\/[^"]*)"/',
        ];

        foreach ($patterns as $pattern) {
            $content = preg_replace_callback($pattern, function ($matches) {
                $url = $matches[1];
                
                // Add random query parameters
                $separator = strpos($url, '?') !== false ? '&' : '?';
                $randomParams = $this->generateRandomParams();
                
                return str_replace($matches[1], $url . $separator . $randomParams, $matches[0]);
            }, $content);
        }

        return $content;
    }

    /**
     * Generate random query parameters
     */
    private function generateRandomParams()
    {
        $params = [];
        $paramNames = ['ref', 'src', 'utm', 'token', 'session', 'cache', 'ver', 'ts'];
        
        // Add 3-5 random parameters
        $numParams = rand(3, 5);
        
        for ($i = 0; $i < $numParams; $i++) {
            $paramName = $paramNames[array_rand($paramNames)] . rand(1, 999);
            $paramValue = $this->generateRandomString(rand(8, 16));
            $params[] = $paramName . '=' . $paramValue;
        }
        
        return implode('&', $params);
    }

    /**
     * Generate a random string
     */
    private function generateRandomString($length)
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }
}
