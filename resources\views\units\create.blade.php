@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-home-add-line text-primary me-2"></i>Create New Unit
                        </h4>
                        <p class="text-muted mb-0">
                            Add a new unit to the property management system
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('units.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Units
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('units.store') }}" method="POST" id="createUnitForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Property Selection -->
                            <div class="mb-3">
                                <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                <select class="form-select @error('property_id') is-invalid @enderror" 
                                        id="property_id" name="property_id" required>
                                    <option value="">Select Property</option>
                                    @foreach($properties as $property)
                                        <option value="{{ $property->id }}" {{ old('property_id') == $property->id ? 'selected' : '' }}>
                                            {{ $property->name }} - {{ $property->address }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('property_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Unit Number -->
                            <div class="mb-3">
                                <label for="unit_number" class="form-label">Unit Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('unit_number') is-invalid @enderror" 
                                       id="unit_number" name="unit_number" value="{{ old('unit_number') }}" 
                                       placeholder="e.g., 101, A-1, 2B" required>
                                @error('unit_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Floor -->
                            <div class="mb-3">
                                <label for="floor" class="form-label">Floor</label>
                                <input type="number" class="form-control @error('floor') is-invalid @enderror" 
                                       id="floor" name="floor" value="{{ old('floor') }}" 
                                       placeholder="e.g., 1, 2, 3" min="0" max="100">
                                @error('floor')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Unit Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Unit Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="studio" {{ old('type') == 'studio' ? 'selected' : '' }}>Studio</option>
                                    <option value="1_bedroom" {{ old('type') == '1_bedroom' ? 'selected' : '' }}>1 Bedroom</option>
                                    <option value="2_bedroom" {{ old('type') == '2_bedroom' ? 'selected' : '' }}>2 Bedroom</option>
                                    <option value="3_bedroom" {{ old('type') == '3_bedroom' ? 'selected' : '' }}>3 Bedroom</option>
                                    <option value="4_bedroom" {{ old('type') == '4_bedroom' ? 'selected' : '' }}>4 Bedroom</option>
                                    <option value="penthouse" {{ old('type') == 'penthouse' ? 'selected' : '' }}>Penthouse</option>
                                    <option value="commercial" {{ old('type') == 'commercial' ? 'selected' : '' }}>Commercial</option>
                                    <option value="office" {{ old('type') == 'office' ? 'selected' : '' }}>Office</option>
                                    <option value="retail" {{ old('type') == 'retail' ? 'selected' : '' }}>Retail</option>
                                    <option value="other" {{ old('type') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Size -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="size" class="form-label">Size (sq ft)</label>
                                        <input type="number" class="form-control @error('size') is-invalid @enderror" 
                                               id="size" name="size" value="{{ old('size') }}" 
                                               placeholder="e.g., 1200" min="1" step="0.01">
                                        @error('size')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bedrooms" class="form-label">Bedrooms</label>
                                        <input type="number" class="form-control @error('bedrooms') is-invalid @enderror" 
                                               id="bedrooms" name="bedrooms" value="{{ old('bedrooms') }}" 
                                               placeholder="e.g., 2" min="0" max="10">
                                        @error('bedrooms')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Bathrooms and Parking -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bathrooms" class="form-label">Bathrooms</label>
                                        <input type="number" class="form-control @error('bathrooms') is-invalid @enderror" 
                                               id="bathrooms" name="bathrooms" value="{{ old('bathrooms') }}" 
                                               placeholder="e.g., 2" min="0" max="10" step="0.5">
                                        @error('bathrooms')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="parking_spaces" class="form-label">Parking Spaces</label>
                                        <input type="number" class="form-control @error('parking_spaces') is-invalid @enderror" 
                                               id="parking_spaces" name="parking_spaces" value="{{ old('parking_spaces') }}" 
                                               placeholder="e.g., 1" min="0" max="5">
                                        @error('parking_spaces')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4" 
                                          placeholder="Additional details about the unit...">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Status -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="available" {{ old('status', 'available') == 'available' ? 'selected' : '' }}>Available</option>
                                    <option value="occupied" {{ old('status') == 'occupied' ? 'selected' : '' }}>Occupied</option>
                                    <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                                    <option value="reserved" {{ old('status') == 'reserved' ? 'selected' : '' }}>Reserved</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Rent Amount -->
                            <div class="mb-3">
                                <label for="rent_amount" class="form-label">Monthly Rent</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('rent_amount') is-invalid @enderror" 
                                           id="rent_amount" name="rent_amount" value="{{ old('rent_amount') }}" 
                                           placeholder="0.00" min="0" step="0.01">
                                    @error('rent_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Security Deposit -->
                            <div class="mb-3">
                                <label for="security_deposit" class="form-label">Security Deposit</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('security_deposit') is-invalid @enderror" 
                                           id="security_deposit" name="security_deposit" value="{{ old('security_deposit') }}" 
                                           placeholder="0.00" min="0" step="0.01">
                                    @error('security_deposit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Tenant Assignment -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Assign Tenant (Optional)</label>
                                <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id">
                                    <option value="">No Tenant Assigned</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name }} ({{ $tenant->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Lease Dates (if tenant assigned) -->
                            <div id="lease-dates" style="display: none;">
                                <div class="mb-3">
                                    <label for="lease_start_date" class="form-label">Lease Start Date</label>
                                    <input type="date" class="form-control @error('lease_start_date') is-invalid @enderror" 
                                           id="lease_start_date" name="lease_start_date" value="{{ old('lease_start_date') }}">
                                    @error('lease_start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="lease_end_date" class="form-label">Lease End Date</label>
                                    <input type="date" class="form-control @error('lease_end_date') is-invalid @enderror" 
                                           id="lease_end_date" name="lease_end_date" value="{{ old('lease_end_date') }}">
                                    @error('lease_end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Features -->
                            <div class="mb-3">
                                <label class="form-label">Features</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_balcony" name="features[]" value="balcony" 
                                           {{ in_array('balcony', old('features', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_balcony">Balcony</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_ac" name="features[]" value="air_conditioning" 
                                           {{ in_array('air_conditioning', old('features', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_ac">Air Conditioning</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_heating" name="features[]" value="heating" 
                                           {{ in_array('heating', old('features', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_heating">Heating</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="furnished" name="features[]" value="furnished" 
                                           {{ in_array('furnished', old('features', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="furnished">Furnished</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="pet_friendly" name="features[]" value="pet_friendly" 
                                           {{ in_array('pet_friendly', old('features', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="pet_friendly">Pet Friendly</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('units.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create Unit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide lease dates based on tenant selection
    const tenantSelect = document.getElementById('tenant_id');
    const leaseDates = document.getElementById('lease-dates');
    const statusSelect = document.getElementById('status');

    function toggleLeaseDates() {
        if (tenantSelect.value) {
            leaseDates.style.display = 'block';
            statusSelect.value = 'occupied';
        } else {
            leaseDates.style.display = 'none';
            statusSelect.value = 'available';
        }
    }

    tenantSelect.addEventListener('change', toggleLeaseDates);
    
    // Initialize on page load
    toggleLeaseDates();

    // Form validation
    const form = document.getElementById('createUnitForm');
    form.addEventListener('submit', function(e) {
        const propertyId = document.getElementById('property_id').value;
        const unitNumber = document.getElementById('unit_number').value;
        const tenantId = document.getElementById('tenant_id').value;
        const leaseStartDate = document.getElementById('lease_start_date').value;
        const leaseEndDate = document.getElementById('lease_end_date').value;

        // Validate required fields
        if (!propertyId || !unitNumber) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Validate lease dates if tenant is assigned
        if (tenantId && (!leaseStartDate || !leaseEndDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Lease Dates',
                text: 'Please provide lease start and end dates when assigning a tenant.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Validate lease date logic
        if (leaseStartDate && leaseEndDate && new Date(leaseStartDate) >= new Date(leaseEndDate)) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Lease Dates',
                text: 'Lease end date must be after the start date.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });

    // Auto-suggest unit number based on property and floor
    const floorInput = document.getElementById('floor');
    const unitNumberInput = document.getElementById('unit_number');

    floorInput.addEventListener('change', function() {
        const floor = this.value;
        if (floor && !unitNumberInput.value) {
            // Suggest unit number format: floor + 01, 02, etc.
            const suggestedNumber = floor.padStart(1, '0') + '01';
            unitNumberInput.placeholder = `Suggested: ${suggestedNumber}`;
        }
    });
});
</script>
@endsection
