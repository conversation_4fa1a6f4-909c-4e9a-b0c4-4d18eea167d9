<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-flashlight-line text-primary me-2"></i>Add Utility Bill
                        </h4>
                        <p class="text-muted mb-0">
                            Record a new utility bill for property management
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('utility-bills.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Utility Bills
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form action="<?php echo e(route('utility-bills.store')); ?>" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            
            <div class="row">
                <!-- Bill Information -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Bill Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="utility_type" class="form-label">Utility Type <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['utility_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="utility_type" name="utility_type" required>
                                        <option value="">Select Utility Type</option>
                                        <option value="electricity" <?php echo e(old('utility_type') == 'electricity' ? 'selected' : ''); ?>>Electricity</option>
                                        <option value="water" <?php echo e(old('utility_type') == 'water' ? 'selected' : ''); ?>>Water</option>
                                        <option value="gas" <?php echo e(old('utility_type') == 'gas' ? 'selected' : ''); ?>>Gas</option>
                                        <option value="internet" <?php echo e(old('utility_type') == 'internet' ? 'selected' : ''); ?>>Internet</option>
                                        <option value="cable_tv" <?php echo e(old('utility_type') == 'cable_tv' ? 'selected' : ''); ?>>Cable TV</option>
                                        <option value="waste_management" <?php echo e(old('utility_type') == 'waste_management' ? 'selected' : ''); ?>>Waste Management</option>
                                        <option value="other" <?php echo e(old('utility_type') == 'other' ? 'selected' : ''); ?>>Other</option>
                                    </select>
                                    <?php $__errorArgs = ['utility_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="property_id" class="form-label">Property <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="property_id" name="property_id" required>
                                        <option value="">Select Property</option>
                                        <!-- Properties will be populated here -->
                                        <option value="1" <?php echo e(old('property_id') == '1' ? 'selected' : ''); ?>>Sample Property 1</option>
                                        <option value="2" <?php echo e(old('property_id') == '2' ? 'selected' : ''); ?>>Sample Property 2</option>
                                    </select>
                                    <?php $__errorArgs = ['property_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="bill_number" class="form-label">Bill Number</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['bill_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="bill_number" name="bill_number" value="<?php echo e(old('bill_number')); ?>" 
                                           placeholder="e.g., ELC-2024-001">
                                    <?php $__errorArgs = ['bill_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="provider_name" class="form-label">Service Provider <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['provider_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="provider_name" name="provider_name" value="<?php echo e(old('provider_name')); ?>" 
                                           placeholder="e.g., City Electric Company" required>
                                    <?php $__errorArgs = ['provider_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="billing_period_start" class="form-label">Billing Period Start <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['billing_period_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="billing_period_start" name="billing_period_start" 
                                           value="<?php echo e(old('billing_period_start')); ?>" required>
                                    <?php $__errorArgs = ['billing_period_start'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="billing_period_end" class="form-label">Billing Period End <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['billing_period_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="billing_period_end" name="billing_period_end" 
                                           value="<?php echo e(old('billing_period_end')); ?>" required>
                                    <?php $__errorArgs = ['billing_period_end'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="due_date" name="due_date" value="<?php echo e(old('due_date')); ?>" required>
                                    <?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Usage & Amount -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Usage & Amount Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="previous_reading" class="form-label">Previous Reading</label>
                                    <input type="number" step="0.01" class="form-control <?php $__errorArgs = ['previous_reading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="previous_reading" name="previous_reading" value="<?php echo e(old('previous_reading')); ?>" 
                                           placeholder="0.00">
                                    <?php $__errorArgs = ['previous_reading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="current_reading" class="form-label">Current Reading</label>
                                    <input type="number" step="0.01" class="form-control <?php $__errorArgs = ['current_reading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="current_reading" name="current_reading" value="<?php echo e(old('current_reading')); ?>" 
                                           placeholder="0.00">
                                    <?php $__errorArgs = ['current_reading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="usage_amount" class="form-label">Usage Amount</label>
                                    <input type="number" step="0.01" class="form-control <?php $__errorArgs = ['usage_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="usage_amount" name="usage_amount" value="<?php echo e(old('usage_amount')); ?>" 
                                           placeholder="0.00" readonly>
                                    <?php $__errorArgs = ['usage_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="rate_per_unit" class="form-label">Rate per Unit</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.0001" class="form-control <?php $__errorArgs = ['rate_per_unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="rate_per_unit" name="rate_per_unit" value="<?php echo e(old('rate_per_unit')); ?>" 
                                               placeholder="0.0000">
                                        <?php $__errorArgs = ['rate_per_unit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="total_amount" class="form-label">Total Amount <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" class="form-control <?php $__errorArgs = ['total_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="total_amount" name="total_amount" value="<?php echo e(old('total_amount')); ?>" 
                                               placeholder="0.00" required>
                                        <?php $__errorArgs = ['total_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="notes" name="notes" rows="3" 
                                          placeholder="Additional notes about this utility bill"><?php echo e(old('notes')); ?></textarea>
                                <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="bill_attachment" class="form-label">Bill Attachment</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['bill_attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="bill_attachment" name="bill_attachment" accept=".pdf,.jpg,.jpeg,.png">
                                <?php $__errorArgs = ['bill_attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">Upload the original bill (PDF, JPG, PNG). Max size: 5MB</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions & Summary -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Bill Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Utility Type</small>
                                <div id="summary-utility-type" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Property</small>
                                <div id="summary-property" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Billing Period</small>
                                <div id="summary-period" class="fw-bold">-</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Total Amount</small>
                                <div id="summary-amount" class="fw-bold text-primary fs-4">$0.00</div>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i>Save Utility Bill
                                </button>
                                <button type="button" class="btn btn-success" onclick="saveAndDistribute()">
                                    <i class="ri-share-line me-1"></i>Save & Distribute
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Utility bill creation scripts
    console.log('Utility Bill Create page loaded');
    
    // Calculate usage amount
    function calculateUsage() {
        const previous = parseFloat(document.getElementById('previous_reading').value) || 0;
        const current = parseFloat(document.getElementById('current_reading').value) || 0;
        const usage = current - previous;
        document.getElementById('usage_amount').value = usage >= 0 ? usage.toFixed(2) : '';
        
        // Calculate total if rate is available
        const rate = parseFloat(document.getElementById('rate_per_unit').value) || 0;
        if (rate > 0 && usage > 0) {
            document.getElementById('total_amount').value = (usage * rate).toFixed(2);
        }
        updateSummary();
    }
    
    // Update summary
    function updateSummary() {
        const utilityType = document.getElementById('utility_type').selectedOptions[0]?.text || '-';
        const property = document.getElementById('property_id').selectedOptions[0]?.text || '-';
        const startDate = document.getElementById('billing_period_start').value;
        const endDate = document.getElementById('billing_period_end').value;
        const amount = document.getElementById('total_amount').value || '0.00';
        
        document.getElementById('summary-utility-type').textContent = utilityType;
        document.getElementById('summary-property').textContent = property;
        document.getElementById('summary-period').textContent = startDate && endDate ? `${startDate} to ${endDate}` : '-';
        document.getElementById('summary-amount').textContent = `$${parseFloat(amount).toFixed(2)}`;
    }
    
    function saveAndDistribute() {
        // Implementation for save and distribute
        console.log('Saving and distributing utility bill...');
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('previous_reading').addEventListener('input', calculateUsage);
        document.getElementById('current_reading').addEventListener('input', calculateUsage);
        document.getElementById('rate_per_unit').addEventListener('input', calculateUsage);
        document.getElementById('total_amount').addEventListener('input', updateSummary);
        document.getElementById('utility_type').addEventListener('change', updateSummary);
        document.getElementById('property_id').addEventListener('change', updateSummary);
        document.getElementById('billing_period_start').addEventListener('change', updateSummary);
        document.getElementById('billing_period_end').addEventListener('change', updateSummary);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/billing/utility-bills/create.blade.php ENDPATH**/ ?>