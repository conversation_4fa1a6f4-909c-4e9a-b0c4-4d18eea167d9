@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-alarm-warning-line text-warning me-2"></i>Expiring Documents
                        </h4>
                        <p class="text-muted mb-0">
                            Documents that are expiring soon or have already expired
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('documents.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Upload Document
                        </a>
                        <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>All Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $expiredCount ?? 0 }}</h3>
                                <p class="mb-0">Expired Documents</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-error-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $expiringSoonCount ?? 0 }}</h3>
                                <p class="mb-0">Expiring Soon</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-alarm-warning-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $totalCount ?? 0 }}</h3>
                                <p class="mb-0">Total Documents</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-file-list-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expired Documents -->
        @if(isset($expiredDocuments) && $expiredDocuments->count() > 0)
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="ri-error-warning-line me-2"></i>Expired Documents ({{ $expiredDocuments->count() }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Property</th>
                                <th>Expiry Date</th>
                                <th>Days Overdue</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($expiredDocuments as $document)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if(in_array($document->file_type, ['jpg', 'jpeg', 'png']))
                                                    <i class="ri-image-line fs-4 text-info"></i>
                                                @elseif($document->file_type === 'pdf')
                                                    <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                                @else
                                                    <i class="ri-file-line fs-4 text-muted"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-1">{{ $document->title }}</h6>
                                                <small class="text-muted">{{ $document->file_name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ ucfirst(str_replace('_', ' ', $document->type)) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($document->property)
                                            <span class="text-primary">{{ $document->property->name }}</span>
                                        @else
                                            <span class="text-muted">No property</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-danger fw-bold">
                                            {{ $document->expiry_date->format('M d, Y') }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            {{ abs($document->expiry_date->diffInDays(now())) }} days
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('documents.show', $document) }}" class="btn btn-outline-primary" title="View">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="{{ route('documents.edit', $document) }}" class="btn btn-outline-warning" title="Edit">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <a href="{{ route('documents.download', $document) }}" class="btn btn-outline-success" title="Download">
                                                <i class="ri-download-line"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Expiring Soon Documents -->
        @if(isset($expiringSoonDocuments) && $expiringSoonDocuments->count() > 0)
        <div class="card mb-4">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="ri-alarm-warning-line me-2"></i>Expiring Soon ({{ $expiringSoonDocuments->count() }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Property</th>
                                <th>Expiry Date</th>
                                <th>Days Remaining</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($expiringSoonDocuments as $document)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if(in_array($document->file_type, ['jpg', 'jpeg', 'png']))
                                                    <i class="ri-image-line fs-4 text-info"></i>
                                                @elseif($document->file_type === 'pdf')
                                                    <i class="ri-file-pdf-line fs-4 text-danger"></i>
                                                @else
                                                    <i class="ri-file-line fs-4 text-muted"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-1">{{ $document->title }}</h6>
                                                <small class="text-muted">{{ $document->file_name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ ucfirst(str_replace('_', ' ', $document->type)) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($document->property)
                                            <span class="text-primary">{{ $document->property->name }}</span>
                                        @else
                                            <span class="text-muted">No property</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-warning fw-bold">
                                            {{ $document->expiry_date->format('M d, Y') }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            {{ $document->expiry_date->diffInDays(now()) }} days
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('documents.show', $document) }}" class="btn btn-outline-primary" title="View">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="{{ route('documents.edit', $document) }}" class="btn btn-outline-warning" title="Edit">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <a href="{{ route('documents.download', $document) }}" class="btn btn-outline-success" title="Download">
                                                <i class="ri-download-line"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- No Expiring Documents -->
        @if((!isset($expiredDocuments) || $expiredDocuments->count() === 0) && (!isset($expiringSoonDocuments) || $expiringSoonDocuments->count() === 0))
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="ri-shield-check-line fs-1 text-success mb-3"></i>
                <h4 class="text-success mb-2">All Documents Are Up to Date!</h4>
                <p class="text-muted mb-4">
                    Great news! You don't have any documents that are expired or expiring soon.
                </p>
                <div class="d-flex justify-content-center gap-2">
                    <a href="{{ route('documents.index') }}" class="btn btn-outline-primary">
                        <i class="ri-file-list-line me-1"></i>View All Documents
                    </a>
                    <a href="{{ route('documents.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Upload New Document
                    </a>
                </div>
            </div>
        </div>
        @endif

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" onclick="sendExpiryAlerts()">
                                <i class="ri-mail-send-line me-2"></i>Send Expiry Alerts
                            </button>
                            <a href="{{ route('documents.create') }}" class="btn btn-primary">
                                <i class="ri-upload-line me-2"></i>Upload New Document
                            </a>
                            <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                                <i class="ri-file-list-line me-2"></i>View All Documents
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Document Types</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-2">
                                <small class="text-muted">Insurance</small>
                                <div class="fw-bold">{{ $typeStats['insurance'] ?? 0 }}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Fire Certificate</small>
                                <div class="fw-bold">{{ $typeStats['fire_certificate'] ?? 0 }}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Contracts</small>
                                <div class="fw-bold">{{ $typeStats['contract'] ?? 0 }}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Permits</small>
                                <div class="fw-bold">{{ $typeStats['permit'] ?? 0 }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function sendExpiryAlerts() {
    Swal.fire({
        title: 'Send Expiry Alerts',
        text: 'This will send email notifications for all expiring documents. Continue?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="ri-mail-send-line me-1"></i>Send Alerts',
        cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Sending Alerts...',
                text: 'Please wait while we send the expiry alerts.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send request
            fetch('{{ route("documents.send-expiry-alerts") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: data.message || 'Expiry alerts sent successfully.',
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    throw new Error(data.message || 'Failed to send alerts');
                }
            })
            .catch(error => {
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to send expiry alerts.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    });
}
</script>
@endsection
