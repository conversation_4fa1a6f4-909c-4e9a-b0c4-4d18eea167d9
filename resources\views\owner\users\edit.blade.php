@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Edit Employee: {{ $user->name }}</h5>
                <div>
                    <a href="{{ route('owner.users.show', $user) }}" class="btn btn-outline-info me-2">
                        <i class="ri-eye-line me-1"></i>View Details
                    </a>
                    <a href="{{ route('owner.users.index') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Employees
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Info Alert -->
                <div class="alert alert-info border-0 mb-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ri-building-line fs-2 text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="alert-heading mb-1">Property Owner Employee Management</h6>
                            <p class="mb-0">You can edit your condominium staff details. Available roles: <strong>Tenant</strong>, <strong>Receptionist</strong>, and <strong>Maintenance Staff</strong>.</p>
                        </div>
                    </div>
                </div>

                <form action="{{ route('owner.users.update', $user) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <!-- Profile Photo Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title">
                                        <i class="ri-camera-line text-primary me-2"></i>Profile Photo
                                    </h6>

                                    <!-- Current Profile Photo Preview -->
                                    <div class="mb-3">
                                        <div class="profile-photo-preview" style="width: 120px; height: 120px; margin: 0 auto;">
                                            <img id="photo-preview"
                                                 src="{{ $user->profile_photo_url }}"
                                                 alt="Profile Preview"
                                                 class="rounded-circle border border-primary"
                                                 style="width: 100%; height: 100%; object-fit: cover;">
                                        </div>
                                        @if($user->hasProfilePhoto())
                                            <small class="text-muted d-block mt-2">
                                                <i class="ri-check-line text-success"></i> Current photo uploaded
                                            </small>
                                        @else
                                            <small class="text-muted d-block mt-2">
                                                <i class="ri-information-line"></i> Using default avatar
                                            </small>
                                        @endif
                                    </div>

                                    <!-- File Input -->
                                    <div class="mb-3">
                                        <input type="file" class="form-control @error('profile_photo') is-invalid @enderror"
                                               id="profile_photo" name="profile_photo" accept="image/*"
                                               onchange="previewProfilePhoto(this)">
                                        @error('profile_photo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">
                                            <i class="ri-information-line"></i> Upload a new profile photo (JPG, PNG, GIF - Max: 2MB). Leave empty to keep current photo.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required
                                       placeholder="Enter employee's full name">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror"
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required
                                       placeholder="Enter email address">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password"
                                       placeholder="Leave blank to keep current password">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave blank to keep the current password</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation"
                                       placeholder="Confirm new password">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}"
                                       placeholder="Enter phone number">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_type" class="form-label">Employee Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('user_type') is-invalid @enderror" 
                                        id="user_type" name="user_type" required>
                                    <option value="">Select Employee Type</option>
                                    <option value="tenant" {{ old('user_type', $user->user_type) == 'tenant' ? 'selected' : '' }}>
                                        Tenant
                                    </option>
                                    <option value="receptionist" {{ old('user_type', $user->user_type) == 'receptionist' ? 'selected' : '' }}>
                                        Receptionist
                                    </option>
                                    <option value="maintenance_staff" {{ old('user_type', $user->user_type) == 'maintenance_staff' ? 'selected' : '' }}>
                                        Maintenance Staff
                                    </option>
                                </select>
                                @error('user_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="ri-building-line text-primary"></i> <strong>Property Owner Access:</strong> You can edit your condominium staff.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role Assignment <span class="text-danger">*</span></label>
                                <select class="form-select @error('role') is-invalid @enderror" 
                                        id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    @php
                                        $currentRole = $user->roles->first()->name ?? '';
                                    @endphp
                                    <option value="tenant" {{ old('role', $currentRole) == 'tenant' ? 'selected' : '' }}>
                                        Tenant - Condominium resident
                                    </option>
                                    <option value="receptionist" {{ old('role', $currentRole) == 'receptionist' ? 'selected' : '' }}>
                                        Receptionist - Front desk and visitor management
                                    </option>
                                    <option value="maintenance_staff" {{ old('role', $currentRole) == 'maintenance_staff' ? 'selected' : '' }}>
                                        Maintenance Staff - Building maintenance and repairs
                                    </option>
                                </select>
                                @error('role')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="ri-shield-check-line text-success"></i> <strong>Available Roles:</strong> You can assign staff roles for your condominium management.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="2"
                                          placeholder="Enter address">{{ old('address', $user->address) }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city', $user->city) }}"
                                       placeholder="Enter city">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                       id="state" name="state" value="{{ old('state', $user->state) }}"
                                       placeholder="Enter state">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label">Zip Code</label>
                                <input type="text" class="form-control @error('zip_code') is-invalid @enderror" 
                                       id="zip_code" name="zip_code" value="{{ old('zip_code', $user->zip_code) }}"
                                       placeholder="Enter zip code">
                                @error('zip_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Account Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="ri-information-line"></i> Uncheck to deactivate account. Employee won't be able to login when inactive.
                                </div>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('owner.users.index') }}" class="btn btn-outline-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i>Update Employee
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sync user_type and role fields
    const userTypeSelect = document.getElementById('user_type');
    const roleSelect = document.getElementById('role');

    userTypeSelect.addEventListener('change', function() {
        roleSelect.value = this.value;
    });

    roleSelect.addEventListener('change', function() {
        userTypeSelect.value = this.value;
    });
});

// Profile photo preview function
function previewProfilePhoto(input) {
    const preview = document.getElementById('photo-preview');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
        };

        reader.readAsDataURL(input.files[0]);
    }
}
</script>
@endsection
