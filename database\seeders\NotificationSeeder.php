<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;
use App\Models\Property;
use App\Models\Unit;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (admin) to receive notifications
        $user = User::first();

        if (!$user) {
            $this->command->info('No users found. Please run UserSeeder first.');
            return;
        }

        // Get first property and unit for related notifications
        $property = Property::first();
        $unit = Unit::first();

        // Create sample notifications
        $notifications = [
            [
                'title' => 'Lease Expiry Alert',
                'message' => 'Lease for Unit 101 is expiring in 15 days. Please contact the tenant for renewal.',
                'type' => 'lease_expiry',
                'priority' => 'high',
                'user_id' => $user->id,
                'related_property_id' => $property?->id,
                'related_unit_id' => $unit?->id,
                'is_read' => false,
                'created_at' => now()->subMinutes(30),
            ],
            [
                'title' => 'Document Expiry Warning',
                'message' => 'Fire certificate for Sunset Apartments is expiring on December 15, 2024.',
                'type' => 'document_expiry',
                'priority' => 'urgent',
                'user_id' => $user->id,
                'related_property_id' => $property?->id,
                'is_read' => false,
                'created_at' => now()->subHours(2),
            ],
            [
                'title' => 'Task Reminder',
                'message' => 'Monthly property inspection is due tomorrow. Please schedule the inspection.',
                'type' => 'task_reminder',
                'priority' => 'medium',
                'user_id' => $user->id,
                'related_property_id' => $property?->id,
                'is_read' => false,
                'created_at' => now()->subHours(6),
            ],
            [
                'title' => 'Payment Due Notice',
                'message' => 'Rent payment for Unit 102 is overdue by 5 days. Please follow up with tenant.',
                'type' => 'payment_due',
                'priority' => 'high',
                'user_id' => $user->id,
                'related_property_id' => $property?->id,
                'related_unit_id' => $unit?->id,
                'is_read' => true,
                'read_at' => now()->subHours(1),
                'created_at' => now()->subDays(1),
            ],
            [
                'title' => 'Maintenance Request',
                'message' => 'New maintenance request submitted for plumbing issue in Unit 205.',
                'type' => 'maintenance_request',
                'priority' => 'medium',
                'user_id' => $user->id,
                'related_property_id' => $property?->id,
                'is_read' => true,
                'read_at' => now()->subMinutes(45),
                'created_at' => now()->subDays(2),
            ],
            [
                'title' => 'System Alert',
                'message' => 'System backup completed successfully. All data has been backed up.',
                'type' => 'system_alert',
                'priority' => 'low',
                'user_id' => $user->id,
                'is_read' => false,
                'created_at' => now()->subDays(3),
            ],
            [
                'title' => 'Welcome to Property Management System',
                'message' => 'Welcome! Your property management system is now set up and ready to use.',
                'type' => 'general',
                'priority' => 'medium',
                'user_id' => $user->id,
                'is_read' => true,
                'read_at' => now()->subDays(1),
                'created_at' => now()->subWeek(),
            ],
        ];

        foreach ($notifications as $notificationData) {
            Notification::create($notificationData);
        }

        $this->command->info('Sample notifications created successfully!');
    }
}
