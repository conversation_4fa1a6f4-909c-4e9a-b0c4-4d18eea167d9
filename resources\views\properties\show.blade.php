@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Property Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="mb-2">
                            <i class="ri-building-line text-primary me-2"></i>
                            {{ $property->name }}
                        </h3>
                        <p class="text-muted mb-2">
                            <i class="ri-map-pin-line me-1"></i>
                            {{ $property->address }}, {{ $property->city }}, {{ $property->state }} {{ $property->zip_code }}
                        </p>
                        <div class="d-flex gap-2 mb-3">
                            <span class="badge bg-{{ $property->status == 'active' ? 'success' : ($property->status == 'maintenance' ? 'warning' : 'secondary') }}">
                                {{ ucfirst($property->status) }}
                            </span>
                            <span class="badge bg-info">
                                {{ ucfirst(str_replace('_', ' ', $property->property_type)) }}
                            </span>
                        </div>
                        @if($property->description)
                            <p class="text-muted">{{ $property->description }}</p>
                        @endif
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('properties.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Properties
                        </a>
                        @can('update', $property)
                            <a href="{{ route('properties.edit', $property) }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Property
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-home-line fs-1 text-primary mb-2"></i>
                        <h4 class="mb-1">{{ $stats['total_units'] }}</h4>
                        <p class="text-muted mb-0">Total Units</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-user-line fs-1 text-success mb-2"></i>
                        <h4 class="mb-1">{{ $stats['occupied_units'] }}</h4>
                        <p class="text-muted mb-0">Occupied Units</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-money-dollar-circle-line fs-1 text-info mb-2"></i>
                        <h4 class="mb-1">${{ number_format($stats['monthly_income'], 0) }}</h4>
                        <p class="text-muted mb-0">Monthly Income</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-pie-chart-line fs-1 text-warning mb-2"></i>
                        <h4 class="mb-1">{{ $stats['occupancy_rate'] }}%</h4>
                        <p class="text-muted mb-0">Occupancy Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Unit Status Overview -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ri-home-line text-primary me-2"></i>Unit Status Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success-subtle rounded">
                                    <h4 class="text-success mb-1">{{ $stats['occupied_units'] }}</h4>
                                    <small class="text-muted">Occupied</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-info-subtle rounded">
                                    <h4 class="text-info mb-1">{{ $stats['available_units'] }}</h4>
                                    <small class="text-muted">Available</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-warning-subtle rounded">
                                    <h4 class="text-warning mb-1">{{ $stats['maintenance_units'] }}</h4>
                                    <small class="text-muted">Maintenance</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-primary-subtle rounded">
                                    <h4 class="text-primary mb-1">{{ $stats['total_units'] }}</h4>
                                    <small class="text-muted">Total Units</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Complaints Overview -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ri-customer-service-line text-warning me-2"></i>Complaints Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-danger-subtle rounded">
                                    <h4 class="text-danger mb-1">{{ $complaints['open'] }}</h4>
                                    <small class="text-muted">Open</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-warning-subtle rounded">
                                    <h4 class="text-warning mb-1">{{ $complaints['in_progress'] }}</h4>
                                    <small class="text-muted">In Progress</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-success-subtle rounded">
                                    <h4 class="text-success mb-1">{{ $complaints['resolved'] }}</h4>
                                    <small class="text-muted">Resolved</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-info-subtle rounded">
                                    <h4 class="text-info mb-1">{{ $complaints['total'] }}</h4>
                                    <small class="text-muted">Total</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Financial Overview -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ri-money-dollar-box-line text-success me-2"></i>Financial Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Total Billed:</span>
                            <span class="fw-bold text-primary">${{ number_format($billing['total_billed'], 2) }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Amount Paid:</span>
                            <span class="fw-bold text-success">${{ number_format($billing['amount_paid'], 2) }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Amount Pending:</span>
                            <span class="fw-bold text-warning">${{ number_format($billing['amount_pending'], 2) }}</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>Monthly Income:</strong></span>
                            <span class="fw-bold text-info">${{ number_format($stats['monthly_income'], 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Visitors -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ri-user-follow-line text-info me-2"></i>Recent Visitors
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($recentVisitors->count() > 0)
                            @foreach($recentVisitors as $visitor)
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-0">{{ $visitor->name }}</h6>
                                        <small class="text-muted">
                                            Visiting: {{ $visitor->visitingUnit->unit_number ?? 'N/A' }}
                                            @if($visitor->visitingTenant)
                                                ({{ $visitor->visitingTenant->name }})
                                            @endif
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">
                                            {{ $visitor->check_in_time ? $visitor->check_in_time->format('M d, H:i') : 'N/A' }}
                                        </small>
                                        <br>
                                        <span class="badge bg-{{ $visitor->is_checked_in ? 'success' : 'secondary' }}">
                                            {{ $visitor->is_checked_in ? 'Checked In' : 'Checked Out' }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="ri-user-follow-line fs-2 text-muted"></i>
                                <p class="text-muted mb-0">No recent visitors</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Details -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ri-information-line text-primary me-2"></i>Property Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Property Name:</strong></td>
                                        <td>{{ $property->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Property Type:</strong></td>
                                        <td>{{ ucfirst(str_replace('_', ' ', $property->property_type)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Area:</strong></td>
                                        <td>{{ $property->total_area ? number_format($property->total_area, 2) . ' sq ft' : 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Units:</strong></td>
                                        <td>{{ $property->total_units }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Owner:</strong></td>
                                        <td>{{ $property->owner->name }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $property->address }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>City:</strong></td>
                                        <td>{{ $property->city }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>State:</strong></td>
                                        <td>{{ $property->state }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Zip Code:</strong></td>
                                        <td>{{ $property->zip_code }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Country:</strong></td>
                                        <td>{{ $property->country }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if($property->amenities && count($property->amenities) > 0)
                            <hr>
                            <h6>Amenities:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($property->amenities as $amenity)
                                    <span class="badge bg-light text-dark">{{ $amenity }}</span>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
