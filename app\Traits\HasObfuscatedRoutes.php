<?php

namespace App\Traits;

use App\Helpers\UrlObfuscator;

trait HasObfuscatedRoutes
{
    /**
     * Get the route key for the model.
     */
    public function getRouteKey()
    {
        return $this->encodeId($this->getKey());
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        $decodedId = $this->decodeId($value);
        
        if ($decodedId === null) {
            return null;
        }

        return $this->where($this->getRouteKeyName(), $decodedId)->first();
    }

    /**
     * Encode the ID to make it obfuscated
     */
    public function encodeId($id)
    {
        // Use the advanced URL obfuscator for maximum complexity
        return UrlObfuscator::obfuscate($id, class_basename($this));
    }

    /**
     * Decode the obfuscated ID
     */
    public function decodeId($encodedId)
    {
        // Use the advanced URL obfuscator for decoding
        return UrlObfuscator::deobfuscate($encodedId);
    }

    /**
     * Generate a random string
     */
    private function generateRandomString($length)
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }

    /**
     * Get obfuscated URL for a route
     */
    public function getObfuscatedUrl($route, $parameters = [])
    {
        $parameters = array_merge([$this->getRouteKey()], $parameters);
        return route($route, $parameters);
    }

    /**
     * Get the route key name for the model.
     */
    public function getRouteKeyName()
    {
        return $this->getKeyName();
    }
}
