@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-file-text-line text-primary me-2"></i>Document Details
                        </h4>
                        <p class="text-muted mb-0">
                            View document information and download file
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        @can('update', $document)
                            <a href="{{ route('documents.edit', $document) }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Document
                            </a>
                        @endcan
                        <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Document Details -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ $document->title }}</h5>
                            <div class="d-flex gap-2">
                                <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $document->type)) }}</span>
                                @if($document->is_expired)
                                    <span class="badge bg-danger">
                                        <i class="ri-alarm-warning-line me-1"></i>Expired
                                    </span>
                                @elseif($document->is_expiring_soon)
                                    <span class="badge bg-warning">
                                        <i class="ri-time-line me-1"></i>Expiring Soon
                                    </span>
                                @else
                                    <span class="badge bg-success">
                                        <i class="ri-check-line me-1"></i>Active
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Description -->
                        @if($document->description)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Description</h6>
                                <p class="mb-0">{{ $document->description }}</p>
                            </div>
                        @endif

                        <!-- File Information -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">File Information</h6>
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <div class="me-3">
                                    @if(in_array($document->file_type, ['jpg', 'jpeg', 'png']))
                                        <i class="ri-image-line fs-1 text-info"></i>
                                    @elseif($document->file_type === 'pdf')
                                        <i class="ri-file-pdf-line fs-1 text-danger"></i>
                                    @elseif(in_array($document->file_type, ['doc', 'docx']))
                                        <i class="ri-file-word-line fs-1 text-primary"></i>
                                    @else
                                        <i class="ri-file-line fs-1 text-muted"></i>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $document->file_name }}</h6>
                                    <p class="text-muted mb-2">{{ $document->file_size_formatted }} • {{ strtoupper($document->file_type) }}</p>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('documents.download', $document) }}" class="btn btn-sm btn-primary">
                                            <i class="ri-download-line me-1"></i>Download
                                        </a>
                                        @if(in_array($document->file_type, ['pdf', 'jpg', 'jpeg', 'png']))
                                            <a href="{{ route('documents.view', $document) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ri-external-link-line me-1"></i>View File
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Property and Unit Information -->
                        @if($document->property || $document->unit || $document->tenant)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Related Information</h6>
                                <div class="row">
                                    @if($document->property)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-building-line text-primary me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Property</small>
                                                    <strong>{{ $document->property->name }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($document->unit)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-home-line text-info me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Unit</small>
                                                    <strong>Unit {{ $document->unit->unit_number }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($document->tenant)
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="ri-user-line text-success me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Tenant</small>
                                                    <strong>{{ $document->tenant->name }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Notes -->
                        @if($document->notes)
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Notes</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-0">{{ $document->notes }}</p>
                                </div>
                            </div>
                        @endif

                        <!-- Actions -->
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ route('documents.download', $document) }}" class="btn btn-primary">
                                <i class="ri-download-line me-1"></i>Download Document
                            </a>
                            
                            @if(in_array($document->file_type, ['pdf', 'jpg', 'jpeg', 'png']))
                                <a href="{{ route('documents.view', $document) }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="ri-external-link-line me-1"></i>View in Browser
                                </a>
                            @endif
                            
                            @can('update', $document)
                                <a href="{{ route('documents.edit', $document) }}" class="btn btn-warning">
                                    <i class="ri-edit-line me-1"></i>Edit Document
                                </a>
                            @endcan
                            
                            @if($document->property)
                                <a href="{{ route('properties.show', $document->property) }}" class="btn btn-outline-info">
                                    <i class="ri-building-line me-1"></i>View Property
                                </a>
                            @endif
                            
                            @if($document->unit)
                                <a href="{{ route('units.show', $document->unit) }}" class="btn btn-outline-secondary">
                                    <i class="ri-home-line me-1"></i>View Unit
                                </a>
                            @endif

                            @can('delete', $document)
                                <button type="button" class="btn btn-outline-danger delete-document" 
                                        data-document-id="{{ $document->id }}"
                                        data-document-title="{{ $document->title }}">
                                    <i class="ri-delete-bin-line me-1"></i>Delete Document
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Metadata -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Document Information</h5>
                    </div>
                    <div class="card-body">
                        <!-- Type -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Document Type</small>
                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $document->type)) }}</span>
                        </div>

                        <!-- Issue Date -->
                        @if($document->issue_date)
                            <div class="mb-3">
                                <small class="text-muted d-block">Issue Date</small>
                                <strong>{{ $document->issue_date->format('M d, Y') }}</strong>
                            </div>
                        @endif

                        <!-- Expiry Date -->
                        @if($document->expiry_date)
                            <div class="mb-3">
                                <small class="text-muted d-block">Expiry Date</small>
                                <strong>{{ $document->expiry_date->format('M d, Y') }}</strong>
                                @if($document->is_expired)
                                    <br><small class="text-danger">Expired {{ $document->expiry_date->diffForHumans() }}</small>
                                @elseif($document->is_expiring_soon)
                                    <br><small class="text-warning">Expires in {{ $document->days_until_expiry }} days</small>
                                @else
                                    <br><small class="text-success">{{ $document->days_until_expiry }} days remaining</small>
                                @endif
                            </div>
                        @endif

                        <!-- Alert Settings -->
                        @if($document->expiry_date)
                            <div class="mb-3">
                                <small class="text-muted d-block">Alert Settings</small>
                                <p class="mb-1">
                                    <i class="ri-alarm-line text-warning me-1"></i>
                                    Alert {{ $document->alert_days_before }} days before expiry
                                </p>
                                @if($document->expiry_alert_sent)
                                    <small class="text-success">
                                        <i class="ri-check-line me-1"></i>Alert sent
                                    </small>
                                @else
                                    <small class="text-muted">
                                        <i class="ri-time-line me-1"></i>Alert pending
                                    </small>
                                @endif
                            </div>
                        @endif

                        <!-- Uploaded By -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Uploaded By</small>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2">
                                    @if($document->uploadedBy->profile_photo)
                                        <img src="{{ asset('storage/' . $document->uploadedBy->profile_photo) }}" 
                                             alt="{{ $document->uploadedBy->name }}" class="rounded-circle">
                                    @else
                                        <div class="avatar-initial rounded-circle bg-primary">
                                            {{ substr($document->uploadedBy->name, 0, 1) }}
                                        </div>
                                    @endif
                                </div>
                                <strong>{{ $document->uploadedBy->name }}</strong>
                            </div>
                        </div>

                        <!-- Upload Date -->
                        <div class="mb-3">
                            <small class="text-muted d-block">Uploaded</small>
                            <strong>{{ $document->created_at->format('M d, Y H:i') }}</strong>
                            <br><small class="text-muted">{{ $document->created_at->diffForHumans() }}</small>
                        </div>

                        <!-- Last Modified -->
                        @if($document->updated_at != $document->created_at)
                            <div class="mb-3">
                                <small class="text-muted d-block">Last Modified</small>
                                <strong>{{ $document->updated_at->format('M d, Y H:i') }}</strong>
                                <br><small class="text-muted">{{ $document->updated_at->diffForHumans() }}</small>
                            </div>
                        @endif

                        <!-- File Details -->
                        <div class="mb-3">
                            <small class="text-muted d-block">File Details</small>
                            <p class="mb-1">Size: {{ $document->file_size_formatted }}</p>
                            <p class="mb-0">Format: {{ strtoupper($document->file_type) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete document functionality
    document.querySelectorAll('.delete-document').forEach(button => {
        button.addEventListener('click', function() {
            const documentId = this.dataset.documentId;
            const documentTitle = this.dataset.documentTitle;

            Swal.fire({
                title: 'Delete Document',
                html: `Are you sure you want to delete:<br><br><strong>${documentTitle}</strong>?<br><br><small class="text-danger">This action cannot be undone and will delete the file permanently.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/documents/${documentId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
