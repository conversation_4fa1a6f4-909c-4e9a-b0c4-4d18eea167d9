@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Edit Role: {{ ucfirst(str_replace('_', ' ', $role->name)) }}</h5>
                <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Back to Roles
                </a>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.roles.update', $role) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-4">
                                <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $role->name) }}" required
                                       placeholder="e.g., property_manager, security_guard"
                                       {{ in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']) ? 'readonly' : '' }}>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @if(in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']))
                                    <div class="form-text text-warning">
                                        <i class="ri-information-line"></i> This is a core system role. Name cannot be changed.
                                    </div>
                                @else
                                    <div class="form-text">Use lowercase with underscores for role names (e.g., property_manager)</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">Assign Permissions</h6>
                            <div class="row">
                                @php
                                    $permissionGroups = [
                                        'Property Management' => ['view_properties', 'create_properties', 'edit_properties', 'delete_properties', 'manage_own_properties'],
                                        'Unit Management' => ['view_units', 'create_units', 'edit_units', 'delete_units', 'view_own_unit', 'manage_unit_occupancy'],
                                        'User Management' => ['view_users', 'create_users', 'edit_users', 'delete_users', 'manage_tenants'],
                                        'Complaint Management' => ['view_complaints', 'create_complaints', 'edit_complaints', 'delete_complaints', 'resolve_complaints', 'view_own_complaints', 'assign_complaints'],
                                        'Financial Management' => ['view_financial_reports', 'manage_rent', 'view_bills', 'view_own_bills', 'generate_reports'],
                                        'Visitor Management' => ['view_visitors', 'create_visitor_entries', 'edit_visitor_entries', 'view_visitor_history', 'manage_checklist'],
                                        'System Administration' => ['manage_roles', 'manage_permissions', 'view_system_logs', 'backup_system', 'manage_settings']
                                    ];
                                    $rolePermissions = $role->permissions->pluck('name')->toArray();
                                @endphp

                                @foreach($permissionGroups as $groupName => $groupPermissions)
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header py-2 d-flex justify-content-between align-items-center">
                                                <h6 class="card-title mb-0">{{ $groupName }}</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input group-checkbox" type="checkbox" 
                                                           data-group="{{ str_replace(' ', '_', strtolower($groupName)) }}"
                                                           id="group_{{ str_replace(' ', '_', strtolower($groupName)) }}">
                                                    <label class="form-check-label small" for="group_{{ str_replace(' ', '_', strtolower($groupName)) }}">
                                                        Select All
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="card-body py-2">
                                                @foreach($permissions as $permission)
                                                    @if(in_array($permission->name, $groupPermissions))
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input permission-checkbox" type="checkbox" 
                                                                   name="permissions[]" value="{{ $permission->name }}" 
                                                                   id="permission_{{ $permission->id }}"
                                                                   data-group="{{ str_replace(' ', '_', strtolower($groupName)) }}"
                                                                   {{ in_array($permission->name, array_merge(old('permissions', []), $rolePermissions)) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                                            </label>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-outline-primary" onclick="selectAllPermissions()">
                                        <i class="ri-checkbox-multiple-line me-1"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="deselectAllPermissions()">
                                        <i class="ri-checkbox-blank-line me-1"></i>Deselect All
                                    </button>
                                </div>
                                <div>
                                    <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary me-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Update Role
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function selectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
    updateGroupCheckboxes();
}

function deselectAllPermissions() {
    document.querySelectorAll('input[name="permissions[]"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
    updateGroupCheckboxes();
}

// Handle group checkbox functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize group checkboxes based on current state
    updateGroupCheckboxes();
    
    // Handle group checkbox clicks
    document.querySelectorAll('.group-checkbox').forEach(function(groupCheckbox) {
        groupCheckbox.addEventListener('change', function() {
            const group = this.dataset.group;
            const isChecked = this.checked;
            
            document.querySelectorAll(`input[data-group="${group}"][name="permissions[]"]`).forEach(function(checkbox) {
                checkbox.checked = isChecked;
            });
        });
    });
    
    // Handle individual permission checkbox clicks
    document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updateGroupCheckboxes();
        });
    });
});

function updateGroupCheckboxes() {
    document.querySelectorAll('.group-checkbox').forEach(function(groupCheckbox) {
        const group = groupCheckbox.dataset.group;
        const groupPermissions = document.querySelectorAll(`input[data-group="${group}"][name="permissions[]"]`);
        const checkedPermissions = document.querySelectorAll(`input[data-group="${group}"][name="permissions[]"]:checked`);
        
        if (checkedPermissions.length === 0) {
            groupCheckbox.checked = false;
            groupCheckbox.indeterminate = false;
        } else if (checkedPermissions.length === groupPermissions.length) {
            groupCheckbox.checked = true;
            groupCheckbox.indeterminate = false;
        } else {
            groupCheckbox.checked = false;
            groupCheckbox.indeterminate = true;
        }
    });
}
</script>
@endsection
