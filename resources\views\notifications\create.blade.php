@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-notification-line text-primary me-2"></i>Send Notification
                        </h4>
                        <p class="text-muted mb-0">
                            Send notifications to users in your system
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Notifications
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('notifications.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title') }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Message -->
                            <div class="mb-3">
                                <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('message') is-invalid @enderror" 
                                          id="message" name="message" rows="4" required>{{ old('message') }}</textarea>
                                @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Recipients -->
                            <div class="mb-3">
                                <label for="user_ids" class="form-label">Recipients <span class="text-danger">*</span></label>
                                <select class="form-select @error('user_ids') is-invalid @enderror" 
                                        id="user_ids" name="user_ids[]" multiple required>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" 
                                                {{ in_array($user->id, old('user_ids', [])) ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->email }})
                                            @if($user->roles->isNotEmpty())
                                                - {{ $user->roles->pluck('name')->join(', ') }}
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                <div class="form-text">Hold Ctrl/Cmd to select multiple recipients</div>
                                @error('user_ids')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="general" {{ old('type') == 'general' ? 'selected' : '' }}>General</option>
                                    <option value="task_reminder" {{ old('type') == 'task_reminder' ? 'selected' : '' }}>Task Reminder</option>
                                    <option value="lease_expiry" {{ old('type') == 'lease_expiry' ? 'selected' : '' }}>Lease Expiry</option>
                                    <option value="document_expiry" {{ old('type') == 'document_expiry' ? 'selected' : '' }}>Document Expiry</option>
                                    <option value="payment_due" {{ old('type') == 'payment_due' ? 'selected' : '' }}>Payment Due</option>
                                    <option value="maintenance_request" {{ old('type') == 'maintenance_request' ? 'selected' : '' }}>Maintenance Request</option>
                                    <option value="system_alert" {{ old('type') == 'system_alert' ? 'selected' : '' }}>System Alert</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Priority -->
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Schedule For -->
                            <div class="mb-3">
                                <label for="scheduled_for" class="form-label">Schedule For (Optional)</label>
                                <input type="datetime-local" class="form-control @error('scheduled_for') is-invalid @enderror" 
                                       id="scheduled_for" name="scheduled_for" value="{{ old('scheduled_for') }}">
                                <div class="form-text">Leave empty to send immediately</div>
                                @error('scheduled_for')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Send Email -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1" 
                                           {{ old('send_email') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="send_email">
                                        Send Email Notification
                                    </label>
                                </div>
                                <div class="form-text">Also send notification via email</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-send-plane-line me-1"></i>Send Notification
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize select2 for better user selection
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('#user_ids').select2({
            placeholder: 'Select recipients...',
            allowClear: true,
            width: '100%'
        });
    }

    // Set minimum datetime to current time
    const scheduledInput = document.getElementById('scheduled_for');
    if (scheduledInput) {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        scheduledInput.min = now.toISOString().slice(0, 16);
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const userIds = document.getElementById('user_ids');
        if (userIds.selectedOptions.length === 0) {
            e.preventDefault();
            Swal.fire({
                title: 'No Recipients Selected',
                text: 'Please select at least one recipient for the notification.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        const scheduledFor = document.getElementById('scheduled_for').value;
        if (scheduledFor) {
            const scheduledDate = new Date(scheduledFor);
            const now = new Date();
            
            if (scheduledDate <= now) {
                e.preventDefault();
                Swal.fire({
                    title: 'Invalid Schedule Time',
                    text: 'Scheduled time must be in the future.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return false;
            }
        }
    });
});
</script>
@endsection
