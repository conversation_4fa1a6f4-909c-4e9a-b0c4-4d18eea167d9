<?php

namespace App\Policies;

use App\Models\Unit;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class UnitPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_units') || $user->can('view_own_unit');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Unit $unit): bool
    {
        // Admin can view all units
        if ($user->can('view_units')) {
            return true;
        }

        // Property owners can view units in their properties
        if ($user->can('manage_own_properties') && $unit->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can view units in properties they manage
        if ($unit->property->manager_id === $user->id) {
            return true;
        }

        // Tenants can view their own unit
        if ($user->can('view_own_unit') && $unit->tenant_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_units');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Unit $unit): bool
    {
        // Admin can edit all units
        if ($user->can('edit_units')) {
            return true;
        }

        // Property owners can edit units in their properties
        if ($user->can('manage_own_properties') && $unit->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can edit units in properties they manage
        if ($unit->property->manager_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Unit $unit): bool
    {
        // Admin can delete all units
        if ($user->can('delete_units')) {
            return true;
        }

        // Property owners can delete units in their properties
        if ($user->can('manage_own_properties') && $unit->property->owner_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Unit $unit): bool
    {
        return $this->delete($user, $unit);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Unit $unit): bool
    {
        return $user->can('delete_units');
    }

    /**
     * Determine whether the user can manage occupancy for the unit.
     */
    public function manageOccupancy(User $user, Unit $unit): bool
    {
        // Admin can manage all unit occupancy
        if ($user->can('manage_unit_occupancy')) {
            return true;
        }

        // Property owners can manage occupancy in their properties
        if ($user->can('manage_own_properties') && $unit->property->owner_id === $user->id) {
            return true;
        }

        // Property managers can manage occupancy in properties they manage
        if ($unit->property->manager_id === $user->id) {
            return true;
        }

        return false;
    }
}
