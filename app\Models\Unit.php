<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasObfuscatedRoutes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Unit extends Model
{
    use HasFactory, HasObfuscatedRoutes;

    protected $fillable = [
        'property_id',
        'unit_number',
        'unit_type',
        'area',
        'bedrooms',
        'bathrooms',
        'rent_amount',
        'security_deposit',
        'status',
        'tenant_id',
        'lease_start',
        'lease_end',
        'description',
        'features',
        'lease_document',
        'tenant_id_document',
    ];

    protected $casts = [
        'features' => 'array',
        'lease_start' => 'date',
        'lease_end' => 'date',
        'area' => 'decimal:2',
        'rent_amount' => 'decimal:2',
        'security_deposit' => 'decimal:2',
    ];

    // Relationships
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeOccupied($query)
    {
        return $query->where('status', 'occupied');
    }

    public function scopeByProperty($query, $propertyId)
    {
        return $query->where('property_id', $propertyId);
    }

    // Accessors
    public function getIsAvailableAttribute(): bool
    {
        return $this->status === 'available';
    }

    public function getIsOccupiedAttribute(): bool
    {
        return $this->status === 'occupied';
    }
}
