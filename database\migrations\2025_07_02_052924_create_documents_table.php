<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['contract', 'insurance', 'fire_certificate', 'lease_agreement', 'tenant_id', 'property_deed', 'permit', 'inspection_report', 'other'])->default('other');
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_type'); // pdf, doc, jpg, etc.
            $table->bigInteger('file_size'); // in bytes
            $table->foreignId('property_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('unit_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->date('issue_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->boolean('is_expired')->default(false);
            $table->boolean('expiry_alert_sent')->default(false);
            $table->integer('alert_days_before')->default(30); // days before expiry to send alert
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // additional document metadata
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
