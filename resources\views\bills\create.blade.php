@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-bill-line text-primary me-2"></i>Create New Bill
                        </h4>
                        <p class="text-muted mb-0">
                            Create a new bill or invoice for a tenant
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('bills.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Bills
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('bills.store') }}" method="POST" id="createBillForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Tenant Selection -->
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Tenant <span class="text-danger">*</span></label>
                                <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id" required>
                                    <option value="">Select Tenant</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name }} ({{ $tenant->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Property and Unit Selection -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select @error('property_id') is-invalid @enderror" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            @foreach($properties as $property)
                                                <option value="{{ $property->id }}" {{ old('property_id') == $property->id ? 'selected' : '' }}>
                                                    {{ $property->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('property_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select @error('unit_id') is-invalid @enderror" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                        </select>
                                        @error('unit_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Bill Type -->
                            <div class="mb-3">
                                <label for="type" class="form-label">Bill Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="rent" {{ old('type') == 'rent' ? 'selected' : '' }}>Monthly Rent</option>
                                    <option value="utilities" {{ old('type') == 'utilities' ? 'selected' : '' }}>Utilities</option>
                                    <option value="maintenance" {{ old('type') == 'maintenance' ? 'selected' : '' }}>Maintenance Fee</option>
                                    <option value="parking" {{ old('type') == 'parking' ? 'selected' : '' }}>Parking Fee</option>
                                    <option value="late_fee" {{ old('type') == 'late_fee' ? 'selected' : '' }}>Late Fee</option>
                                    <option value="security_deposit" {{ old('type') == 'security_deposit' ? 'selected' : '' }}>Security Deposit</option>
                                    <option value="cleaning" {{ old('type') == 'cleaning' ? 'selected' : '' }}>Cleaning Fee</option>
                                    <option value="other" {{ old('type') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Bill Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Bill Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title') }}" 
                                       placeholder="e.g., Monthly Rent - January 2024" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3" 
                                          placeholder="Additional details about the bill...">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Bill Period -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="period_start" class="form-label">Period Start</label>
                                        <input type="date" class="form-control @error('period_start') is-invalid @enderror" 
                                               id="period_start" name="period_start" value="{{ old('period_start') }}">
                                        @error('period_start')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="period_end" class="form-label">Period End</label>
                                        <input type="date" class="form-control @error('period_end') is-invalid @enderror" 
                                               id="period_end" name="period_end" value="{{ old('period_end') }}">
                                        @error('period_end')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Bill Amount -->
                            <div class="mb-3">
                                <label for="amount" class="form-label">Bill Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                           id="amount" name="amount" value="{{ old('amount') }}" 
                                           placeholder="0.00" min="0.01" step="0.01" required>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Due Date -->
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                       id="due_date" name="due_date" value="{{ old('due_date') }}" required>
                                @error('due_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Issue Date -->
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('issue_date') is-invalid @enderror" 
                                       id="issue_date" name="issue_date" 
                                       value="{{ old('issue_date', now()->format('Y-m-d')) }}" required>
                                @error('issue_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="pending" {{ old('status', 'pending') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="sent" {{ old('status') == 'sent' ? 'selected' : '' }}>Sent</option>
                                    <option value="paid" {{ old('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                    <option value="overdue" {{ old('status') == 'overdue' ? 'selected' : '' }}>Overdue</option>
                                    <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Bill Number -->
                            <div class="mb-3">
                                <label for="bill_number" class="form-label">Bill Number</label>
                                <input type="text" class="form-control @error('bill_number') is-invalid @enderror" 
                                       id="bill_number" name="bill_number" value="{{ old('bill_number') }}" 
                                       placeholder="Auto-generated if empty">
                                @error('bill_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Recurring Bill -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" value="1" 
                                           {{ old('is_recurring') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_recurring">
                                        Recurring Bill
                                    </label>
                                </div>
                            </div>

                            <!-- Recurring Options -->
                            <div id="recurring-options" style="display: none;">
                                <div class="mb-3">
                                    <label for="recurring_frequency" class="form-label">Frequency</label>
                                    <select class="form-select" id="recurring_frequency" name="recurring_frequency">
                                        <option value="monthly" {{ old('recurring_frequency', 'monthly') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        <option value="quarterly" {{ old('recurring_frequency') == 'quarterly' ? 'selected' : '' }}>Quarterly</option>
                                        <option value="yearly" {{ old('recurring_frequency') == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Send Options -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1" 
                                           {{ old('send_email', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="send_email">
                                        Send email to tenant
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_reminder" name="auto_reminder" value="1" 
                                           {{ old('auto_reminder') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="auto_reminder">
                                        Enable auto reminders
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('bills.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create Bill
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');
    const tenantSelect = document.getElementById('tenant_id');
    const typeSelect = document.getElementById('type');
    const titleInput = document.getElementById('title');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Auto-generate title based on type and tenant
    function updateTitle() {
        const tenant = tenantSelect.options[tenantSelect.selectedIndex]?.text?.split(' (')[0];
        const type = typeSelect.options[typeSelect.selectedIndex]?.text;
        const currentMonth = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        
        if (tenant && type && !titleInput.value) {
            if (type === 'Monthly Rent') {
                titleInput.value = `${type} - ${currentMonth} - ${tenant}`;
            } else {
                titleInput.value = `${type} - ${tenant}`;
            }
        }
    }

    tenantSelect.addEventListener('change', updateTitle);
    typeSelect.addEventListener('change', updateTitle);

    // Handle recurring bill options
    const recurringCheckbox = document.getElementById('is_recurring');
    const recurringOptions = document.getElementById('recurring-options');

    recurringCheckbox.addEventListener('change', function() {
        recurringOptions.style.display = this.checked ? 'block' : 'none';
    });

    // Initialize recurring options display
    if (recurringCheckbox.checked) {
        recurringOptions.style.display = 'block';
    }

    // Form validation
    const form = document.getElementById('createBillForm');
    form.addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount').value);
        const tenantId = tenantSelect.value;
        const type = typeSelect.value;
        const title = titleInput.value;
        const dueDate = document.getElementById('due_date').value;

        if (!amount || amount <= 0) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Amount',
                text: 'Please enter a valid bill amount.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (!tenantId || !type || !title || !dueDate) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Validate due date is not in the past
        const today = new Date().toISOString().split('T')[0];
        if (dueDate < today) {
            e.preventDefault();
            Swal.fire({
                title: 'Invalid Due Date',
                text: 'Due date cannot be in the past.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
@endsection
