<?php $__env->startSection('content'); ?>
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-flashlight-line text-primary me-2"></i>Utility Bills
                        </h4>
                        <p class="text-muted mb-0">
                            Manage electricity, water, gas and other utility bills
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('utility-bills.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Utility Bill
                        </a>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkCreateModal">
                            <i class="ri-stack-line me-1"></i>Bulk Create
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('utility-bills.index')); ?>">All Bills</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('utility-bills.index', ['type' => 'electricity'])); ?>">Electricity</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('utility-bills.index', ['type' => 'water'])); ?>">Water</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('utility-bills.index', ['type' => 'gas'])); ?>">Gas</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('utility-bills.index', ['type' => 'internet'])); ?>">Internet</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">$<?php echo e(number_format(0, 2)); ?></h3>
                                <p class="mb-0">Total Amount</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-money-dollar-circle-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1"><?php echo e($utilityBills->count()); ?></h3>
                                <p class="mb-0">Pending Bills</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-time-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">Distributed</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-share-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">4</h3>
                                <p class="mb-0">Utility Types</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-settings-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Utility Bills List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Utility Bills</h5>
            </div>
            <div class="card-body">
                <?php if($utilityBills->isEmpty()): ?>
                    <div class="text-center py-5">
                        <i class="ri-flashlight-line fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">No Utility Bills Found</h5>
                        <p class="text-muted mb-4">Start by adding utility bills for your properties.</p>
                        <a href="<?php echo e(route('utility-bills.create')); ?>" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add First Utility Bill
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Bill Number</th>
                                    <th>Utility Type</th>
                                    <th>Property</th>
                                    <th>Amount</th>
                                    <th>Period</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Placeholder for utility bill data -->
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        Utility bill data will be displayed here when implemented
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div class="modal fade" id="bulkCreateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Create Utility Bills</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Create multiple utility bills at once for different properties.</p>
                <!-- Bulk create form will go here -->
                <div class="text-center py-4">
                    <p class="text-muted">Bulk create functionality coming soon...</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Utility bills management scripts will go here
    console.log('Utility Bills page loaded');
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.base', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\PM_SYSTEM\property_ms\resources\views/billing/utility-bills/index.blade.php ENDPATH**/ ?>