@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-notification-line text-primary me-2"></i>Notifications
                        </h4>
                        <p class="text-muted mb-0">
                            View and manage your notifications and alerts
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        @can('create', App\Models\Notification::class)
                            <a href="{{ route('notifications.create') }}" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Send Notification
                            </a>
                        @endcan
                        <button type="button" class="btn btn-outline-secondary" id="markAllRead">
                            <i class="ri-check-double-line me-1"></i>Mark All Read
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-notification-line fs-1 text-primary mb-2"></i>
                        <h4 class="mb-1">{{ $stats['total'] }}</h4>
                        <p class="text-muted mb-0">Total Notifications</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-notification-badge-line fs-1 text-warning mb-2"></i>
                        <h4 class="mb-1">{{ $stats['unread'] }}</h4>
                        <p class="text-muted mb-0">Unread</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-check-double-line fs-1 text-success mb-2"></i>
                        <h4 class="mb-1">{{ $stats['read'] }}</h4>
                        <p class="text-muted mb-0">Read</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="ri-time-line fs-1 text-info mb-2"></i>
                        <h4 class="mb-1">{{ $stats['recent'] }}</h4>
                        <p class="text-muted mb-0">Recent (7 days)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('notifications.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="task_reminder" {{ request('type') == 'task_reminder' ? 'selected' : '' }}>Task Reminder</option>
                            <option value="lease_expiry" {{ request('type') == 'lease_expiry' ? 'selected' : '' }}>Lease Expiry</option>
                            <option value="document_expiry" {{ request('type') == 'document_expiry' ? 'selected' : '' }}>Document Expiry</option>
                            <option value="payment_due" {{ request('type') == 'payment_due' ? 'selected' : '' }}>Payment Due</option>
                            <option value="maintenance_request" {{ request('type') == 'maintenance_request' ? 'selected' : '' }}>Maintenance Request</option>
                            <option value="system_alert" {{ request('type') == 'system_alert' ? 'selected' : '' }}>System Alert</option>
                            <option value="general" {{ request('type') == 'general' ? 'selected' : '' }}>General</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <option value="low" {{ request('priority') == 'low' ? 'selected' : '' }}>Low</option>
                            <option value="medium" {{ request('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                            <option value="high" {{ request('priority') == 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ request('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="unread" {{ request('status') == 'unread' ? 'selected' : '' }}>Unread</option>
                            <option value="read" {{ request('status') == 'read' ? 'selected' : '' }}>Read</option>
                        </select>
                    </div>
                    <div class="col-md-5 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications List -->
        @if($notifications->count() > 0)
            <div class="card">
                <div class="card-body p-0">
                    @foreach($notifications as $notification)
                        <div class="notification-item border-bottom p-3 {{ !$notification->is_read ? 'bg-light' : '' }}" 
                             data-notification-id="{{ $notification->id }}">
                            <div class="d-flex align-items-start">
                                <!-- Notification Icon -->
                                <div class="me-3">
                                    @switch($notification->type)
                                        @case('task_reminder')
                                            <div class="avatar avatar-sm bg-primary">
                                                <i class="ri-task-line text-white"></i>
                                            </div>
                                            @break
                                        @case('lease_expiry')
                                            <div class="avatar avatar-sm bg-warning">
                                                <i class="ri-calendar-line text-white"></i>
                                            </div>
                                            @break
                                        @case('document_expiry')
                                            <div class="avatar avatar-sm bg-danger">
                                                <i class="ri-file-line text-white"></i>
                                            </div>
                                            @break
                                        @case('payment_due')
                                            <div class="avatar avatar-sm bg-success">
                                                <i class="ri-money-dollar-circle-line text-white"></i>
                                            </div>
                                            @break
                                        @case('maintenance_request')
                                            <div class="avatar avatar-sm bg-info">
                                                <i class="ri-tools-line text-white"></i>
                                            </div>
                                            @break
                                        @case('system_alert')
                                            <div class="avatar avatar-sm bg-secondary">
                                                <i class="ri-alert-line text-white"></i>
                                            </div>
                                            @break
                                        @default
                                            <div class="avatar avatar-sm bg-primary">
                                                <i class="ri-notification-line text-white"></i>
                                            </div>
                                    @endswitch
                                </div>

                                <!-- Notification Content -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <h6 class="mb-0 {{ !$notification->is_read ? 'fw-bold' : '' }}">
                                            {{ $notification->title }}
                                        </h6>
                                        <div class="d-flex align-items-center gap-2">
                                            <!-- Priority Badge -->
                                            <span class="badge bg-{{ $notification->priority == 'urgent' ? 'danger' : ($notification->priority == 'high' ? 'warning' : ($notification->priority == 'medium' ? 'info' : 'secondary')) }}">
                                                {{ ucfirst($notification->priority) }}
                                            </span>
                                            <!-- Unread Indicator -->
                                            @if(!$notification->is_read)
                                                <span class="badge bg-primary">New</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <p class="text-muted mb-2">{{ $notification->message }}</p>
                                    
                                    <!-- Related Information -->
                                    @if($notification->relatedProperty || $notification->relatedUnit || $notification->relatedTask)
                                        <div class="mb-2">
                                            @if($notification->relatedProperty)
                                                <small class="text-info me-2">
                                                    <i class="ri-building-line me-1"></i>{{ $notification->relatedProperty->name }}
                                                </small>
                                            @endif
                                            @if($notification->relatedUnit)
                                                <small class="text-info me-2">
                                                    <i class="ri-home-line me-1"></i>Unit {{ $notification->relatedUnit->unit_number }}
                                                </small>
                                            @endif
                                            @if($notification->relatedTask)
                                                <small class="text-info me-2">
                                                    <i class="ri-task-line me-1"></i>{{ $notification->relatedTask->title }}
                                                </small>
                                            @endif
                                        </div>
                                    @endif
                                    
                                    <!-- Timestamp and Sender -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="ri-time-line me-1"></i>{{ $notification->time_ago }}
                                            @if($notification->createdBy)
                                                • from {{ $notification->createdBy->name }}
                                            @endif
                                        </small>
                                        
                                        <!-- Actions -->
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('notifications.show', $notification) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            @if(!$notification->is_read)
                                                <button type="button" class="btn btn-sm btn-outline-success mark-read" 
                                                        data-notification-id="{{ $notification->id }}">
                                                    <i class="ri-check-line"></i>
                                                </button>
                                            @endif
                                            @can('delete', $notification)
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-notification" 
                                                        data-notification-id="{{ $notification->id }}">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            @endcan
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <!-- Pagination -->
                    <div class="p-3">
                        {{ $notifications->links() }}
                    </div>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-notification-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Notifications Found</h5>
                    <p class="text-muted mb-4">
                        @if(request()->hasAny(['type', 'priority', 'status']))
                            No notifications match your current filters. Try adjusting your search criteria.
                        @else
                            You don't have any notifications yet. They will appear here when you receive them.
                        @endif
                    </p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark single notification as read
    document.querySelectorAll('.mark-read').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/notifications/${notificationId}/read`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    });

    // Mark all notifications as read
    document.getElementById('markAllRead').addEventListener('click', function() {
        fetch('/notifications/mark-all-read', {
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    });

    // Delete notification
    document.querySelectorAll('.delete-notification').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;

            Swal.fire({
                title: 'Delete Notification',
                text: 'Are you sure you want to delete this notification?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/notifications/${notificationId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
