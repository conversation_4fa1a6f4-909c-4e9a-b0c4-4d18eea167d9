@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-user-line text-primary me-2"></i>Visitor Management
                        </h4>
                        <p class="text-muted mb-0">
                            Track and manage all property visitors
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('visitors.create') }}" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In Visitor
                        </a>
                        <a href="{{ route('visitors.dashboard') }}" class="btn btn-outline-info">
                            <i class="ri-dashboard-line me-1"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['today_visitors'] }}</h3>
                                <p class="mb-0">Today's Visitors</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['checked_in'] }}</h3>
                                <p class="mb-0">Currently Checked In</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-user-check-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['this_week'] }}</h3>
                                <p class="mb-0">This Week</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-week-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['this_month'] }}</h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-month-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('visitors.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="{{ request('date', today()->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Visitors</option>
                            <option value="checked_in" {{ request('status') == 'checked_in' ? 'selected' : '' }}>Checked In</option>
                            <option value="checked_out" {{ request('status') == 'checked_out' ? 'selected' : '' }}>Checked Out</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="property_id" class="form-label">Property</label>
                        <select class="form-select" id="property_id" name="property_id">
                            <option value="">All Properties</option>
                            @foreach($properties as $property)
                                <option value="{{ $property->id }}" {{ request('property_id') == $property->id ? 'selected' : '' }}>
                                    {{ $property->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Name, phone, ID..." value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Visitors Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Visitors List</h5>
            </div>
            <div class="card-body">
                @if($visitors->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Visitor</th>
                                    <th>Contact</th>
                                    <th>Visiting</th>
                                    <th>Purpose</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($visitors as $visitor)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $visitor->name }}</strong>
                                                <br><small class="text-muted">
                                                    {{ ucfirst(str_replace('_', ' ', $visitor->identification_type)) }}: 
                                                    {{ $visitor->identification_number }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="ri-phone-line me-1"></i>{{ $visitor->phone }}
                                                @if($visitor->email)
                                                    <br><i class="ri-mail-line me-1"></i>{{ $visitor->email }}
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $visitor->property->name ?? 'N/A' }}</strong>
                                                @if($visitor->visitingUnit)
                                                    <br><small class="text-muted">Unit {{ $visitor->visitingUnit->unit_number }}</small>
                                                @endif
                                                @if($visitor->visitingTenant)
                                                    <br><small class="text-info">{{ $visitor->visitingTenant->name }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $visitor->purpose_of_visit }}</span>
                                            @if($visitor->vehicle_number)
                                                <br><small class="text-muted">
                                                    <i class="ri-car-line me-1"></i>{{ $visitor->vehicle_number }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($visitor->check_in_time)
                                                {{ $visitor->check_in_time->format('H:i') }}
                                                <br><small class="text-muted">{{ $visitor->check_in_time->format('M d') }}</small>
                                                @if($visitor->checkedInBy)
                                                    <br><small class="text-info">by {{ $visitor->checkedInBy->name }}</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Not checked in</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($visitor->check_out_time)
                                                {{ $visitor->check_out_time->format('H:i') }}
                                                <br><small class="text-muted">{{ $visitor->check_out_time->format('M d') }}</small>
                                                @if($visitor->checkedOutBy)
                                                    <br><small class="text-info">by {{ $visitor->checkedOutBy->name }}</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Still inside</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($visitor->visit_duration)
                                                <span class="badge bg-info">{{ $visitor->visit_duration }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($visitor->is_checked_in)
                                                <span class="badge bg-success">Checked In</span>
                                            @else
                                                <span class="badge bg-secondary">Checked Out</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('visitors.show', $visitor) }}" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                @if($visitor->is_checked_in)
                                                    <button type="button" class="btn btn-outline-danger check-out-visitor" 
                                                            data-visitor-id="{{ $visitor->id }}" 
                                                            data-visitor-name="{{ $visitor->name }}" title="Check Out">
                                                        <i class="ri-logout-circle-line"></i>
                                                    </button>
                                                @endif
                                                <a href="{{ route('visitors.edit', $visitor) }}" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $visitors->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="ri-user-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Visitors Found</h5>
                        <p class="text-muted mb-4">
                            @if(request('search') || request('status') || request('property_id'))
                                No visitors match your search criteria.
                            @else
                                No visitors have been registered for the selected date.
                            @endif
                        </p>
                        <a href="{{ route('visitors.create') }}" class="btn btn-primary">
                            <i class="ri-user-add-line me-1"></i>Check In First Visitor
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check out visitor
    document.querySelectorAll('.check-out-visitor').forEach(button => {
        button.addEventListener('click', function() {
            const visitorId = this.dataset.visitorId;
            const visitorName = this.dataset.visitorName;

            Swal.fire({
                title: 'Check Out Visitor',
                html: `Are you sure you want to check out:<br><br><strong>${visitorName}</strong>?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-logout-circle-line me-1"></i>Check Out',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/visitors/${visitorId}/check-out`;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Auto-refresh page every 30 seconds for real-time updates
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            window.location.reload();
        }
    }, 30000);
});
</script>
@endsection
