@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-feedback-line text-primary me-2"></i>Submit New Complaint
                        </h4>
                        <p class="text-muted mb-0">
                            Report an issue or submit a complaint for resolution
                        </p>
                    </div>
                    <div>
                        <a href="{{ route('complaints.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Complaints
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('complaints.store') }}" method="POST" enctype="multipart/form-data" id="createComplaintForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Tenant Selection (for admin/property owners) -->
                            @if(auth()->user()->roles->pluck('name')->intersect(['admin', 'property_owner'])->isNotEmpty())
                                <div class="mb-3">
                                    <label for="tenant_id" class="form-label">Tenant <span class="text-danger">*</span></label>
                                    <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                            id="tenant_id" name="tenant_id" required>
                                        <option value="">Select Tenant</option>
                                        @foreach($tenants as $tenant)
                                            <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                                {{ $tenant->name }} ({{ $tenant->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('tenant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            @endif

                            <!-- Property and Unit Selection -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">Property</label>
                                        <select class="form-select @error('property_id') is-invalid @enderror" 
                                                id="property_id" name="property_id">
                                            <option value="">Select Property (Optional)</option>
                                            @foreach($properties as $property)
                                                <option value="{{ $property->id }}" {{ old('property_id') == $property->id ? 'selected' : '' }}>
                                                    {{ $property->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('property_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_id" class="form-label">Unit</label>
                                        <select class="form-select @error('unit_id') is-invalid @enderror" 
                                                id="unit_id" name="unit_id">
                                            <option value="">Select Unit (Optional)</option>
                                        </select>
                                        @error('unit_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Subject -->
                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                       id="subject" name="subject" value="{{ old('subject') }}" 
                                       placeholder="Brief description of the issue" required>
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('category') is-invalid @enderror" 
                                        id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="maintenance" {{ old('category') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                    <option value="plumbing" {{ old('category') == 'plumbing' ? 'selected' : '' }}>Plumbing</option>
                                    <option value="electrical" {{ old('category') == 'electrical' ? 'selected' : '' }}>Electrical</option>
                                    <option value="heating_cooling" {{ old('category') == 'heating_cooling' ? 'selected' : '' }}>Heating/Cooling</option>
                                    <option value="appliances" {{ old('category') == 'appliances' ? 'selected' : '' }}>Appliances</option>
                                    <option value="pest_control" {{ old('category') == 'pest_control' ? 'selected' : '' }}>Pest Control</option>
                                    <option value="noise" {{ old('category') == 'noise' ? 'selected' : '' }}>Noise Complaint</option>
                                    <option value="security" {{ old('category') == 'security' ? 'selected' : '' }}>Security</option>
                                    <option value="parking" {{ old('category') == 'parking' ? 'selected' : '' }}>Parking</option>
                                    <option value="cleaning" {{ old('category') == 'cleaning' ? 'selected' : '' }}>Cleaning</option>
                                    <option value="neighbor" {{ old('category') == 'neighbor' ? 'selected' : '' }}>Neighbor Issue</option>
                                    <option value="billing" {{ old('category') == 'billing' ? 'selected' : '' }}>Billing</option>
                                    <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="5" 
                                          placeholder="Please provide detailed information about the issue..." required>{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Location Details -->
                            <div class="mb-3">
                                <label for="location" class="form-label">Specific Location</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                       id="location" name="location" value="{{ old('location') }}" 
                                       placeholder="e.g., Kitchen sink, Living room window, Bedroom 2">
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Attachments -->
                            <div class="mb-3">
                                <label for="attachments" class="form-label">Attachments</label>
                                <input type="file" class="form-control @error('attachments.*') is-invalid @enderror" 
                                       id="attachments" name="attachments[]" multiple 
                                       accept="image/*,application/pdf,.doc,.docx">
                                <div class="form-text">
                                    You can upload images, PDFs, or documents. Maximum 5 files, 10MB each.
                                </div>
                                @error('attachments.*')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Priority -->
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" name="priority" required>
                                    <option value="low" {{ old('priority', 'medium') == 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="medium" {{ old('priority', 'medium') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <small>
                                        <strong>Low:</strong> Non-urgent issues<br>
                                        <strong>Medium:</strong> Standard maintenance<br>
                                        <strong>High:</strong> Affects daily living<br>
                                        <strong>Urgent:</strong> Safety/security issues
                                    </small>
                                </div>
                            </div>

                            <!-- Preferred Contact Method -->
                            <div class="mb-3">
                                <label for="preferred_contact" class="form-label">Preferred Contact Method</label>
                                <select class="form-select @error('preferred_contact') is-invalid @enderror" 
                                        id="preferred_contact" name="preferred_contact">
                                    <option value="email" {{ old('preferred_contact', 'email') == 'email' ? 'selected' : '' }}>Email</option>
                                    <option value="phone" {{ old('preferred_contact') == 'phone' ? 'selected' : '' }}>Phone</option>
                                    <option value="sms" {{ old('preferred_contact') == 'sms' ? 'selected' : '' }}>SMS</option>
                                    <option value="in_person" {{ old('preferred_contact') == 'in_person' ? 'selected' : '' }}>In Person</option>
                                </select>
                                @error('preferred_contact')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Access Instructions -->
                            <div class="mb-3">
                                <label for="access_instructions" class="form-label">Access Instructions</label>
                                <textarea class="form-control @error('access_instructions') is-invalid @enderror" 
                                          id="access_instructions" name="access_instructions" rows="3" 
                                          placeholder="Special instructions for accessing the unit...">{{ old('access_instructions') }}</textarea>
                                @error('access_instructions')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Availability -->
                            <div class="mb-3">
                                <label for="availability" class="form-label">Availability</label>
                                <textarea class="form-control @error('availability') is-invalid @enderror" 
                                          id="availability" name="availability" rows="3" 
                                          placeholder="When are you available for maintenance visits?">{{ old('availability') }}</textarea>
                                @error('availability')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Emergency Contact -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_emergency" name="is_emergency" value="1" 
                                           {{ old('is_emergency') ? 'checked' : '' }}>
                                    <label class="form-check-label text-danger" for="is_emergency">
                                        <strong>This is an emergency</strong>
                                    </label>
                                </div>
                                <div class="form-text">
                                    <small class="text-danger">
                                        Check this box only for urgent issues that require immediate attention 
                                        (safety hazards, water leaks, electrical problems, etc.)
                                    </small>
                                </div>
                            </div>

                            <!-- Anonymous Complaint -->
                            @if(auth()->user()->roles->pluck('name')->intersect(['admin', 'property_owner'])->isNotEmpty())
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous" value="1" 
                                               {{ old('is_anonymous') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_anonymous">
                                            Anonymous complaint
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <small>Hide tenant identity from maintenance staff</small>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="{{ route('complaints.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-close-line me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-send-plane-line me-1"></i>Submit Complaint
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle property selection to load units
    const propertySelect = document.getElementById('property_id');
    const unitSelect = document.getElementById('unit_id');

    propertySelect.addEventListener('change', function() {
        const propertyId = this.value;
        loadUnits(propertyId);
    });

    function loadUnits(propertyId) {
        unitSelect.innerHTML = '<option value="">Select Unit (Optional)</option>';
        
        if (propertyId) {
            const properties = @json($properties);
            const selectedProperty = properties.find(p => p.id == propertyId);
            
            if (selectedProperty && selectedProperty.units) {
                selectedProperty.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `Unit ${unit.unit_number}`;
                    unitSelect.appendChild(option);
                });
            }
        }
    }

    // Handle emergency checkbox
    const emergencyCheckbox = document.getElementById('is_emergency');
    const prioritySelect = document.getElementById('priority');

    emergencyCheckbox.addEventListener('change', function() {
        if (this.checked) {
            prioritySelect.value = 'urgent';
            Swal.fire({
                title: 'Emergency Complaint',
                text: 'This complaint will be marked as urgent priority. Emergency contacts will be notified immediately.',
                icon: 'warning',
                confirmButtonText: 'Understood'
            });
        }
    });

    // File upload validation
    const fileInput = document.getElementById('attachments');
    fileInput.addEventListener('change', function() {
        const files = this.files;
        const maxFiles = 5;
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (files.length > maxFiles) {
            Swal.fire({
                title: 'Too Many Files',
                text: `You can only upload up to ${maxFiles} files.`,
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            this.value = '';
            return;
        }

        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSize) {
                Swal.fire({
                    title: 'File Too Large',
                    text: `File "${files[i].name}" is too large. Maximum size is 10MB.`,
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                this.value = '';
                return;
            }
        }
    });

    // Form validation
    const form = document.getElementById('createComplaintForm');
    form.addEventListener('submit', function(e) {
        const subject = document.getElementById('subject').value;
        const category = document.getElementById('category').value;
        const description = document.getElementById('description').value;
        const priority = document.getElementById('priority').value;

        if (!subject || !category || !description || !priority) {
            e.preventDefault();
            Swal.fire({
                title: 'Missing Information',
                text: 'Please fill in all required fields.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (description.length < 10) {
            e.preventDefault();
            Swal.fire({
                title: 'Description Too Short',
                text: 'Please provide a more detailed description of the issue.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }
    });
});
</script>
@endsection
