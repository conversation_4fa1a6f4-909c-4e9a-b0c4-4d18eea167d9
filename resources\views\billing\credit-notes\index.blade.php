@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-add-line text-primary me-2"></i>Credit Notes
                        </h4>
                        <p class="text-muted mb-0">
                            Manage credit notes for refunds, discounts, and adjustments
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('credit-notes.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create Credit Note
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('credit-notes.index') }}">All Notes</a></li>
                                <li><a class="dropdown-item" href="{{ route('credit-notes.index', ['status' => 'draft']) }}">Draft</a></li>
                                <li><a class="dropdown-item" href="{{ route('credit-notes.index', ['status' => 'issued']) }}">Issued</a></li>
                                <li><a class="dropdown-item" href="{{ route('credit-notes.index', ['status' => 'applied']) }}">Applied</a></li>
                                <li><a class="dropdown-item" href="{{ route('credit-notes.index', ['status' => 'cancelled']) }}">Cancelled</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">${{ number_format(0, 2) }}</h3>
                                <p class="mb-0">Total Credit Amount</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-add-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $creditNotes->count() }}</h3>
                                <p class="mb-0">Pending Notes</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-time-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">Applied Notes</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-check-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">0</h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="icon-shape">
                                <i class="ri-calendar-line fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credit Notes List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Credit Notes</h5>
            </div>
            <div class="card-body">
                @if($creditNotes->isEmpty())
                    <div class="text-center py-5">
                        <i class="ri-add-line fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">No Credit Notes Found</h5>
                        <p class="text-muted mb-4">Create credit notes for refunds, discounts, or positive adjustments.</p>
                        <a href="{{ route('credit-notes.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First Credit Note
                        </a>
                    </div>
                @else
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Note Number</th>
                                    <th>Tenant</th>
                                    <th>Property</th>
                                    <th>Amount</th>
                                    <th>Reason</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Placeholder for credit note data -->
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        Credit note data will be displayed here when implemented
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Credit notes management scripts will go here
    console.log('Credit Notes page loaded');
    
    // Apply credit note function
    function applyCreditNote(noteId) {
        if (confirm('Are you sure you want to apply this credit note to the tenant account?')) {
            // Implementation will go here
            console.log('Applying credit note:', noteId);
        }
    }
</script>
@endpush
