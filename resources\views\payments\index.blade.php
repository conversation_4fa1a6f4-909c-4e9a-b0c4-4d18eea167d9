@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-money-dollar-circle-line text-primary me-2"></i>Payments Management
                        </h4>
                        <p class="text-muted mb-0">
                            Track and manage all payments received from tenants
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('payments.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Record Payment
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('payments.index') }}">All Payments</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['status' => 'completed']) }}">Completed</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['status' => 'pending']) }}">Pending</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['status' => 'failed']) }}">Failed</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['method' => 'cash']) }}">Cash</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['method' => 'bank_transfer']) }}">Bank Transfer</a></li>
                                <li><a class="dropdown-item" href="{{ route('payments.index', ['method' => 'check']) }}">Check</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">${{ number_format($stats['total_amount'] ?? 0, 2) }}</h3>
                                <p class="mb-0">Total Received</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-money-dollar-circle-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['total_count'] ?? 0 }}</h3>
                                <p class="mb-0">Total Payments</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-file-list-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">${{ number_format($stats['this_month'] ?? 0, 2) }}</h3>
                                <p class="mb-0">This Month</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-calendar-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-1">{{ $stats['pending_count'] ?? 0 }}</h3>
                                <p class="mb-0">Pending</p>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="ri-time-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Payments List</h5>
                    <div class="d-flex gap-2">
                        <form method="GET" action="{{ route('payments.index') }}" class="d-flex gap-2">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search payments..." value="{{ request('search') }}">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if(isset($payments) && $payments->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Payment #</th>
                                    <th>Tenant</th>
                                    <th>Property/Unit</th>
                                    <th>Bill</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($payments as $payment)
                                    <tr>
                                        <td>
                                            <strong>#{{ $payment->payment_number ?? 'P-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</strong>
                                        </td>
                                        <td>
                                            @if($payment->tenant)
                                                <div>
                                                    <h6 class="mb-0">{{ $payment->tenant->name }}</h6>
                                                    <small class="text-muted">{{ $payment->tenant->email }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">No tenant</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($payment->unit)
                                                <div>
                                                    <strong>{{ $payment->unit->property->name ?? 'Unknown Property' }}</strong>
                                                    <br><small class="text-muted">Unit {{ $payment->unit->unit_number }}</small>
                                                </div>
                                            @elseif($payment->property)
                                                <strong>{{ $payment->property->name }}</strong>
                                            @else
                                                <span class="text-muted">No property</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($payment->bill)
                                                <a href="{{ route('bills.show', $payment->bill) }}" class="text-primary">
                                                    #{{ $payment->bill->bill_number ?? 'B-' . str_pad($payment->bill->id, 6, '0', STR_PAD_LEFT) }}
                                                </a>
                                            @else
                                                <span class="text-muted">No bill</span>
                                            @endif
                                        </td>
                                        <td>
                                            <strong>${{ number_format($payment->amount ?? 0, 2) }}</strong>
                                        </td>
                                        <td>
                                            @php
                                                $methodColors = [
                                                    'cash' => 'success',
                                                    'bank_transfer' => 'primary',
                                                    'check' => 'info',
                                                    'credit_card' => 'warning',
                                                    'online' => 'secondary'
                                                ];
                                                $methodColor = $methodColors[$payment->payment_method ?? 'secondary'] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $methodColor }}">
                                                {{ ucfirst(str_replace('_', ' ', $payment->payment_method ?? 'unknown')) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($payment->payment_date)
                                                {{ $payment->payment_date->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $payment->payment_date->format('H:i') }}</small>
                                            @else
                                                <span class="text-muted">No date</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'completed' => 'success',
                                                    'pending' => 'warning',
                                                    'failed' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $statusColor = $statusColors[$payment->status ?? 'pending'] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $statusColor }}">
                                                {{ ucfirst($payment->status ?? 'pending') }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('payments.show', $payment) }}" class="btn btn-outline-primary" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ route('payments.edit', $payment) }}" class="btn btn-outline-warning" title="Edit">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                @if($payment->status === 'pending')
                                                    <button type="button" class="btn btn-outline-success confirm-payment" 
                                                            data-payment-id="{{ $payment->id }}" title="Confirm Payment">
                                                        <i class="ri-check-line"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if(method_exists($payments, 'links'))
                        <div class="d-flex justify-content-center mt-4">
                            {{ $payments->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-5">
                        <i class="ri-money-dollar-circle-line fs-1 text-muted mb-3"></i>
                        <h5 class="mb-2">No Payments Found</h5>
                        <p class="text-muted mb-4">
                            Start by recording your first payment to track tenant payments.
                        </p>
                        <a href="{{ route('payments.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Record First Payment
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Confirm payment
    document.querySelectorAll('.confirm-payment').forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.dataset.paymentId;

            Swal.fire({
                title: 'Confirm Payment',
                text: 'Are you sure you want to confirm this payment?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Confirm',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/payments/${paymentId}/confirm`;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
