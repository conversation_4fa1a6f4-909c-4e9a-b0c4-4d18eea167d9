<?php

namespace App\Policies;

use App\Models\Task;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TaskPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All users can view tasks
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Task $task): bool
    {
        // Admin can view all tasks
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can view tasks for their properties
        if ($user->hasRole('property_owner') && $task->property_id) {
            return $user->ownedProperties->contains($task->property_id);
        }

        // Users can view tasks assigned to them or created by them
        return $task->assigned_to === $user->id || $task->created_by === $user->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'property_owner', 'property_manager']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Task $task): bool
    {
        // Admin can update all tasks
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can update tasks for their properties
        if ($user->hasRole('property_owner') && $task->property_id) {
            return $user->ownedProperties->contains($task->property_id);
        }

        // Task creator can update their tasks
        if ($task->created_by === $user->id) {
            return true;
        }

        // Assigned users can update task status and notes
        return $task->assigned_to === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Task $task): bool
    {
        // Admin can delete all tasks
        if ($user->hasRole('admin')) {
            return true;
        }

        // Property owners can delete tasks for their properties
        if ($user->hasRole('property_owner') && $task->property_id) {
            return $user->ownedProperties->contains($task->property_id);
        }

        // Task creator can delete their tasks
        return $task->created_by === $user->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Task $task): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Task $task): bool
    {
        return $user->hasRole('admin');
    }
}
