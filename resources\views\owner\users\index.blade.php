@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">My Employees</h5>
                <div>
                    <a href="{{ route('owner.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="ri-arrow-left-line me-1"></i>Back to Dashboard
                    </a>
                    <a href="{{ route('owner.users.create') }}" class="btn btn-primary">
                        <i class="ri-user-add-line me-1"></i>Add Employee
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Access Level Info -->
                <div class="alert alert-info border-0 mb-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ri-building-line fs-2 text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="alert-heading mb-1">Property Owner Employee Management</h6>
                            <p class="mb-0">You can manage your condominium staff. Create, edit, delete, activate, and deactivate accounts for tenants, receptionists, and maintenance staff.</p>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('owner.users.index') }}" class="d-flex">
                            <input type="text" name="search" class="form-control me-2" 
                                   placeholder="Search by name or email..." 
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="ri-search-line"></i>
                            </button>
                            @if(request('search') || request('role'))
                                <a href="{{ route('owner.users.index') }}" class="btn btn-outline-secondary ms-2">
                                    <i class="ri-close-line"></i>
                                </a>
                            @endif
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('owner.users.index') }}" class="d-flex justify-content-end">
                            <select name="role" class="form-select me-2" style="width: auto;" onchange="this.form.submit()">
                                <option value="">All Roles</option>
                                <option value="tenant" {{ request('role') == 'tenant' ? 'selected' : '' }}>Tenant</option>
                                <option value="receptionist" {{ request('role') == 'receptionist' ? 'selected' : '' }}>Receptionist</option>
                                <option value="maintenance_staff" {{ request('role') == 'maintenance_staff' ? 'selected' : '' }}>Maintenance Staff</option>
                            </select>
                            @if(request('search'))
                                <input type="hidden" name="search" value="{{ request('search') }}">
                            @endif
                        </form>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>User Type</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($users as $user)
                                <tr>
                                    <td>{{ $user->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2" style="width: 40px; height: 40px;">
                                                <img src="{{ $user->profile_photo_url }}"
                                                     alt="{{ $user->name }}"
                                                     class="rounded-circle border"
                                                     style="width: 100%; height: 100%; object-fit: cover;">
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->name }}</h6>
                                                <small class="text-muted">Employee</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>{{ $user->phone ?? 'Not provided' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</span>
                                    </td>
                                    <td>
                                        @foreach($user->roles as $role)
                                            <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        @if($user->is_active)
                                            <span class="badge bg-success">
                                                <i class="ri-check-line me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="ri-close-line me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                    <td>{{ $user->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('owner.users.show', $user) }}" class="btn btn-outline-info" title="View Details">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            
                                            <a href="{{ route('owner.users.edit', $user) }}" class="btn btn-outline-warning" title="Edit Employee">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            
                                            @if($user->is_active)
                                                <button type="button" class="btn btn-outline-secondary deactivate-user" 
                                                        title="Deactivate Employee"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->name }}"
                                                        data-url="{{ route('owner.users.deactivate', $user) }}">
                                                    <i class="ri-pause-circle-line"></i>
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-outline-success activate-user" 
                                                        title="Activate Employee"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->name }}"
                                                        data-url="{{ route('owner.users.activate', $user) }}">
                                                    <i class="ri-play-circle-line"></i>
                                                </button>
                                            @endif
                                            
                                            <button type="button" class="btn btn-outline-danger delete-user" 
                                                    title="Delete Employee"
                                                    data-user-id="{{ $user->id }}"
                                                    data-user-name="{{ $user->name }}"
                                                    data-url="{{ route('owner.users.destroy', $user) }}">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="ri-group-line fs-1 text-muted"></i>
                                        <h6 class="mt-2 text-muted">No Employees Found</h6>
                                        <p class="text-muted">You haven't created any employee accounts yet.</p>
                                        <a href="{{ route('owner.users.create') }}" class="btn btn-primary">
                                            <i class="ri-user-add-line me-1"></i>Add First Employee
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($users->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $users->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activate User
    document.querySelectorAll('.activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Activate Employee Account',
                html: `Are you sure you want to <strong>activate</strong> the account for:<br><br><strong>${userName}</strong>?<br><br>The employee will be able to login and access the system.`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-play-circle-line me-1"></i>Yes, Activate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
    
    // Deactivate User
    document.querySelectorAll('.deactivate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Deactivate Employee Account',
                html: `Are you sure you want to <strong>deactivate</strong> the account for:<br><br><strong>${userName}</strong>?<br><br>The employee will be logged out and unable to access the system until reactivated.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-pause-circle-line me-1"></i>Yes, Deactivate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
    
    // Delete User
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Delete Employee Account',
                html: `Are you sure you want to <strong>permanently delete</strong> the account for:<br><br><strong>${userName}</strong>?<br><br><span class="text-danger"><i class="ri-alert-line"></i> This action cannot be undone!</span>`,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
