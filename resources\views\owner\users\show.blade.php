@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Employee Details: {{ $user->name }}</h5>
                <div>
                    <a href="{{ route('owner.users.edit', $user) }}" class="btn btn-outline-warning me-2">
                        <i class="ri-edit-line me-1"></i>Edit Employee
                    </a>
                    <a href="{{ route('owner.users.index') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Employees
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Profile Photo Section -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <div class="profile-photo-display" style="width: 150px; height: 150px; margin: 0 auto;">
                            <img src="{{ $user->profile_photo_url }}"
                                 alt="{{ $user->name }}"
                                 class="rounded-circle border border-primary shadow"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <h4 class="mt-3 mb-1">{{ $user->name }}</h4>
                        <p class="text-muted">
                            @foreach($user->roles as $role)
                                {{ ucfirst(str_replace('_', ' ', $role->name)) }}
                            @endforeach
                        </p>
                        @if($user->hasProfilePhoto())
                            <small class="text-success">
                                <i class="ri-camera-line me-1"></i>Custom Profile Photo
                            </small>
                        @else
                            <small class="text-muted">
                                <i class="ri-user-line me-1"></i>Default Avatar
                            </small>
                        @endif
                    </div>
                </div>

                <!-- Employee Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-user-line text-primary me-2"></i>Personal Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ $user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $user->phone ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $user->address ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>City:</strong></td>
                                        <td>{{ $user->city ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>State:</strong></td>
                                        <td>{{ $user->state ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Zip Code:</strong></td>
                                        <td>{{ $user->zip_code ?? 'Not provided' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-shield-user-line text-success me-2"></i>Employment Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Employee Type:</strong></td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Role:</strong></td>
                                        <td>
                                            @foreach($user->roles as $role)
                                                <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                            @endforeach
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            @if($user->is_active)
                                                <span class="badge bg-success">
                                                    <i class="ri-check-line me-1"></i>Active
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="ri-close-line me-1"></i>Inactive
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Employer:</strong></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="ri-building-line text-white"></i>
                                                </div>
                                                <div>
                                                    {{ auth()->user()->name }}
                                                    <br><small class="text-muted">Property Owner</small>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Hired Date:</strong></td>
                                        <td>{{ $user->created_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $user->updated_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('owner.users.index') }}" class="btn btn-outline-secondary">
                                <i class="ri-arrow-left-line me-1"></i>Back to Employees
                            </a>
                            <a href="{{ route('owner.users.edit', $user) }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Employee
                            </a>
                            @if($user->is_active)
                                <button type="button" class="btn btn-outline-secondary deactivate-user" 
                                        data-user-name="{{ $user->name }}"
                                        data-url="{{ route('owner.users.deactivate', $user) }}">
                                    <i class="ri-pause-circle-line me-1"></i>Deactivate
                                </button>
                            @else
                                <button type="button" class="btn btn-success activate-user" 
                                        data-user-name="{{ $user->name }}"
                                        data-url="{{ route('owner.users.activate', $user) }}">
                                    <i class="ri-play-circle-line me-1"></i>Activate
                                </button>
                            @endif
                            
                            <button type="button" class="btn btn-danger delete-user" 
                                    data-user-name="{{ $user->name }}"
                                    data-url="{{ route('owner.users.destroy', $user) }}">
                                <i class="ri-delete-bin-line me-1"></i>Delete Employee
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activate User
    document.querySelectorAll('.activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Activate Employee Account',
                html: `Are you sure you want to <strong>activate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-play-circle-line me-1"></i>Yes, Activate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
    
    // Deactivate User
    document.querySelectorAll('.deactivate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Deactivate Employee Account',
                html: `Are you sure you want to <strong>deactivate</strong> the account for:<br><br><strong>${userName}</strong>?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-pause-circle-line me-1"></i>Yes, Deactivate',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('PATCH')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
    
    // Delete User
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            const userName = this.dataset.userName;
            const url = this.dataset.url;
            
            Swal.fire({
                title: 'Delete Employee Account',
                html: `Are you sure you want to <strong>permanently delete</strong> the account for:<br><br><strong>${userName}</strong>?<br><br><span class="text-danger"><i class="ri-alert-line"></i> This action cannot be undone!</span>`,
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
