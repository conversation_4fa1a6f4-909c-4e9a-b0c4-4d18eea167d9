<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Property Management
            'view_properties',
            'create_properties',
            'edit_properties',
            'delete_properties',
            'manage_own_properties',

            // Unit Management
            'view_units',
            'create_units',
            'edit_units',
            'delete_units',
            'view_own_unit',
            'manage_unit_occupancy',

            // User Management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'manage_tenants',

            // Complaint Management
            'view_complaints',
            'create_complaints',
            'edit_complaints',
            'delete_complaints',
            'resolve_complaints',
            'view_own_complaints',
            'assign_complaints',

            // Financial Management
            'view_financial_reports',
            'manage_rent',
            'view_bills',
            'view_own_bills',
            'generate_reports',

            // Visitor Management
            'view_visitors',
            'create_visitor_entries',
            'edit_visitor_entries',
            'view_visitor_history',
            'manage_checklist',

            // System Administration
            'manage_roles',
            'manage_permissions',
            'view_system_logs',
            'backup_system',
            'manage_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin Role - Full access
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());

        // Property Owner Role
        $ownerRole = Role::firstOrCreate(['name' => 'property_owner']);
        $ownerRole->syncPermissions([
            'manage_own_properties',
            'view_units',
            'create_units',
            'edit_units',
            'manage_unit_occupancy',
            'view_financial_reports',
            'manage_rent',
            'generate_reports',
            'view_complaints',
            'resolve_complaints',
            'assign_complaints',
            'manage_tenants',
        ]);

        // Tenant Role
        $tenantRole = Role::firstOrCreate(['name' => 'tenant']);
        $tenantRole->syncPermissions([
            'view_own_unit',
            'view_own_bills',
            'create_complaints',
            'view_own_complaints',
        ]);

        // Receptionist Role
        $receptionistRole = Role::firstOrCreate(['name' => 'receptionist']);
        $receptionistRole->syncPermissions([
            'view_visitors',
            'create_visitor_entries',
            'edit_visitor_entries',
            'view_visitor_history',
            'manage_checklist',
            'view_properties',
            'view_units',
            'view_users',
        ]);

        // Maintenance Staff Role
        $maintenanceRole = Role::firstOrCreate(['name' => 'maintenance_staff']);
        $maintenanceRole->syncPermissions([
            'view_complaints',
            'edit_complaints',
            'resolve_complaints',
            'view_properties',
            'view_units',
        ]);
    }
}
