@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Edit My Profile</h5>
                <div>
                    <a href="{{ route('profile.show') }}" class="btn btn-outline-info me-2">
                        <i class="ri-eye-line me-1"></i>View Profile
                    </a>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Profile Photo Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title">
                                    <i class="ri-camera-line text-primary me-2"></i>Profile Photo
                                </h6>
                                
                                <!-- Current Profile Photo Preview -->
                                <div class="mb-3">
                                    <div class="profile-photo-preview position-relative" style="width: 120px; height: 120px; margin: 0 auto;">
                                        <img id="photo-preview" 
                                             src="{{ $user->profile_photo_url }}" 
                                             alt="Profile Preview" 
                                             class="rounded-circle border border-primary" 
                                             style="width: 100%; height: 100%; object-fit: cover;">
                                        
                                        <!-- Photo Update Overlay -->
                                        <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center"
                                             style="background: rgba(0,123,255,0.8); opacity: 0; transition: opacity 0.3s; cursor: pointer;"
                                             onmouseover="this.style.opacity='1'" 
                                             onmouseout="this.style.opacity='0'"
                                             onclick="showProfilePhotoModal()">
                                            <i class="ri-camera-line text-white fs-4"></i>
                                        </div>
                                    </div>
                                    @if($user->hasProfilePhoto())
                                        <small class="text-success d-block mt-2">
                                            <i class="ri-check-line me-1"></i>Custom Profile Photo
                                        </small>
                                    @else
                                        <small class="text-muted d-block mt-2">
                                            <i class="ri-user-line me-1"></i>Default Avatar
                                        </small>
                                    @endif
                                </div>
                                
                                <!-- Quick Photo Update Button -->
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="showProfilePhotoModal()">
                                    <i class="ri-camera-line me-1"></i>Update Profile Photo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <form action="{{ route('profile.update') }}" method="POST">
                    @csrf
                    @method('PATCH')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required
                                       placeholder="Enter your full name">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required
                                       placeholder="Enter email address">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password"
                                       placeholder="Leave blank to keep current password">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave blank to keep the current password</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation"
                                       placeholder="Confirm new password">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}"
                                       placeholder="Enter phone number">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_type" class="form-label">User Type</label>
                                <input type="text" class="form-control" 
                                       value="{{ ucfirst(str_replace('_', ' ', $user->user_type)) }}" readonly>
                                <div class="form-text">
                                    <i class="ri-information-line"></i> User type cannot be changed. Contact administrator if needed.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="2"
                                          placeholder="Enter address">{{ old('address', $user->address) }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city', $user->city) }}"
                                       placeholder="Enter city">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                       id="state" name="state" value="{{ old('state', $user->state) }}"
                                       placeholder="Enter state">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label">Zip Code</label>
                                <input type="text" class="form-control @error('zip_code') is-invalid @enderror" 
                                       id="zip_code" name="zip_code" value="{{ old('zip_code', $user->zip_code) }}"
                                       placeholder="Enter zip code">
                                @error('zip_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Account Information (Read-only) -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="ri-information-line text-info me-2"></i>Account Information
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-2">
                                                <strong>Role:</strong> 
                                                @foreach($user->roles as $role)
                                                    <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $role->name)) }}</span>
                                                @endforeach
                                            </p>
                                            <p class="mb-2">
                                                <strong>Account Status:</strong> 
                                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-2">
                                                <strong>Member Since:</strong> {{ $user->created_at->format('M d, Y') }}
                                            </p>
                                            <p class="mb-0">
                                                <strong>Last Updated:</strong> {{ $user->updated_at->format('M d, Y \a\t H:i') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('profile.show') }}" class="btn btn-outline-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i>Update Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
