<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->string('unit_number');
            $table->string('unit_type'); // studio, 1br, 2br, 3br, etc.
            $table->decimal('area', 8, 2)->nullable(); // square feet
            $table->integer('bedrooms')->default(0);
            $table->integer('bathrooms')->default(1);
            $table->decimal('rent_amount', 10, 2);
            $table->decimal('security_deposit', 10, 2)->nullable();
            $table->enum('status', ['available', 'occupied', 'maintenance', 'reserved'])->default('available');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('set null');
            $table->date('lease_start')->nullable();
            $table->date('lease_end')->nullable();
            $table->text('description')->nullable();
            $table->json('features')->nullable(); // balcony, parking, etc.
            $table->string('lease_document')->nullable();
            $table->string('tenant_id_document')->nullable();
            $table->timestamps();

            $table->unique(['property_id', 'unit_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
