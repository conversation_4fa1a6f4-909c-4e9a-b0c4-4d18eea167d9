@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Role Details: {{ ucfirst(str_replace('_', ' ', $role->name)) }}</h5>
                <div>
                    <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-outline-warning me-2">
                        <i class="ri-edit-line me-1"></i>Edit Role
                    </a>
                    <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to Roles
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Role Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-shield-user-line text-primary me-2"></i>Role Information
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Role Name:</strong></td>
                                        <td>
                                            {{ ucfirst(str_replace('_', ' ', $role->name)) }}
                                            @if(in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']))
                                                <span class="badge bg-warning ms-2">Core Role</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>System Name:</strong></td>
                                        <td><code>{{ $role->name }}</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Permissions Count:</strong></td>
                                        <td><span class="badge bg-info">{{ $role->permissions->count() }} permissions</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Users Count:</strong></td>
                                        <td><span class="badge bg-success">{{ $role->users->count() }} users</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Created:</strong></td>
                                        <td>{{ $role->created_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $role->updated_at->format('M d, Y \a\t H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ri-group-line text-success me-2"></i>Users with this Role
                                </h6>
                                @if($role->users->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Email</th>
                                                    <th>Type</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($role->users->take(5) as $user)
                                                    <tr>
                                                        <td>{{ $user->name }}</td>
                                                        <td>{{ $user->email }}</td>
                                                        <td>
                                                            <span class="badge bg-secondary">{{ ucfirst($user->user_type) }}</span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                    @if($role->users->count() > 5)
                                        <p class="text-muted small mb-0">
                                            Showing 5 of {{ $role->users->count() }} users. 
                                            <a href="{{ route('admin.users.index') }}?role={{ $role->name }}">View all users</a>
                                        </p>
                                    @endif
                                @else
                                    <p class="text-muted mb-0">No users assigned to this role yet.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card border">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="ri-key-line text-warning me-2"></i>Assigned Permissions
                                </h6>
                            </div>
                            <div class="card-body">
                                @if($role->permissions->count() > 0)
                                    @php
                                        $permissionGroups = [
                                            'Property Management' => ['view_properties', 'create_properties', 'edit_properties', 'delete_properties', 'manage_own_properties'],
                                            'Unit Management' => ['view_units', 'create_units', 'edit_units', 'delete_units', 'view_own_unit', 'manage_unit_occupancy'],
                                            'User Management' => ['view_users', 'create_users', 'edit_users', 'delete_users', 'manage_tenants'],
                                            'Complaint Management' => ['view_complaints', 'create_complaints', 'edit_complaints', 'delete_complaints', 'resolve_complaints', 'view_own_complaints', 'assign_complaints'],
                                            'Financial Management' => ['view_financial_reports', 'manage_rent', 'view_bills', 'view_own_bills', 'generate_reports'],
                                            'Visitor Management' => ['view_visitors', 'create_visitor_entries', 'edit_visitor_entries', 'view_visitor_history', 'manage_checklist'],
                                            'System Administration' => ['manage_roles', 'manage_permissions', 'view_system_logs', 'backup_system', 'manage_settings']
                                        ];
                                        $rolePermissions = $role->permissions->pluck('name')->toArray();
                                    @endphp

                                    <div class="row">
                                        @foreach($permissionGroups as $groupName => $groupPermissions)
                                            @php
                                                $hasPermissionsInGroup = array_intersect($groupPermissions, $rolePermissions);
                                            @endphp
                                            @if(count($hasPermissionsInGroup) > 0)
                                                <div class="col-md-6 col-lg-4 mb-4">
                                                    <div class="card h-100 border-primary">
                                                        <div class="card-header bg-primary-lighten">
                                                            <h6 class="card-title mb-0 text-primary">{{ $groupName }}</h6>
                                                        </div>
                                                        <div class="card-body py-2">
                                                            @foreach($groupPermissions as $permissionName)
                                                                @if(in_array($permissionName, $rolePermissions))
                                                                    <div class="d-flex align-items-center mb-2">
                                                                        <i class="ri-check-line text-success me-2"></i>
                                                                        <span class="small">{{ ucfirst(str_replace('_', ' ', $permissionName)) }}</span>
                                                                    </div>
                                                                @endif
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="ri-key-line fs-1 text-muted"></i>
                                        <p class="text-muted mt-2">No permissions assigned to this role.</p>
                                        <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-outline-primary">
                                            <i class="ri-add-line me-1"></i>Assign Permissions
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                                <i class="ri-arrow-left-line me-1"></i>Back to Roles
                            </a>
                            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-warning">
                                <i class="ri-edit-line me-1"></i>Edit Role
                            </a>
                            @if(!in_array($role->name, ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff']))
                                <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="ri-delete-bin-line me-1"></i>Delete Role
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
