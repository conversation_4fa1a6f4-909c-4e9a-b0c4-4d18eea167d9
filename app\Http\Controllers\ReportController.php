<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use App\Models\Bill;
use App\Models\Payment;
use App\Models\Task;
use App\Models\Complaint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Property Reports Dashboard
     */
    public function propertyReports(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Get properties based on user role
        if (in_array('admin', $userRoles)) {
            $properties = Property::with(['units', 'bills', 'payments'])->get();
        } elseif (in_array('property_owner', $userRoles)) {
            $properties = $user->ownedProperties()->with(['units', 'bills', 'payments'])->get();
        } else {
            $properties = collect();
        }

        // Calculate occupancy statistics
        $occupancyStats = $this->calculateOccupancyStats($properties);
        
        // Calculate financial statistics
        $financialStats = $this->calculateFinancialStats($properties);
        
        // Calculate maintenance statistics
        $maintenanceStats = $this->calculateMaintenanceStats($properties);

        return view('reports.property', compact(
            'properties', 
            'occupancyStats', 
            'financialStats', 
            'maintenanceStats'
        ));
    }

    /**
     * Financial Reports Dashboard
     */
    public function financialReports(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Date range filter
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now()->endOfMonth();

        // Get financial data based on user role
        if (in_array('admin', $userRoles)) {
            $incomeData = $this->getIncomeData($startDate, $endDate);
            $expenseData = $this->getExpenseData($startDate, $endDate);
        } elseif (in_array('property_owner', $userRoles)) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $incomeData = $this->getIncomeData($startDate, $endDate, $propertyIds);
            $expenseData = $this->getExpenseData($startDate, $endDate, $propertyIds);
        } else {
            $incomeData = collect();
            $expenseData = collect();
        }

        // Calculate profit/loss
        $profitLoss = $this->calculateProfitLoss($incomeData, $expenseData);

        return view('reports.financial', compact(
            'incomeData',
            'expenseData', 
            'profitLoss',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Calculate occupancy statistics
     */
    private function calculateOccupancyStats($properties)
    {
        $totalUnits = 0;
        $occupiedUnits = 0;
        $availableUnits = 0;
        $maintenanceUnits = 0;

        foreach ($properties as $property) {
            $units = $property->units;
            $totalUnits += $units->count();
            $occupiedUnits += $units->where('status', 'occupied')->count();
            $availableUnits += $units->where('status', 'available')->count();
            $maintenanceUnits += $units->where('status', 'maintenance')->count();
        }

        $occupancyRate = $totalUnits > 0 ? ($occupiedUnits / $totalUnits) * 100 : 0;

        return [
            'total_units' => $totalUnits,
            'occupied_units' => $occupiedUnits,
            'available_units' => $availableUnits,
            'maintenance_units' => $maintenanceUnits,
            'occupancy_rate' => round($occupancyRate, 2)
        ];
    }

    /**
     * Calculate financial statistics
     */
    private function calculateFinancialStats($properties)
    {
        $totalRent = 0;
        $collectedRent = 0;
        $pendingRent = 0;
        $overdueRent = 0;

        foreach ($properties as $property) {
            // Calculate from units
            foreach ($property->units as $unit) {
                if ($unit->status === 'occupied' && $unit->rent_amount) {
                    $totalRent += $unit->rent_amount;
                }
            }

            // Calculate from bills
            $bills = $property->bills()->whereIn('status', ['pending', 'paid', 'overdue'])->get();
            foreach ($bills as $bill) {
                if ($bill->status === 'paid') {
                    $collectedRent += $bill->amount;
                } elseif ($bill->status === 'pending') {
                    $pendingRent += $bill->amount;
                } elseif ($bill->status === 'overdue') {
                    $overdueRent += $bill->amount;
                }
            }
        }

        $collectionRate = $totalRent > 0 ? ($collectedRent / ($collectedRent + $pendingRent + $overdueRent)) * 100 : 0;

        return [
            'total_rent' => $totalRent,
            'collected_rent' => $collectedRent,
            'pending_rent' => $pendingRent,
            'overdue_rent' => $overdueRent,
            'collection_rate' => round($collectionRate, 2)
        ];
    }

    /**
     * Calculate maintenance statistics
     */
    private function calculateMaintenanceStats($properties)
    {
        $totalTasks = 0;
        $completedTasks = 0;
        $pendingTasks = 0;
        $overdueTasks = 0;
        $totalComplaints = 0;
        $resolvedComplaints = 0;

        foreach ($properties as $property) {
            // Tasks statistics
            $tasks = Task::where('property_id', $property->id)->get();
            $totalTasks += $tasks->count();
            $completedTasks += $tasks->where('status', 'completed')->count();
            $pendingTasks += $tasks->where('status', 'pending')->count();
            $overdueTasks += $tasks->where('due_date', '<', now())->where('status', '!=', 'completed')->count();

            // Complaints statistics
            $complaints = Complaint::where('property_id', $property->id)->get();
            $totalComplaints += $complaints->count();
            $resolvedComplaints += $complaints->whereIn('status', ['resolved', 'closed'])->count();
        }

        $completionRate = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;
        $resolutionRate = $totalComplaints > 0 ? ($resolvedComplaints / $totalComplaints) * 100 : 0;

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'pending_tasks' => $pendingTasks,
            'overdue_tasks' => $overdueTasks,
            'completion_rate' => round($completionRate, 2),
            'total_complaints' => $totalComplaints,
            'resolved_complaints' => $resolvedComplaints,
            'resolution_rate' => round($resolutionRate, 2)
        ];
    }

    /**
     * Get income data for financial reports
     */
    private function getIncomeData($startDate, $endDate, $propertyIds = null)
    {
        $query = Payment::whereBetween('payment_date', [$startDate, $endDate])
                       ->where('status', 'completed');

        if ($propertyIds) {
            $query->whereHas('bill', function($q) use ($propertyIds) {
                $q->whereIn('property_id', $propertyIds);
            });
        }

        return $query->with(['bill', 'tenant'])->get();
    }

    /**
     * Get expense data for financial reports
     */
    private function getExpenseData($startDate, $endDate, $propertyIds = null)
    {
        // For now, we'll return empty collection since tasks don't have cost column
        // In a real application, you might have a separate expenses table
        return collect();
    }

    /**
     * Calculate profit/loss
     */
    private function calculateProfitLoss($incomeData, $expenseData)
    {
        $totalIncome = $incomeData->sum('amount');
        $totalExpenses = 0; // Since we don't have expense data yet
        $netProfit = $totalIncome - $totalExpenses;
        $profitMargin = $totalIncome > 0 ? ($netProfit / $totalIncome) * 100 : 0;

        return [
            'total_income' => $totalIncome,
            'total_expenses' => $totalExpenses,
            'net_profit' => $netProfit,
            'profit_margin' => round($profitMargin, 2)
        ];
    }

    /**
     * Export property report to PDF
     */
    public function exportPropertyReport(Request $request)
    {
        // Implementation for PDF export
        // You would use a library like DomPDF or similar
        return response()->json(['message' => 'PDF export feature coming soon']);
    }

    /**
     * Export financial report to Excel
     */
    public function exportFinancialReport(Request $request)
    {
        // Implementation for Excel export
        // You would use a library like Laravel Excel
        return response()->json(['message' => 'Excel export feature coming soon']);
    }
}
