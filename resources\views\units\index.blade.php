@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <!-- Header Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="ri-home-line text-primary me-2"></i>Unit Management
                        </h4>
                        <p class="text-muted mb-0">
                            Manage units, track occupancy, and assign tenants
                        </p>
                    </div>
                    <div>
                        @can('create', App\Models\Unit::class)
                            <a href="{{ route('units.create') }}" class="btn btn-primary">
                                <i class="ri-add-line me-1"></i>Add New Unit
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('units.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Unit number, tenant name...">
                    </div>
                    <div class="col-md-2">
                        <label for="property_id" class="form-label">Property</label>
                        <select class="form-select" id="property_id" name="property_id">
                            <option value="">All Properties</option>
                            @foreach($properties as $property)
                                <option value="{{ $property->id }}" {{ request('property_id') == $property->id ? 'selected' : '' }}>
                                    {{ $property->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="available" {{ request('status') == 'available' ? 'selected' : '' }}>Available</option>
                            <option value="occupied" {{ request('status') == 'occupied' ? 'selected' : '' }}>Occupied</option>
                            <option value="maintenance" {{ request('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                            <option value="reserved" {{ request('status') == 'reserved' ? 'selected' : '' }}>Reserved</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="unit_type" class="form-label">Type</label>
                        <select class="form-select" id="unit_type" name="unit_type">
                            <option value="">All Types</option>
                            @foreach($unitTypes as $type)
                                <option value="{{ $type }}" {{ request('unit_type') == $type ? 'selected' : '' }}>
                                    {{ ucfirst($type) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="ri-search-line me-1"></i>Filter
                        </button>
                        <a href="{{ route('units.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i>Reset
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Units Grid -->
        @if($units->count() > 0)
            <div class="row">
                @foreach($units as $unit)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 unit-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="ri-home-line text-primary me-1"></i>
                                    Unit {{ $unit->unit_number }}
                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="ri-more-line"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ route('units.show', $unit) }}">
                                                <i class="ri-eye-line me-1"></i>View Details
                                            </a>
                                        </li>
                                        @can('update', $unit)
                                            <li>
                                                <a class="dropdown-item" href="{{ route('units.edit', $unit) }}">
                                                    <i class="ri-edit-line me-1"></i>Edit Unit
                                                </a>
                                            </li>
                                        @endcan
                                        @can('manageOccupancy', $unit)
                                            @if($unit->tenant_id)
                                                <li>
                                                    <button class="dropdown-item text-warning remove-tenant" 
                                                            data-unit-id="{{ $unit->id }}"
                                                            data-unit-number="{{ $unit->unit_number }}">
                                                        <i class="ri-user-unfollow-line me-1"></i>Remove Tenant
                                                    </button>
                                                </li>
                                            @else
                                                <li>
                                                    <a class="dropdown-item text-success" href="{{ route('units.show', $unit) }}#assign-tenant">
                                                        <i class="ri-user-add-line me-1"></i>Assign Tenant
                                                    </a>
                                                </li>
                                            @endif
                                        @endcan
                                        @can('delete', $unit)
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-unit" 
                                                        data-unit-id="{{ $unit->id }}"
                                                        data-unit-number="{{ $unit->unit_number }}">
                                                    <i class="ri-delete-bin-line me-1"></i>Delete
                                                </button>
                                            </li>
                                        @endcan
                                    </ul>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Unit Info -->
                                <div class="mb-3">
                                    <p class="text-muted mb-1">
                                        <i class="ri-building-line me-1"></i>
                                        {{ $unit->property->name }}
                                    </p>
                                    <p class="text-muted mb-1">
                                        <i class="ri-home-gear-line me-1"></i>
                                        {{ ucfirst($unit->unit_type) }} • {{ $unit->bedrooms }}BR/{{ $unit->bathrooms }}BA
                                    </p>
                                    @if($unit->area)
                                        <p class="text-muted mb-1">
                                            <i class="ri-ruler-line me-1"></i>
                                            {{ number_format($unit->area, 0) }} sq ft
                                        </p>
                                    @endif
                                    <span class="badge bg-{{ $unit->status == 'available' ? 'success' : ($unit->status == 'occupied' ? 'primary' : ($unit->status == 'maintenance' ? 'warning' : 'secondary')) }}">
                                        {{ ucfirst($unit->status) }}
                                    </span>
                                </div>

                                <!-- Tenant Info -->
                                @if($unit->tenant)
                                    <div class="mb-3 p-2 bg-light rounded">
                                        <h6 class="mb-1">
                                            <i class="ri-user-line text-primary me-1"></i>Current Tenant
                                        </h6>
                                        <p class="mb-1">{{ $unit->tenant->name }}</p>
                                        @if($unit->lease_end)
                                            <small class="text-muted">
                                                Lease expires: {{ $unit->lease_end->format('M d, Y') }}
                                                @if($unit->lease_end->isPast())
                                                    <span class="badge bg-danger ms-1">Expired</span>
                                                @elseif($unit->lease_end->diffInDays() <= 30)
                                                    <span class="badge bg-warning ms-1">Expiring Soon</span>
                                                @endif
                                            </small>
                                        @endif
                                    </div>
                                @endif

                                <!-- Financial Info -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="text-muted">Monthly Rent:</span>
                                    <span class="fw-bold text-success">${{ number_format($unit->rent_amount, 0) }}</span>
                                </div>

                                @if($unit->security_deposit)
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="text-muted">Security Deposit:</span>
                                        <span class="fw-bold">${{ number_format($unit->security_deposit, 0) }}</span>
                                    </div>
                                @endif
                            </div>
                            <div class="card-footer">
                                <div class="d-flex gap-2">
                                    <a href="{{ route('units.show', $unit) }}" class="btn btn-primary btn-sm flex-fill">
                                        <i class="ri-eye-line me-1"></i>View Details
                                    </a>
                                    @can('update', $unit)
                                        <a href="{{ route('units.edit', $unit) }}" class="btn btn-outline-warning btn-sm">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $units->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-home-line fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No Units Found</h5>
                    <p class="text-muted mb-4">
                        @if(request()->hasAny(['search', 'property_id', 'status', 'unit_type']))
                            No units match your current filters. Try adjusting your search criteria.
                        @else
                            You haven't added any units yet. Start by adding your first unit.
                        @endif
                    </p>
                    @can('create', App\Models\Unit::class)
                        <a href="{{ route('units.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Your First Unit
                        </a>
                    @endcan
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete unit functionality
    document.querySelectorAll('.delete-unit').forEach(button => {
        button.addEventListener('click', function() {
            const unitId = this.dataset.unitId;
            const unitNumber = this.dataset.unitNumber;

            Swal.fire({
                title: 'Delete Unit',
                html: `Are you sure you want to delete:<br><br><strong>Unit ${unitNumber}</strong>?<br><br><small class="text-danger">This action cannot be undone and will delete all associated data.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-delete-bin-line me-1"></i>Yes, Delete',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/units/${unitId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Remove tenant functionality
    document.querySelectorAll('.remove-tenant').forEach(button => {
        button.addEventListener('click', function() {
            const unitId = this.dataset.unitId;
            const unitNumber = this.dataset.unitNumber;

            Swal.fire({
                title: 'Remove Tenant',
                html: `Are you sure you want to remove the tenant from:<br><br><strong>Unit ${unitNumber}</strong>?<br><br><small class="text-warning">This will set the unit status to available.</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="ri-user-unfollow-line me-1"></i>Yes, Remove',
                cancelButtonText: '<i class="ri-close-line me-1"></i>Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/units/${unitId}/remove-tenant`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>
@endsection
