<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $roles = Role::with('permissions')->paginate(10);
        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissions = Permission::all();
        return view('admin.roles.create', compact('permissions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $role = Role::create(['name' => $request->name]);

        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        }

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        $role->load('permissions', 'users');
        return view('admin.roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all();
        return view('admin.roles.edit', compact('role', 'permissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role)
    {
        // Prevent updating core system roles' names
        $coreRoles = ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff'];

        $nameValidation = 'required|string|max:255|unique:roles,name,' . $role->id;

        // If it's a core role, don't validate name uniqueness since we won't update it
        if (in_array($role->name, $coreRoles)) {
            $nameValidation = 'required|string|max:255';
        }

        $request->validate([
            'name' => $nameValidation,
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        // Only update name if it's not a core role
        if (!in_array($role->name, $coreRoles)) {
            $role->update(['name' => $request->name]);
        }

        // Always allow permission updates
        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        } else {
            $role->syncPermissions([]);
        }

        $message = in_array($role->name, $coreRoles)
            ? 'Role permissions updated successfully. (Core role name cannot be changed)'
            : 'Role updated successfully.';

        return redirect()->route('admin.roles.index')
            ->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        // Prevent deletion of core roles
        $coreRoles = ['admin', 'property_owner', 'tenant', 'receptionist', 'maintenance_staff'];

        if (in_array($role->name, $coreRoles)) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete core system roles.');
        }

        // Check if role has users assigned
        if ($role->users()->count() > 0) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete role that has users assigned. Please reassign users first.');
        }

        $roleName = $role->name;
        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', "Role '{$roleName}' deleted successfully.");
    }
}
