@extends('layouts.base')

@section('content')
<div class="row gx-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Create New User</h5>
                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Back to Users
                </a>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.users.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <!-- Profile Photo Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title">
                                        <i class="ri-camera-line text-primary me-2"></i>Profile Photo
                                    </h6>

                                    <!-- Profile Photo Preview -->
                                    <div class="mb-3">
                                        <div class="profile-photo-preview" style="width: 120px; height: 120px; margin: 0 auto;">
                                            <img id="photo-preview"
                                                 src="https://ui-avatars.com/api/?name=User&background=007bff&color=ffffff&size=120&font-size=0.6&bold=true"
                                                 alt="Profile Preview"
                                                 class="rounded-circle border border-primary"
                                                 style="width: 100%; height: 100%; object-fit: cover;">
                                        </div>
                                    </div>

                                    <!-- File Input -->
                                    <div class="mb-3">
                                        <input type="file" class="form-control @error('profile_photo') is-invalid @enderror"
                                               id="profile_photo" name="profile_photo" accept="image/*"
                                               onchange="previewProfilePhoto(this)">
                                        @error('profile_photo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">
                                            <i class="ri-information-line"></i> Upload a profile photo (JPG, PNG, GIF - Max: 2MB). Leave empty for default avatar.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required
                                       onchange="updatePhotoPreview()">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror"
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_type" class="form-label">User Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('user_type') is-invalid @enderror"
                                        id="user_type" name="user_type" required>
                                    <option value="">Select User Type</option>
                                    @foreach($allowedUserTypes as $userType)
                                        <option value="{{ $userType }}" {{ old('user_type') == $userType ? 'selected' : '' }}>
                                            {{ ucfirst(str_replace('_', ' ', $userType)) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('user_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    @if(auth()->user()->hasRole('admin'))
                                        <i class="ri-shield-check-line text-success"></i> <strong>Admin Access:</strong> You can create all user types including other admins and property owners.
                                    @elseif(auth()->user()->hasRole('property_owner'))
                                        <i class="ri-building-line text-primary"></i> <strong>Property Owner Access:</strong> You can create staff for your condominium (tenants, receptionists, and maintenance staff).
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select @error('role') is-invalid @enderror" 
                                        id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->name }}" {{ old('role') == $role->name ? 'selected' : '' }}>
                                            {{ ucfirst(str_replace('_', ' ', $role->name)) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('role')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    @if(auth()->user()->hasRole('admin'))
                                        <i class="ri-shield-check-line text-success"></i> <strong>Admin Access:</strong> You can assign all roles including admin and property owner roles.
                                    @elseif(auth()->user()->hasRole('property_owner'))
                                        <i class="ri-building-line text-primary"></i> <strong>Property Owner Access:</strong> You can assign staff roles for your condominium management.
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Account Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="ri-information-line"></i> Uncheck to create an inactive account. User won't be able to login until activated.
                                </div>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="2">{{ old('address') }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city') }}">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                       id="state" name="state" value="{{ old('state') }}">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control @error('zip_code') is-invalid @enderror" 
                                       id="zip_code" name="zip_code" value="{{ old('zip_code') }}">
                                @error('zip_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// Profile photo preview function
function previewProfilePhoto(input) {
    const preview = document.getElementById('photo-preview');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
        };

        reader.readAsDataURL(input.files[0]);
    }
}

// Update photo preview based on name
function updatePhotoPreview() {
    const nameInput = document.getElementById('name');
    const photoInput = document.getElementById('profile_photo');
    const preview = document.getElementById('photo-preview');

    // Only update if no custom photo is selected
    if (!photoInput.files || !photoInput.files[0]) {
        const name = nameInput.value || 'User';
        const encodedName = encodeURIComponent(name);
        preview.src = `https://ui-avatars.com/api/?name=${encodedName}&background=007bff&color=ffffff&size=120&font-size=0.6&bold=true`;
    }
}
</script>
@endsection
