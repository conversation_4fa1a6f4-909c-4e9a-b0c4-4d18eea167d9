<?php

namespace App\Http\Controllers;

use App\Models\Bill;
use App\Models\Property;
use App\Models\Unit;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BillController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Build query based on user role
        if ($user->hasRole('admin')) {
            $query = Bill::with(['tenant', 'unit', 'property', 'payments']);
        } elseif ($user->hasRole('property_owner')) {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Bill::whereIn('property_id', $propertyIds)->with(['tenant', 'unit', 'property', 'payments']);
        } else {
            // Tenants see only their bills
            $query = Bill::where('tenant_id', $user->id)->with(['unit', 'property', 'payments']);
        }

        // Apply filters
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('bill_type')) {
            $query->where('bill_type', $request->bill_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('bill_number', 'like', "%{$search}%")
                  ->orWhereHas('tenant', function ($tq) use ($search) {
                      $tq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $bills = $query->latest()->paginate(15);

        // Calculate totals
        $totals = [
            'total_amount' => $query->sum('total_amount'),
            'paid_amount' => $query->where('status', 'paid')->sum('total_amount'),
            'pending_amount' => $query->where('status', 'unpaid')->sum('total_amount'),
            'overdue_amount' => $query->where('status', 'overdue')->sum('total_amount'),
        ];

        // Get filter options
        if ($user->hasRole('admin')) {
            $properties = Property::all();
        } elseif ($user->hasRole('property_owner')) {
            $properties = $user->ownedProperties;
        } else {
            $properties = collect();
        }

        return view('bills.index', compact('bills', 'properties', 'totals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $this->authorize('create', Bill::class);
        
        $user = Auth::user();
        
        // Get properties and units based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units.tenant')->get();
        } else {
            $properties = $user->ownedProperties()->with('units.tenant')->get();
        }

        $selectedProperty = $request->property_id ? Property::find($request->property_id) : null;
        $selectedUnit = $request->unit_id ? Unit::find($request->unit_id) : null;

        return view('bills.create', compact('properties', 'selectedProperty', 'selectedUnit'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Bill::class);

        $validated = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'unit_id' => 'required|exists:units,id',
            'property_id' => 'required|exists:properties,id',
            'bill_type' => 'required|in:rent,utilities,maintenance,parking,other',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'late_fee' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        // Generate unique bill number
        $validated['bill_number'] = 'BILL-' . date('Y') . '-' . str_pad(Bill::count() + 1, 6, '0', STR_PAD_LEFT);
        $validated['issued_date'] = now();
        $validated['created_by'] = Auth::id();
        $validated['status'] = 'unpaid';

        // Calculate total amount
        $validated['total_amount'] = $validated['amount'] + ($validated['late_fee'] ?? 0) - ($validated['discount'] ?? 0);

        $bill = Bill::create($validated);

        return redirect()->route('bills.show', $bill)
            ->with('success', 'Bill created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Bill $bill)
    {
        $this->authorize('view', $bill);

        $bill->load(['tenant', 'unit', 'property', 'payments.processedBy', 'createdBy']);

        // Calculate payment statistics
        $paymentStats = [
            'total_paid' => $bill->payments->where('status', 'completed')->sum('amount'),
            'pending_payments' => $bill->payments->where('status', 'pending')->sum('amount'),
            'remaining_amount' => $bill->total_amount - $bill->payments->where('status', 'completed')->sum('amount'),
        ];

        return view('bills.show', compact('bill', 'paymentStats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Bill $bill)
    {
        $this->authorize('update', $bill);
        
        $user = Auth::user();
        
        // Get properties and units based on user role
        if ($user->hasRole('admin')) {
            $properties = Property::with('units.tenant')->get();
        } else {
            $properties = $user->ownedProperties()->with('units.tenant')->get();
        }

        return view('bills.edit', compact('bill', 'properties'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Bill $bill)
    {
        $this->authorize('update', $bill);

        $validated = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'unit_id' => 'required|exists:units,id',
            'property_id' => 'required|exists:properties,id',
            'bill_type' => 'required|in:rent,utilities,maintenance,parking,other',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'due_date' => 'required|date',
            'late_fee' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'status' => 'required|in:unpaid,paid,overdue,cancelled',
            'notes' => 'nullable|string',
        ]);

        // Calculate total amount
        $validated['total_amount'] = $validated['amount'] + ($validated['late_fee'] ?? 0) - ($validated['discount'] ?? 0);

        $bill->update($validated);

        return redirect()->route('bills.show', $bill)
            ->with('success', 'Bill updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Bill $bill)
    {
        $this->authorize('delete', $bill);

        $bill->delete();

        return redirect()->route('bills.index')
            ->with('success', 'Bill deleted successfully!');
    }

    /**
     * Generate automatic bills for rent
     */
    public function generateRentBills(Request $request)
    {
        $this->authorize('create', Bill::class);

        $validated = $request->validate([
            'property_id' => 'nullable|exists:properties,id',
            'due_date' => 'required|date|after:today',
        ]);

        $user = Auth::user();
        
        // Get occupied units
        if ($user->hasRole('admin')) {
            $query = Unit::where('status', 'occupied')->whereNotNull('tenant_id');
        } else {
            $propertyIds = $user->ownedProperties->pluck('id');
            $query = Unit::whereIn('property_id', $propertyIds)->where('status', 'occupied')->whereNotNull('tenant_id');
        }

        if ($validated['property_id']) {
            $query->where('property_id', $validated['property_id']);
        }

        $units = $query->with(['tenant', 'property'])->get();

        $billsCreated = 0;

        foreach ($units as $unit) {
            // Check if bill already exists for this month
            $existingBill = Bill::where('tenant_id', $unit->tenant_id)
                ->where('unit_id', $unit->id)
                ->where('bill_type', 'rent')
                ->whereMonth('due_date', date('m', strtotime($validated['due_date'])))
                ->whereYear('due_date', date('Y', strtotime($validated['due_date'])))
                ->first();

            if (!$existingBill) {
                Bill::create([
                    'bill_number' => 'BILL-' . date('Y') . '-' . str_pad(Bill::count() + 1, 6, '0', STR_PAD_LEFT),
                    'tenant_id' => $unit->tenant_id,
                    'unit_id' => $unit->id,
                    'property_id' => $unit->property_id,
                    'bill_type' => 'rent',
                    'description' => 'Monthly rent for ' . $unit->unit_number,
                    'amount' => $unit->rent_amount,
                    'due_date' => $validated['due_date'],
                    'issued_date' => now(),
                    'status' => 'unpaid',
                    'total_amount' => $unit->rent_amount,
                    'created_by' => Auth::id(),
                ]);

                $billsCreated++;
            }
        }

        return redirect()->route('bills.index')
            ->with('success', "Generated {$billsCreated} rent bills successfully!");
    }

    /**
     * Mark bill as paid
     */
    public function markAsPaid(Bill $bill)
    {
        $this->authorize('update', $bill);

        $bill->update(['status' => 'paid']);

        return redirect()->route('bills.show', $bill)
            ->with('success', 'Bill marked as paid successfully!');
    }

    /**
     * Send payment reminder
     */
    public function sendReminder(Bill $bill)
    {
        $this->authorize('view', $bill);

        // Here you would implement email/SMS reminder logic
        // For now, we'll just show a success message

        return redirect()->route('bills.show', $bill)
            ->with('success', 'Payment reminder sent successfully!');
    }
}
